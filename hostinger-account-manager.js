const { NodeSSH } = require('node-ssh');
const chokidar = require('chokidar');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const HostingerDatabaseManager = require('./database-manager');

class HostingerAccountManager {
    constructor() {
        this.config = this.loadConfig();
        this.ssh = new NodeSSH();
        this.isConnected = false;
        this.watchers = new Map();
        this.uploadQueues = new Map();
        this.isMonitoring = false;
        this.knownDomains = new Set();
        this.activeUploads = 0;
        this.databaseManager = new HostingerDatabaseManager(this.config);
    }

    loadConfig() {
        try {
            return JSON.parse(fs.readFileSync('sync-config.json', 'utf8'));
        } catch (error) {
            console.error(chalk.red('Error loading config:'), error.message);
            process.exit(1);
        }
    }

    saveConfig() {
        try {
            fs.writeFileSync('sync-config.json', JSON.stringify(this.config, null, 2));
        } catch (error) {
            console.error(chalk.red('Error saving config:'), error.message);
        }
    }

    async connect() {
        if (this.isConnected) return;

        let retryCount = 0;
        const maxRetries = 5;

        while (retryCount < maxRetries) {
            try {
                console.log(chalk.blue(`🔗 Connecting to Hostinger... (attempt ${retryCount + 1})`));

                await this.ssh.connect({
                    host: this.config.hostinger.host,
                    username: this.config.hostinger.username,
                    port: this.config.hostinger.port,
                    password: this.config.hostinger.password,
                    keepaliveInterval: 30000,
                    keepaliveCountMax: 3
                });

                this.isConnected = true;
                console.log(chalk.green('✅ Connected to Hostinger successfully!'));
                return;

            } catch (error) {
                retryCount++;
                this.isConnected = false;

                if (retryCount >= maxRetries) {
                    console.error(chalk.red('❌ Connection failed after all retries:'), error.message);
                    throw error;
                }

                console.log(chalk.yellow(`⚠️  Connection failed, retrying in 5 seconds... (${retryCount}/${maxRetries})`));
                await new Promise(resolve => setTimeout(resolve, 5000));
            }
        }
    }

    async ensureConnection() {
        if (!this.isConnected) {
            await this.connect();
        }

        // Test connection
        try {
            await this.ssh.execCommand('echo "test"');
        } catch (error) {
            console.log(chalk.yellow('🔄 Connection lost, reconnecting...'));
            this.isConnected = false;
            await this.connect();
        }
    }

    async scanForNewDomains() {
        await this.connect();
        
        try {
            console.log(chalk.cyan('🔍 Scanning for new domains...'));
            
            const domainsPath = `/home/<USER>/domains`;
            const result = await this.ssh.execCommand(`ls -la ${domainsPath}`);
            
            const currentDomains = new Set();
            
            if (result.stdout) {
                const lines = result.stdout.split('\n');
                for (const line of lines) {
                    const parts = line.trim().split(/\s+/);
                    if (parts.length > 8 && parts[0].startsWith('d')) {
                        const dirName = parts[parts.length - 1];
                        if (dirName && dirName !== '.' && dirName !== '..') {
                            currentDomains.add(dirName);
                        }
                    }
                }
            }
            
            // Check for new domains
            const newDomains = [];
            for (const domain of currentDomains) {
                if (!this.config.domains[domain]) {
                    newDomains.push(domain);
                }
            }
            
            // Check for removed domains
            const removedDomains = [];
            for (const domain of Object.keys(this.config.domains)) {
                if (!currentDomains.has(domain)) {
                    removedDomains.push(domain);
                }
            }
            
            if (newDomains.length > 0) {
                console.log(chalk.green(`🆕 Found ${newDomains.length} new domains:`));
                for (const domain of newDomains) {
                    console.log(chalk.cyan(`   + ${domain}`));
                    await this.addNewDomain(domain);
                }
            }
            
            if (removedDomains.length > 0) {
                console.log(chalk.yellow(`🗑️  Removed ${removedDomains.length} domains:`));
                for (const domain of removedDomains) {
                    console.log(chalk.yellow(`   - ${domain}`));
                    this.removeDomain(domain);
                }
            }
            
            if (newDomains.length === 0 && removedDomains.length === 0) {
                console.log(chalk.gray('✅ No changes detected'));
            }
            
            return { newDomains, removedDomains };
            
        } catch (error) {
            console.error(chalk.red('Error scanning domains:'), error.message);
            return { newDomains: [], removedDomains: [] };
        }
    }

    async addNewDomain(domainName) {
        const domainsPath = `/home/<USER>/domains`;
        const localPath = path.join(this.config.hostinger.localBasePath, domainName);
        
        // Add to config
        this.config.domains[domainName] = {
            remotePath: `${domainsPath}/${domainName}`,
            localPath: localPath,
            enabled: true,
            lastSync: null
        };
        
        this.saveConfig();
        
        // Create local directory
        await fs.ensureDir(localPath);
        console.log(chalk.green(`📁 Created local directory: ${localPath}`));
        
        // Download the domain
        await this.downloadDomain(domainName);
        
        // Start watching it
        this.startWatchingDomain(domainName);
    }

    removeDomain(domainName) {
        // Stop watching
        if (this.watchers.has(domainName)) {
            this.watchers.get(domainName).close();
            this.watchers.delete(domainName);
        }
        
        // Remove from config
        delete this.config.domains[domainName];
        this.saveConfig();
        
        console.log(chalk.yellow(`🗑️  Removed ${domainName} from management`));
    }

    async downloadDomain(domainName) {
        const domainConfig = this.config.domains[domainName];
        if (!domainConfig) return;

        await this.connect();
        
        try {
            console.log(chalk.blue(`⬇️  Downloading ${domainName}...`));
            
            // Check if remote exists
            const checkResult = await this.ssh.execCommand(`test -d "${domainConfig.remotePath}" && echo "exists"`);
            if (!checkResult.stdout.includes('exists')) {
                console.log(chalk.yellow(`⚠️  Remote path does not exist: ${domainConfig.remotePath}`));
                return;
            }
            
            // Download with progress
            await this.ssh.getDirectory(domainConfig.localPath, domainConfig.remotePath, {
                recursive: true,
                concurrency: 3,
                validate: (itemPath) => {
                    const relativePath = path.relative(domainConfig.remotePath, itemPath);
                    return !this.shouldIgnoreFile(relativePath);
                },
                tick: (localPath, remotePath, error) => {
                    if (!error) {
                        const relativePath = path.relative(domainConfig.localPath, localPath);
                        if (relativePath) {
                            console.log(chalk.gray(`   📄 ${relativePath}`));
                        }
                    }
                }
            });
            
            domainConfig.lastSync = new Date().toISOString();
            this.saveConfig();
            
            console.log(chalk.green(`✅ Downloaded ${domainName} successfully!`));
            
        } catch (error) {
            console.error(chalk.red(`❌ Error downloading ${domainName}:`), error.message);
        }
    }

    shouldIgnoreFile(filePath) {
        return this.config.hostinger.excludePatterns.some(pattern => {
            if (pattern.endsWith('/')) {
                return filePath.includes(pattern.slice(0, -1));
            }
            return filePath.includes(pattern) || filePath.match(pattern.replace('*', '.*'));
        });
    }

    async uploadFile(localFilePath, domainName) {
        const domainConfig = this.config.domains[domainName];
        if (!domainConfig) return;

        const maxRetries = 3;
        let retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                await this.ensureConnection();

                const relativePath = path.relative(domainConfig.localPath, localFilePath);
                const remoteFilePath = path.posix.join(domainConfig.remotePath, relativePath);

                console.log(chalk.cyan(`⬆️  [${domainName}] ${relativePath} (attempt ${retryCount + 1})`));

                // Ensure remote directory exists
                const remoteDir = path.posix.dirname(remoteFilePath);
                await this.ssh.execCommand(`mkdir -p "${remoteDir}"`);

                // Upload file
                await this.ssh.putFile(localFilePath, remoteFilePath);

                console.log(chalk.green(`✅ [${domainName}] Uploaded: ${relativePath}`));
                return true;

            } catch (error) {
                retryCount++;
                this.isConnected = false; // Force reconnection

                if (retryCount >= maxRetries) {
                    console.error(chalk.red(`❌ [${domainName}] Upload failed after ${maxRetries} attempts: ${path.relative(domainConfig.localPath, localFilePath)}`));
                    console.error(chalk.red(`   Error: ${error.message}`));
                    return false;
                }

                console.log(chalk.yellow(`⚠️  [${domainName}] Upload failed, retrying in 2 seconds... (${retryCount}/${maxRetries})`));
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }

        return false;
    }

    async deleteRemoteFile(localFilePath, domainName) {
        const domainConfig = this.config.domains[domainName];
        if (!domainConfig) return;

        const maxRetries = 3;
        let retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                await this.ensureConnection();

                const relativePath = path.relative(domainConfig.localPath, localFilePath);
                const remoteFilePath = path.posix.join(domainConfig.remotePath, relativePath);

                console.log(chalk.red(`🗑️  [${domainName}] Deleting: ${relativePath} (attempt ${retryCount + 1})`));

                await this.ssh.execCommand(`rm -rf "${remoteFilePath}"`);

                console.log(chalk.red(`✅ [${domainName}] Deleted: ${relativePath}`));
                return true;

            } catch (error) {
                retryCount++;
                this.isConnected = false; // Force reconnection

                if (retryCount >= maxRetries) {
                    console.error(chalk.red(`❌ [${domainName}] Delete failed after ${maxRetries} attempts: ${path.relative(domainConfig.localPath, localFilePath)}`));
                    console.error(chalk.red(`   Error: ${error.message}`));
                    return false;
                }

                console.log(chalk.yellow(`⚠️  [${domainName}] Delete failed, retrying in 2 seconds... (${retryCount}/${maxRetries})`));
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }

        return false;
    }

    addToQueue(operation, filePath, domainName) {
        if (!this.uploadQueues.has(domainName)) {
            this.uploadQueues.set(domainName, []);
        }

        const queue = this.uploadQueues.get(domainName);

        // Remove any existing operations for the same file
        const existingIndex = queue.findIndex(item => item.filePath === filePath);
        if (existingIndex !== -1) {
            queue.splice(existingIndex, 1);
        }

        // Add new operation
        queue.push({ operation, filePath, domainName, timestamp: Date.now() });

        // Process queue
        this.processQueue(domainName);
    }

    async processQueue(domainName) {
        if (!this.uploadQueues.has(domainName)) return;

        const queue = this.uploadQueues.get(domainName);
        if (queue.length === 0) return;

        // Limit concurrent operations per domain
        if (this.activeUploads >= 3) {
            setTimeout(() => this.processQueue(domainName), 1000);
            return;
        }

        const operation = queue.shift();
        if (!operation) return;

        this.activeUploads++;

        try {
            if (operation.operation === 'upload') {
                await this.uploadFile(operation.filePath, operation.domainName);
            } else if (operation.operation === 'delete') {
                await this.deleteRemoteFile(operation.filePath, operation.domainName);
            }
        } catch (error) {
            console.error(chalk.red(`❌ Queue operation failed:`), error.message);
        } finally {
            this.activeUploads--;

            // Process next item in queue
            if (queue.length > 0) {
                setTimeout(() => this.processQueue(domainName), 500);
            }
        }
    }

    startWatchingDomain(domainName) {
        const domainConfig = this.config.domains[domainName];
        if (!domainConfig || !fs.existsSync(domainConfig.localPath)) return;

        const watcher = chokidar.watch(domainConfig.localPath, {
            ignored: /(^|[\/\\])\../,
            persistent: true,
            ignoreInitial: true,
            awaitWriteFinish: {
                stabilityThreshold: 1000,
                pollInterval: 100
            }
        });

        watcher
            .on('change', (filePath) => {
                if (!this.shouldIgnoreFile(path.relative(domainConfig.localPath, filePath))) {
                    console.log(chalk.blue(`📝 [${domainName}] File changed: ${path.relative(domainConfig.localPath, filePath)}`));
                    this.addToQueue('upload', filePath, domainName);
                }
            })
            .on('add', (filePath) => {
                if (!this.shouldIgnoreFile(path.relative(domainConfig.localPath, filePath))) {
                    console.log(chalk.green(`➕ [${domainName}] File added: ${path.relative(domainConfig.localPath, filePath)}`));
                    this.addToQueue('upload', filePath, domainName);
                }
            })
            .on('unlink', (filePath) => {
                if (!this.shouldIgnoreFile(path.relative(domainConfig.localPath, filePath))) {
                    console.log(chalk.red(`➖ [${domainName}] File deleted: ${path.relative(domainConfig.localPath, filePath)}`));
                    this.addToQueue('delete', filePath, domainName);
                }
            })
            .on('error', (error) => {
                console.error(chalk.red(`❌ Watcher error for ${domainName}:`), error.message);
            });

        this.watchers.set(domainName, watcher);
        console.log(chalk.green(`👁️  Watching: ${domainName}`));
    }

    async startCompleteManagement() {
        console.log(chalk.cyan('🚀 Starting Complete Hostinger Account Management'));
        console.log(chalk.gray('====================================='));

        await this.connect();

        // Initial scan and setup
        await this.scanForNewDomains();

        // Start database management
        console.log(chalk.blue('🗄️  Starting database management...'));
        await this.databaseManager.startDatabaseMonitoring();

        // Start watching all domains
        for (const domainName of Object.keys(this.config.domains)) {
            this.startWatchingDomain(domainName);
        }

        // Start monitoring for new domains every 30 seconds
        this.isMonitoring = true;
        this.monitoringInterval = setInterval(async () => {
            if (this.isMonitoring) {
                await this.scanForNewDomains();
            }
        }, 30000);

        console.log(chalk.green('✅ Complete account management started!'));
        console.log(chalk.yellow('📊 Monitoring for new domains every 30 seconds'));
        console.log(chalk.cyan('🔄 Real-time sync active for all domains'));
        console.log(chalk.blue('🗄️  Database backups every hour'));
        console.log(chalk.gray('Press Ctrl+C to stop'));
    }

    async stop() {
        this.isMonitoring = false;

        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
        }

        // Stop database monitoring
        await this.databaseManager.stopDatabaseMonitoring();

        for (const [domainName, watcher] of this.watchers) {
            await watcher.close();
            console.log(chalk.gray(`Stopped watching: ${domainName}`));
        }

        if (this.isConnected) {
            this.ssh.dispose();
            this.isConnected = false;
        }

        console.log(chalk.blue('🛑 Hostinger Account Management stopped'));
    }

    // Database management methods
    async getDatabaseInfo() {
        return await this.databaseManager.getDatabaseInfo();
    }

    async backupAllDatabases() {
        return await this.databaseManager.backupAllDatabases();
    }

    async backupDatabase(dbName, outputPath) {
        return await this.databaseManager.backupDatabase(dbName, outputPath);
    }

    async restoreDatabase(dbName, sqlFilePath) {
        return await this.databaseManager.restoreDatabase(dbName, sqlFilePath);
    }

    async createDatabase(dbName) {
        return await this.databaseManager.createDatabase(dbName);
    }

    async executeQuery(dbName, query) {
        return await this.databaseManager.executeQuery(dbName, query);
    }
}

module.exports = HostingerAccountManager;

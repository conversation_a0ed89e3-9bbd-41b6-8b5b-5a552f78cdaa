<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin'])) {
    header('Location: ../login.php');
    exit();
}

$message = '';

// Handle user actions (delete, status change)
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $fullname = trim($_POST['fullname']);
                $email = trim($_POST['email']);
                $mobile = trim($_POST['mobile']);
                
                $stmt = $conn->prepare("INSERT INTO users (fullname, email, mobile) VALUES (?, ?, ?)");
                $stmt->bind_param("sss", $fullname, $email, $mobile);
                if ($stmt->execute()) {
                    $message = "User added successfully";
                } else {
                    $message = "Error adding user: " . $conn->error;
                }
                break;
                
            case 'delete':
                if (isset($_POST['user_id'])) {
                    $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
                    $stmt->bind_param("i", $_POST['user_id']);
                    $stmt->execute();
                }
                break;
                
            case 'toggle_status':
                if (isset($_POST['user_id'])) {
                    $stmt = $conn->prepare("UPDATE users SET status = CASE WHEN status = 'active' THEN 'inactive' ELSE 'active' END WHERE id = ?");
                    $stmt->bind_param("i", $_POST['user_id']);
                    $stmt->execute();
                }
                break;
                
            case 'assign_all':
                if (isset($_POST['user_id'])) {
                    // Get all active strategies
                    $strategies = $conn->query("SELECT id FROM strategies WHERE status = 'active'");
                    while ($strategy = $strategies->fetch_assoc()) {
                        // Check if assignment already exists
                        $check = $conn->prepare("SELECT 1 FROM strategy_users WHERE strategy_id = ? AND user_id = ?");
                        $check->bind_param("ii", $strategy['id'], $_POST['user_id']);
                        $check->execute();
                        if ($check->get_result()->num_rows === 0) {
                            // Add new assignment
                            $stmt = $conn->prepare("INSERT INTO strategy_users (strategy_id, user_id) VALUES (?, ?)");
                            $stmt->bind_param("ii", $strategy['id'], $_POST['user_id']);
                            $stmt->execute();
                        }
                    }
                }
                break;
        }
    }
}

// Get all users
$users = $conn->query("SELECT * FROM users ORDER BY created_at DESC");

include '../templates/header.php';
?>

<div class="admin-container">
    <div class="admin-header">
        <h1>User Management</h1>
        <button onclick="showAddUserForm()" class="btn btn-primary">Add New User</button>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-success"><?php echo $message; ?></div>
    <?php endif; ?>

    <!-- Add User Form (Hidden by default) -->
    <div id="addUserForm" class="form-container" style="display: none;">
        <form method="POST" action="">
            <input type="hidden" name="action" value="add">
            <div class="form-group">
                <label for="fullname">Full Name *</label>
                <input type="text" id="fullname" name="fullname" required>
            </div>
            <div class="form-group">
                <label for="email">Email *</label>
                <input type="email" id="email" name="email" required>
                <small class="help-text">User must be registered on www.algoangel.in with this email</small>
            </div>
            <div class="form-group">
                <label for="mobile">Mobile</label>
                <input type="text" id="mobile" name="mobile">
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">Add User</button>
                <button type="button" onclick="hideAddUserForm()" class="btn">Cancel</button>
            </div>
        </form>
    </div>

    <div class="table-responsive">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Mobile</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Assigned Strategies</th>
                    <th>Quick Assign</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($user = $users->fetch_assoc()): ?>
                <tr>
                    <td><?php echo htmlspecialchars($user['id']); ?></td>
                    <td><?php echo htmlspecialchars($user['fullname']); ?></td>
                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                    <td><?php echo htmlspecialchars($user['mobile']); ?></td>
                    <td>
                        <span class="status-badge <?php echo $user['status']; ?>">
                            <?php echo ucfirst($user['status']); ?>
                        </span>
                    </td>
                    <td><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></td>
                    <td>
                        <?php
                        $stmt = $conn->prepare("SELECT s.name FROM strategies s 
                                               INNER JOIN strategy_users su ON s.id = su.strategy_id 
                                               WHERE su.user_id = ?");
                        $stmt->bind_param("i", $user['id']);
                        $stmt->execute();
                        $strategies = $stmt->get_result();
                        while ($strategy = $strategies->fetch_assoc()) {
                            echo htmlspecialchars($strategy['name']) . "<br>";
                        }
                        ?>
                    </td>
                    <td>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="assign_all">
                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                            <button type="submit" class="btn btn-small btn-info">Assign All Strategies</button>
                        </form>
                    </td>
                    <td class="actions">
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="toggle_status">
                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                            <button type="submit" class="btn btn-small <?php echo $user['status'] == 'active' ? 'btn-warning' : 'btn-success'; ?>">
                                <?php echo $user['status'] == 'active' ? 'Deactivate' : 'Activate'; ?>
                            </button>
                        </form>
                        <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this user?');">
                            <input type="hidden" name="action" value="delete">
                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                            <button type="submit" class="btn btn-small btn-danger">Delete</button>
                        </form>
                    </td>
                </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
    </div>
</div>

<script>
function showAddUserForm() {
    document.getElementById('addUserForm').style.display = 'block';
}

function hideAddUserForm() {
    document.getElementById('addUserForm').style.display = 'none';
}
</script>

<?php include '../templates/footer.php'; ?> 
{"version": 3, "file": "static/js/659.2b4d301d.chunk.js", "mappings": "gOAGA,MAuCA,EAvCkBA,GACIC,IAKpB,MAAMC,GAAUC,EAAAA,EAAAA,MA2BhB,OA1BAC,EAAAA,EAAAA,YAAU,KACT,MAAMC,EAAQC,aAAaC,QAAQ,uBAC7B,SAAEC,GAAaC,OAAOC,SACtBC,EAAY,WAAaH,EAAW,UAC1C,GAAKH,EAEE,CACN,MAAMO,EAAiB,CACtBC,OAAQ,OACRC,QAAS,CAAE,eAAgB,oBAC3BC,KAAMC,KAAKC,UAAU,CAAEZ,WAExBa,MAAMC,EAAAA,GAAU,eAAgBP,GAC9BQ,MAAMC,GAAaA,EAASC,SAC5BF,MAAMG,IACY,oBAAdA,EAAKC,OACRf,OAAOC,SAASe,KAAOd,EACxB,IAEAe,OAAOC,IACPC,QAAQD,MAAM,SAAUA,GACxBlB,OAAOC,SAASe,KAAOd,CAAS,GAEnC,MAlBCF,OAAOC,SAASe,KAAOd,CAkBxB,GACE,CAACT,KAEG2B,EAAAA,EAAAA,KAAC7B,EAAgB,IAAKC,GAAS,E,iRCfxC,SAAS6B,IACR,MAAOC,EAAcC,IAAmBC,EAAAA,EAAAA,WAAS,IAC1CC,EAAoBC,IAAyBF,EAAAA,EAAAA,UAAS,IAEvDG,IADUjC,EAAAA,EAAAA,OACGkC,EAAAA,EAAAA,UAAQ,IAAM,IAAIC,KAAO,MACtC,SAAEC,IAAaC,EAAAA,EAAAA,OACdjB,EAAMkB,IAAWR,EAAAA,EAAAA,aAEjBS,EAAcC,IAAmBV,EAAAA,EAAAA,UAAS,IAAIW,MA2BrD,SAASC,EAAaC,GACrB,IAAIC,EAAe,GACnBD,EAAUE,KAAI,CAACC,EAAKC,IACZH,EAAaI,KAAKC,EAAqBF,EAAOD,MAEtDd,EAAsBY,EACvB,CAEA,SAASK,EAAqBC,EAAKJ,GAClC,MAAO,CACNC,MAAOG,EAAM,EACbC,SAAUL,EAAIK,SACdC,eAAgBN,EAAIM,eACpBC,SAAUP,EAAIO,SACdC,SAAUR,EAAIQ,SACdC,MAAOT,EAAIS,MACXnB,SAAUU,EAAIU,OAASV,EAAIV,SAAWU,EAAIW,KAC1CC,aAAaC,EAAAA,EAAAA,IAAmBb,EAAIY,aACpCE,WAAYd,EAAIc,WAAWC,MAAM,KAAK,GAAGA,MAAM,KAAK,GACpDC,MACCpC,EAAAA,EAAAA,KAAA,UACCqC,KAAK,SACLC,UAAU,2DACVC,QAASA,IACR3D,OAAO4D,KAAK,uBAADC,OAAwBrB,EAAIsB,aAAe,UACtDC,SACD,SAIFvB,IAAKA,EAEP,EAxDAwB,EAAAA,EAAAA,UAAS,uBAAuB,KAC/BC,EAAAA,EAAAA,KAAoBC,EAAAA,EAAAA,SAAO,IAAI/B,KAAQ,eACrCxB,MAAMwD,IACN,GAAIA,EAAIjB,OAAQ,CACflB,EAAQmC,EAAIrD,MACZ,MAAMsD,EAAUC,OAAOC,OAAOH,EAAIrD,MAOlC,OANAsB,EAAagC,GAEbA,EAAQ7B,KAAI,CAACC,EAAKC,IACVd,EAAW4C,IAAI/B,EAAIgC,GAAIhC,KAE/BjB,GAAgB,GACT4C,CACR,CAEC,OADA5C,GAAgB,IACT,CACR,IAEAN,OAAOwD,IACPlD,GAAgB,GAChBJ,QAAQuD,IAAI,WAAYD,EAAE,MAsC7B,MAcME,EAAU,CACf,CACCC,KAAM,KACNC,SAAWC,GAAQA,EAAIrC,MACvBsC,UAAU,EACVC,SAAU,OACVC,SAAU,QAEX,CACCL,KAAM,OACNC,SAAWC,GAAQA,EAAIjC,SACvBkC,UAAU,EACVG,MAAM,GAEP,CACCN,KAAM,SACNC,SAAWC,GAAQA,EAAIhC,eACvBiC,UAAU,EACVG,MAAM,GAGP,CAAEN,KAAM,WAAYC,SAAWC,GAAQA,EAAI9B,SAAU+B,UAAU,GAC/D,CACCH,KAAM,WACNC,SAAWC,GAAQA,EAAIhD,SACvBiD,UAAU,EACVG,MAAM,GAEP,CACCN,KAAM,SACNC,SAAWC,GAAQA,EAAI1B,YACvB2B,UAAU,EACVI,aA9C6BC,CAACC,EAAGC,IAEX,MAAtBD,EAAE7C,IAAIY,aACgB,MAAtBiC,EAAE7C,IAAIY,aACgB,QAAtBiC,EAAE7C,IAAIY,aACgB,QAAtBiC,EAAE7C,IAAIY,aACgB,QAAtBiC,EAAE7C,IAAIY,YAEC,GAEC,GAsCT,CAAEwB,KAAM,OAAQC,SAAWC,GAAQA,EAAIxB,WAAYyB,UAAU,GAE7D,CAAEH,KAAM,MAAOC,SAAWC,GAAQA,EAAItC,IAAK+C,MAAM,KA2D5C,YAAEC,IAAgBC,EAAAA,EAAAA,KACxB,OACCC,EAAAA,EAAAA,MAAA,OAAA3B,SAAA,EACC3C,EAAAA,EAAAA,KAAA,OAAKsC,UAAU,cAAaK,UAC3B3C,EAAAA,EAAAA,KAAA,MAAIsC,UAAS,cAAAG,OAAgB2B,GAAe,mBAAoBzB,SAAC,qBAElE3C,EAAAA,EAAAA,KAAA,OAAKsC,UAAS,QAAAG,OAAU2B,GAAe,mBAAoBzB,UAC1D3C,EAAAA,EAAAA,KAAA,OAAKsC,UAAU,YAAWK,UACzB2B,EAAAA,EAAAA,MAAA,OAAKhC,UAAU,MAAKK,SAAA,EACnB3C,EAAAA,EAAAA,KAAA,OAAKsC,UAAU,WAAUK,UACxB3C,EAAAA,EAAAA,KAACuE,EAAAA,EAAI,CAAA5B,UACJ3C,EAAAA,EAAAA,KAACuE,EAAAA,EAAKC,MAAK,CAACC,UAAU,aAAanC,UAAS,GAAAG,OAAK2B,GAAe,kBAAiB,SAAQzB,UAExF3C,EAAAA,EAAAA,KAAC0E,IAAU,CACVC,SAAU9D,EACV+D,SAvCiBC,IACzB/D,EAAgB+D,EAAK,EAuCbvC,UAAU,eACVwC,WAAW,sBAKf9E,EAAAA,EAAAA,KAAA,OAAKsC,UAAU,wBAAuBK,UACrC3C,EAAAA,EAAAA,KAAC+E,EAAAA,EAAM,CACN1C,KAAK,MACLC,UAAU,yBACVC,QA5CiByC,MACvBnC,EAAAA,EAAAA,KAAoBC,EAAAA,EAAAA,SAAOjC,EAAc,eACvCtB,MAAMwD,IAENnC,EAAQmC,EAAIrD,MACZ,MAAMsD,EAAUC,OAAOC,OAAOH,EAAIrD,MAClCsB,EAAagC,GAEbA,EAAQ7B,KAAI,CAACC,EAAKC,IACVd,EAAW4C,IAAI/B,EAAIgC,GAAIhC,IAC7B,IAEFvB,OAAOwD,IACPtD,QAAQuD,IAAI,WAAYD,EAAE,GACzB,EA8B4BV,SACzB,mBAIF3C,EAAAA,EAAAA,KAAA,OAAKsC,UAAU,qBAIlBtC,EAAAA,EAAAA,KAAA,OAAKsC,UAAU,qBAAoBK,UAClC3C,EAAAA,EAAAA,KAAA,OAAKsC,UAAU,qCAAoCK,UAClD3C,EAAAA,EAAAA,KAAA,OAAKsC,UAAS,QAAAG,OAAU2B,GAAe,mBAAoBzB,UAC1D3C,EAAAA,EAAAA,KAAA,OAAKsC,UAAU,YAAWK,SACvBzC,GAWDoE,EAAAA,EAAAA,MAAA,OAAKhC,UAAU,mBAAkBK,SAAA,EAChC3C,EAAAA,EAAAA,KAACuE,EAAAA,EAAKC,MAAK,CAAA7B,UACV3C,EAAAA,EAAAA,KAACuE,EAAAA,EAAKU,QAAO,CACZ3C,UAAS,GAAAG,OAAK2B,GAAe,oBAAmB,yBAChD/B,KAAK,OACL6C,YAAY,SACZN,SAjHYvB,IACrB,IAAI8B,EAAa9B,EAAE+B,OAAOC,MACtBC,EAAe,GACnB,IAAK,IAAID,KAAS9E,EAAW2C,SAAU,CAAC,IAADqC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GAEvB,QAAdV,EAAAF,EAAM5D,gBAAQ,IAAA8D,GAAdA,EAAgBW,cAAcC,SAAmB,OAAVhB,QAAU,IAAVA,OAAU,EAAVA,EAAYe,gBAC/B,QAD6CV,EACjEH,EAAM3D,sBAAc,IAAA8D,GAApBA,EACGU,cACDC,SAAmB,OAAVhB,QAAU,IAAVA,OAAU,EAAVA,EAAYe,gBACT,QADuBT,EACrCJ,EAAM3E,gBAAQ,IAAA+E,GACD,QADCC,EAAdD,EACGW,kBAAU,IAAAV,GADbA,EAEGQ,cACDC,SAAmB,OAAVhB,QAAU,IAAVA,OAAU,EAAVA,EAAYe,gBACb,QAD2BP,EACrCN,EAAMtD,YAAI,IAAA4D,GAAVA,EAAYO,cAAcC,SAAmB,OAAVhB,QAAU,IAAVA,OAAU,EAAVA,EAAYe,gBACjC,QAD+CN,EAC7DP,EAAM1D,gBAAQ,IAAAiE,GAAdA,EAAgBM,cAAcC,SAAmB,OAAVhB,QAAU,IAAVA,OAAU,EAAVA,EAAYe,gBACxC,QADsDL,EACjER,EAAMxD,aAAK,IAAAgE,GAAXA,EAAaO,WAAWD,SAAShB,IACnB,QAD8BW,EAC5CT,EAAMzD,gBAAQ,IAAAkE,GAAdA,EAAgBM,WAAWD,SAAShB,IACnB,QAD8BY,EAC/CV,EAAMrD,mBAAW,IAAA+D,GAAjBA,EAAmBG,cAAcC,SAAmB,OAAVhB,QAAU,IAAVA,OAAU,EAAVA,EAAYe,gBACtC,QADoDF,EACpEX,EAAMnD,kBAAU,IAAA8D,GAAhBA,EAAkBI,WAAWD,SAAShB,IACtB,QADiCc,EACjDZ,EAAMnD,kBAAU,IAAA+D,GAAhBA,EACG9D,MAAM,KAAK,GACZA,MAAM,KAAK,GACXiE,WACAD,SAAShB,KAEXG,EAAahE,KAAK+D,EAEpB,CACA/E,EACCgF,EAAanE,KAAI,CAACC,EAAKC,IAAUE,EAAqBF,EAAOD,KAC7D,EAoFQiF,UAAQ,OAGVrG,EAAAA,EAAAA,KAACsG,EAAAA,GAAS,CACT/C,QAASA,EACT7D,KAAMW,EACNkG,YAAU,EACVC,kBAAmB,GACnBC,SAAUrC,EACVsC,OAAOC,EAAAA,EAAAA,MACPC,kBAAgB,EAChBC,UAAQ,QA5BV7G,EAAAA,EAAAA,KAAA,OACCsC,UAAU,mDACVwE,MAAO,CAAEC,OAAQ,QAASpE,UAE1B3C,EAAAA,EAAAA,KAACgH,EAAAA,EAAO,CACPC,UAAU,SACVH,MAAO,CAAEI,MAAO,OAAQH,OAAQ,sBAgC1C,E,0BACA,SAAeI,EAAAA,EAAAA,GAASlH,G,6ICnRjB,MAAMmH,EAAwB,CACpCC,OAAQA,CAACC,EAAUC,KAAK,IACpBD,EACHE,gBAAiBD,EAAME,WAAa,OAAS,QAC7CC,MAAOH,EAAME,WAAa,QAAU,WAGzBE,EAAsB,CAClCN,OAAQA,CAACO,EAAML,KAAK,IAChBH,EAAsBC,OAAOO,EAAML,GACtC,UAAW,CACVC,gBAAiB,YACjBE,MAAO,YAsBH,SAASzF,EAAmB4F,GAClC,IAAIC,EAASD,EAAYE,OAAO,GAO5BC,EACQ,MAAXF,GACW,MAAXA,GACW,SAAXA,GACW,SAAXA,GACW,SAAXA,EACD,MAXY,MAAXA,GACW,MAAXA,GACW,QAAXA,GACW,QAAXA,GACW,QAAXA,GAQO9H,EAAAA,EAAAA,KAAA,SAAOsC,UAAU,8BAA6BK,SAAC,QAC5CqF,GACHhI,EAAAA,EAAAA,KAAA,SAAOsC,UAAU,8BAA6BK,SAAC,SAGrDkF,IACC7H,EAAAA,EAAAA,KAAA,SAAOsC,UAAU,6BAA4BK,SAC3CkF,EAAYI,eAKlB,CAqBO,SAASC,EAAmBC,GAElC,OADAA,EAASC,WAAWD,GAAU,GAAGE,QAAQ,IAC5B,GAEX/D,EAAAA,EAAAA,MAAA,QAAMhC,UAAU,cAAaK,SAAA,CAC3B,IACAwF,EAAO,KAACnI,EAAAA,EAAAA,KAAA,KAAGsC,UAAU,0BAGH,IAAX6F,GAA2B,OAAXA,GAC1BA,EAAS,GACF7D,EAAAA,EAAAA,MAAA,QAAA3B,SAAA,CAAM,IAAEwF,OAGf7D,EAAAA,EAAAA,MAAA,QAAMhC,UAAU,eAAcK,SAAA,CAC5B,IACAwF,EAAO,KAACnI,EAAAA,EAAAA,KAAA,KAAGsC,UAAU,uBAGzB,CAiBO,SAASgG,EAAexG,EAAQyG,GACtC,MAAyB,MAArBzG,EAAOiG,OAAO,IAAmC,MAArBjG,EAAOiG,OAAO,IAE5C/H,EAAAA,EAAAA,KAAA,SACCsC,UAAU,8BACV,cAAY,UACZ,iBAAe,MACfkG,MAAOD,EAAO5F,SACd,cAI6B,MAArBb,EAAOiG,OAAO,IAAmC,MAArBjG,EAAOiG,OAAO,IAEnD/H,EAAAA,EAAAA,KAAA,SACCsC,UAAU,6BACV,cAAY,UACZ,iBAAe,MACfkG,MAAOD,EAAO5F,SACd,cAiBD3C,EAAAA,EAAAA,KAAA,SACCsC,UAAU,2BACV,cAAY,UACZ,iBAAe,MACfkG,MAAOD,EAAO5F,SAEbb,GAIL,CAEO,SAAS2G,EAAmBC,GAClC,OAAIC,MAAMD,GACF,MAEJA,EAAS,IACLA,EAAOtC,WAEXsC,GAAU,KAAQA,EAAS,KACtBA,EAAS,KAAML,QAAQ,GAAK,KAEjCK,GAAU,KAAUA,EAAS,KACxBA,EAAS,KAAQL,QAAQ,GAAK,KAEnCK,GAAU,KACLA,EAAS,KAAUL,QAAQ,GAAK,WADzC,CAGD,CAKO,MAoCM1B,EAAWA,IACqC,SAAxClI,aAAaC,QAAQ,iBApCzCkK,EAAAA,EAAAA,IACC,YACA,CACCC,WAAY,CACXC,QAAS,eAEVC,OAAQ,CACPC,OAAQ,kBACRC,MAAO,kBACPC,SAAU,oBAGZ,QA4BO,eAtBRN,EAAAA,EAAAA,IACC,gBACA,CACCC,WAAY,CACXC,QAAS,QAEVC,OAAQ,CACPC,OAAQ,kBACRC,MAAO,kBACPC,SAAU,oBAGZ,SAaO,gB,gGCxOLC,EAAY,CAAC,WAAY,UAAW,YAAa,OAAQ,WAAY,KAAM,aAI3EnC,EAAuBoC,EAAAA,YAAiB,SAAUC,EAAMC,GAC1D,IAAIC,EAAWF,EAAKE,SAChBC,EAAUH,EAAKG,QACfvC,EAAYoC,EAAKpC,UACjBwC,EAAOJ,EAAKI,KACZ9G,EAAW0G,EAAK1G,SAChB+G,EAAUL,EAAKM,GACfC,OAAwB,IAAZF,EAAqB,MAAQA,EACzCpH,EAAY+G,EAAK/G,UACjBlE,GAAQyL,EAAAA,EAAAA,GAA8BR,EAAMF,GAG5CW,GADJP,GAAWQ,EAAAA,EAAAA,IAAmBR,EAAU,YACP,IAAMtC,EACvC,OAAoBmC,EAAAA,cAAoBQ,GAAWI,EAAAA,EAAAA,GAAS,CAC1DV,IAAKA,GACJlL,EAAO,CACRkE,UAAW2H,IAAW3H,EAAWwH,EAAiBL,GAAQK,EAAkB,IAAML,EAAMD,GAAW,QAAUA,KAC3G7G,EACN,IACAqE,EAAQkD,YAAc,UACtB,S", "sources": ["app/components/higher-order/withauth.jsx", "app/user-pages/failed-orders/FailedOrder.jsx", "app/user-pages/ui-helper.jsx", "../node_modules/react-bootstrap/esm/Spinner.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\r\nimport { useHistory } from \"react-router-dom\";\r\nimport { API_URL } from \"../../../Util/constant\";\r\nconst withAuth = (WrappedComponent) => {\r\n\tconst AuthWrapper = (props) => {\r\n\t\t// localStorage.setItem(\r\n\t\t// \t\"admin_access_token\",\r\n\t\t// \t\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************.2pJTNAozJZkRDCJcIqE4OaKIEfq2may8sSTswl637LE\"\r\n\t\t// );\r\n\t\tconst history = useHistory();\r\n\t\tuseEffect(() => {\r\n\t\t\tconst token = localStorage.getItem(\"admin_access_token\");\r\n\t\t\tconst { hostname } = window.location;\r\n\t\t\tconst login_url = \"https://\" + hostname + \"/admin/\";\r\n\t\t\tif (!token) {\r\n\t\t\t\twindow.location.href = login_url;\r\n\t\t\t} else {\r\n\t\t\t\tconst requestOptions = {\r\n\t\t\t\t\tmethod: \"POST\",\r\n\t\t\t\t\theaders: { \"Content-Type\": \"application/json\" },\r\n\t\t\t\t\tbody: JSON.stringify({ token }),\r\n\t\t\t\t};\r\n\t\t\t\tfetch(API_URL + \"/auth/verify\", requestOptions)\r\n\t\t\t\t\t.then((response) => response.json())\r\n\t\t\t\t\t.then((data) => {\r\n\t\t\t\t\t\tif (data.code === \"token_not_valid\") {\r\n\t\t\t\t\t\t\twindow.location.href = login_url;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((error) => {\r\n\t\t\t\t\t\tconsole.error(\"Error:\", error);\r\n\t\t\t\t\t\twindow.location.href = login_url;\r\n\t\t\t\t\t});\r\n\t\t\t}\r\n\t\t}, [history]);\r\n\r\n\t\treturn <WrappedComponent {...props} />;\r\n\t};\r\n\r\n\treturn AuthWrapper;\r\n};\r\n\r\nexport default withAuth;\r\n", "import \"../userpages.css\";\r\nimport React, { useMemo, useState } from \"react\";\r\nimport DataTable from \"react-data-table-component\";\r\nimport { useQuery } from \"react-query\";\r\nimport {\r\n\tGetFailedOrdersData,\r\n\tGetOrdersByStrategyId,\r\n\tGetOrdersData,\r\n} from \"../../../services/backendServices\";\r\nimport withAuth from \"../../components/higher-order/withauth\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { useHistory } from \"react-router-dom\";\r\nimport { Spinner } from \"react-bootstrap\";\r\nimport { Button, Form } from \"react-bootstrap\";\r\nimport DatePicker from \"react-datepicker\";\r\nimport \"react-datepicker/dist/react-datepicker.css\";\r\nimport { format } from \"date-fns\";\r\n\r\nimport { getTheme, getTransactionType } from \"../../user-pages/ui-helper\";\r\nimport { useTheme } from \"../../context/ThemeContext\";\r\n\r\nfunction FailedOrders() {\r\n\tconst [isDataLoaded, setIsDataLoaded] = useState(false);\r\n\tconst [tableDataFormatted, setTableDataFormatted] = useState([]);\r\n\tconst history = useHistory();\r\n\tconst rawUserMap = useMemo(() => new Map(), []);\r\n\tconst { order_id } = useParams();\r\n\tconst [data, setData] = useState();\r\n\r\n\tconst [selectedDate, setSelectedDate] = useState(new Date());\r\n\r\n\t// API CALL for Parent Orders\r\n\tuseQuery(\"GetFailedOrdersData\", () =>\r\n\t\tGetFailedOrdersData(format(new Date(), \"MM-dd-yyyy\"))\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tsetData(res.data);\r\n\t\t\t\t\tconst jsonArr = Object.values(res.data);\r\n\t\t\t\t\tsetTableData(jsonArr);\r\n\t\t\t\t\t// Setting row data in rawUserMap with key value\r\n\t\t\t\t\tjsonArr.map((obj, index) => {\r\n\t\t\t\t\t\treturn rawUserMap.set(obj.id, obj);\r\n\t\t\t\t\t});\r\n\t\t\t\t\tsetIsDataLoaded(true);\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetIsDataLoaded(true);\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tsetIsDataLoaded(true);\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t})\r\n\t);\r\n\r\n\tfunction setTableData(tableData) {\r\n\t\tvar rowTableData = [];\r\n\t\ttableData.map((obj, index) => {\r\n\t\t\treturn rowTableData.push(getRowFormatForTable(index, obj));\r\n\t\t});\r\n\t\tsetTableDataFormatted(rowTableData);\r\n\t}\r\n\r\n\tfunction getRowFormatForTable(ind, obj) {\r\n\t\treturn {\r\n\t\t\tindex: ind + 1,\r\n\t\t\tref_name: obj.ref_name,\r\n\t\t\ttrading_symbol: obj.trading_symbol,\r\n\t\t\texchange: obj.exchange,\r\n\t\t\tquantity: obj.quantity,\r\n\t\t\tprice: obj.price,\r\n\t\t\torder_id: obj.status ? obj.order_id : obj.emsg,\r\n\t\t\taction_type: getTransactionType(obj.action_type),\r\n\t\t\torder_time: obj.order_time.split(\".\")[0].split(\"T\")[1],\r\n\t\t\tview: (\r\n\t\t\t\t<button\r\n\t\t\t\t\ttype=\"button\"\r\n\t\t\t\t\tclassName=\"btn btn-outline-primary btn-icon-text btnShowChildOrders\"\r\n\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\twindow.open(`/admin/panel/orders/${obj.strategy_id}`, \"_blank\")\r\n\t\t\t\t\t}\r\n\t\t\t\t>\r\n\t\t\t\t\tView\r\n\t\t\t\t</button>\r\n\t\t\t),\r\n\t\t\tobj: obj,\r\n\t\t};\r\n\t}\r\n\r\n\tconst actionTypeSortFunction = (a, b) => {\r\n\t\tif (\r\n\t\t\ta.obj.action_type === \"B\" ||\r\n\t\t\ta.obj.action_type === \"b\" ||\r\n\t\t\ta.obj.action_type === \"buy\" ||\r\n\t\t\ta.obj.action_type === \"BUY\" ||\r\n\t\t\ta.obj.action_type === \"Buy\"\r\n\t\t) {\r\n\t\t\treturn 1;\r\n\t\t} else {\r\n\t\t\treturn -1;\r\n\t\t}\r\n\t};\r\n\r\n\tconst columns = [\r\n\t\t{\r\n\t\t\tname: \"Id\",\r\n\t\t\tselector: (row) => row.index,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"65px\",\r\n\t\t\tminWidth: \"65px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Name\",\r\n\t\t\tselector: (row) => row.ref_name,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Script\",\r\n\t\t\tselector: (row) => row.trading_symbol,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\r\n\t\t{ name: \"Quantity\", selector: (row) => row.quantity, sortable: true },\r\n\t\t{\r\n\t\t\tname: \"Order Id\",\r\n\t\t\tselector: (row) => row.order_id,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Action\",\r\n\t\t\tselector: (row) => row.action_type,\r\n\t\t\tsortable: true,\r\n\t\t\tsortFunction: actionTypeSortFunction,\r\n\t\t},\r\n\t\t{ name: \"Time\", selector: (row) => row.order_time, sortable: true },\r\n\t\t// { name: \"View\", selector: (row) => row.view },\r\n\t\t{ name: \"obj\", selector: (row) => row.obj, omit: true },\r\n\t];\r\n\tconst handleSearch = (e) => {\r\n\t\tvar search_val = e.target.value;\r\n\t\tvar filteredData = [];\r\n\t\tfor (let value of rawUserMap.values()) {\r\n\t\t\tif (\r\n\t\t\t\tvalue.ref_name?.toLowerCase().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.trading_symbol\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.order_id\r\n\t\t\t\t\t?.toString()\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.emsg?.toLowerCase().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.exchange?.toLowerCase().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.price?.toString().includes(search_val) ||\r\n\t\t\t\tvalue.quantity?.toString().includes(search_val) ||\r\n\t\t\t\tvalue.action_type?.toLowerCase().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.order_time?.toString().includes(search_val) ||\r\n\t\t\t\tvalue.order_time\r\n\t\t\t\t\t?.split(\".\")[0]\r\n\t\t\t\t\t.split(\"T\")[1]\r\n\t\t\t\t\t.toString()\r\n\t\t\t\t\t.includes(search_val)\r\n\t\t\t) {\r\n\t\t\t\tfilteredData.push(value);\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetTableDataFormatted(\r\n\t\t\tfilteredData.map((obj, index) => getRowFormatForTable(index, obj))\r\n\t\t);\r\n\t};\r\n\r\n\tconst handleDateChange = (date) => {\r\n\t\tsetSelectedDate(date);\r\n\t\t// const formattedDate = format(date, \"MM-dd-yyyy\");\r\n\t\t// console.log(\"Selected Date:\", formattedDate);\r\n\t};\r\n\r\n\tconst handleShowOrder = () => {\r\n\t\tGetFailedOrdersData(format(selectedDate, \"MM-dd-yyyy\"))\r\n\t\t\t.then((res) => {\r\n\t\t\t\t// console.log(\"============\", res);\r\n\t\t\t\tsetData(res.data);\r\n\t\t\t\tconst jsonArr = Object.values(res.data);\r\n\t\t\t\tsetTableData(jsonArr);\r\n\t\t\t\t// Setting row data in rawUserMap with key value\r\n\t\t\t\tjsonArr.map((obj, index) => {\r\n\t\t\t\t\treturn rawUserMap.set(obj.id, obj);\r\n\t\t\t\t});\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t});\r\n\t};\r\n\r\n\r\n\tconst { isDarkTheme } = useTheme()\r\n\treturn (\r\n\t\t<div>\r\n\t\t\t<div className=\"page-header\">\r\n\t\t\t\t<h3 className={`page-title ${isDarkTheme && \"dark-page-title\"}`}>Failed Orders</h3>\r\n\t\t\t</div>\r\n\t\t\t<div className={`card ${isDarkTheme && \"dark-theme-card\"}`}>\r\n\t\t\t\t<div className=\"card-body\">\r\n\t\t\t\t\t<div className=\"row\">\r\n\t\t\t\t\t\t<div className=\"col-md-3\">\r\n\t\t\t\t\t\t\t<Form>\r\n\t\t\t\t\t\t\t\t<Form.Group controlId=\"datePicker\" className={`${isDarkTheme && \"dark-datepicker\"} mb-0`}>\r\n\t\t\t\t\t\t\t\t\t{/* Select Date:&nbsp; &nbsp; */}\r\n\t\t\t\t\t\t\t\t\t<DatePicker\r\n\t\t\t\t\t\t\t\t\t\tselected={selectedDate}\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleDateChange}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"form-control\"\r\n\t\t\t\t\t\t\t\t\t\tdateFormat=\"dd-MM-yyyy\" // Customize date format if needed\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t</Form>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div className=\"col-md-2 py-1 rounded\">\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\ttype=\"btn\"\r\n\t\t\t\t\t\t\t\tclassName=\"btn btn-md btn-primary\"\r\n\t\t\t\t\t\t\t\tonClick={handleShowOrder}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tShow Orders\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div className=\"col-md-4\"></div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div className=\"row customtab mt-3\">\r\n\t\t\t\t<div className=\"col-lg-12 grid-margin stretch-card\">\r\n\t\t\t\t\t<div className={`card ${isDarkTheme && \"dark-theme-card\"}`}>\r\n\t\t\t\t\t\t<div className=\"card-body\">\r\n\t\t\t\t\t\t\t{!isDataLoaded ? (\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tclassName=\"d-flex justify-content-center align-items-center\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ height: \"100%\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Spinner\r\n\t\t\t\t\t\t\t\t\t\tanimation=\"border\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"5rem\", height: \"5rem\" }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t<div className=\"table-responsive\">\r\n\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"} mb-2 searchbox-style`}\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Search\"\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={handleSearch}\r\n\t\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t<DataTable\r\n\t\t\t\t\t\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\t\t\t\t\t\tdata={tableDataFormatted}\r\n\t\t\t\t\t\t\t\t\t\tpagination\r\n\t\t\t\t\t\t\t\t\t\tpaginationPerPage={10}\r\n\t\t\t\t\t\t\t\t\t\tstriped={!isDarkTheme}\r\n\t\t\t\t\t\t\t\t\t\ttheme={getTheme()}\r\n\t\t\t\t\t\t\t\t\t\thighlightOnHover\r\n\t\t\t\t\t\t\t\t\t\tnoHeader\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n}\r\nexport default withAuth(FailedOrders);\r\n", "import React from \"react\";\r\nimport { createTheme } from \"react-data-table-component\";\r\n\r\nexport const customStylesForSelect = {\r\n\toption: (provided, state) => ({\r\n\t\t...provided,\r\n\t\tbackgroundColor: state.isSelected ? \"blue\" : \"white\", // Change the background color as desired\r\n\t\tcolor: state.isSelected ? \"white\" : \"black\", // Change the text color as desired\r\n\t}),\r\n};\r\nexport const hoverEffectOnSelect = {\r\n\toption: (base, state) => ({\r\n\t\t...customStylesForSelect.option(base, state),\r\n\t\t\"&:hover\": {\r\n\t\t\tbackgroundColor: \"lightgray\", // Change the hover background color\r\n\t\t\tcolor: \"black\", // Change the hover text color\r\n\t\t},\r\n\t}),\r\n};\r\n\r\nexport const createDataTableTheme = () => {\r\n\tcreateTheme(\r\n\t\t\"solarized\",\r\n\t\t{\r\n\t\t\tbackground: {\r\n\t\t\t\tdefault: \"transparent\",\r\n\t\t\t},\r\n\t\t\taction: {\r\n\t\t\t\tbutton: \"rgba(0,0,0,.54)\",\r\n\t\t\t\thover: \"rgba(0,0,0,.08)\",\r\n\t\t\t\tdisabled: \"rgba(0,0,0,.12)\",\r\n\t\t\t},\r\n\t\t},\r\n\t\t\"dark\"\r\n\t);\r\n};\r\n\r\nexport function getTransactionType(transaction) {\r\n\tlet a_type = transaction.charAt(0);\r\n\tlet is_buy =\r\n\t\ta_type === \"B\" ||\r\n\t\ta_type === \"b\" ||\r\n\t\ta_type === \"buy\" ||\r\n\t\ta_type === \"BUY\" ||\r\n\t\ta_type === \"Buy\";\r\n\tlet is_sell =\r\n\t\ta_type === \"S\" ||\r\n\t\ta_type === \"s\" ||\r\n\t\ta_type === \"sell\" ||\r\n\t\ta_type === \"SELL\" ||\r\n\t\ta_type === \"Sell\";\r\n\tif (is_buy) {\r\n\t\treturn <label className=\"badge badge-outline-warning\">BUY</label>;\r\n\t} else if (is_sell) {\r\n\t\treturn <label className=\"badge badge-outline-primary\">SELL</label>;\r\n\t} else {\r\n\t\treturn (\r\n\t\t\ttransaction && (\r\n\t\t\t\t<label className=\"badge badge-outline-danger\">\r\n\t\t\t\t\t{transaction.toUpperCase()}\r\n\t\t\t\t</label>\r\n\t\t\t)\r\n\t\t);\r\n\t}\r\n}\r\n\r\nexport function getPnlComponent(pnlVal) {\r\n\tpnlVal = parseFloat(pnlVal || 0).toFixed(2);\r\n\tif (pnlVal < 0) {\r\n\t\treturn (\r\n\t\t\t'<span class=\"text-danger\"> ' +\r\n\t\t\tpnlVal +\r\n\t\t\t' <i class=\"mdi mdi-arrow-down\"></i></span>'\r\n\t\t);\r\n\t} else if (pnlVal === 0 || pnlVal === null) {\r\n\t\tpnlVal = 0;\r\n\t\treturn \"<span> \" + pnlVal + \"</span>\";\r\n\t}\r\n\treturn (\r\n\t\t'<span class=\"text-success\"> ' +\r\n\t\tpnlVal +\r\n\t\t' <i class=\"mdi mdi-arrow-up\"></i></span>'\r\n\t);\r\n}\r\n\r\nexport function getSubPnlComponent(pnlVal) {\r\n\tpnlVal = parseFloat(pnlVal || 0).toFixed(2);\r\n\tif (pnlVal < 0) {\r\n\t\treturn (\r\n\t\t\t<span className=\"text-danger\">\r\n\t\t\t\t{\" \"}\r\n\t\t\t\t{pnlVal} <i className=\"mdi mdi-arrow-down\"></i>\r\n\t\t\t</span>\r\n\t\t);\r\n\t} else if (pnlVal === 0 || pnlVal === null) {\r\n\t\tpnlVal = 0;\r\n\t\treturn <span> {pnlVal}</span>;\r\n\t}\r\n\treturn (\r\n\t\t<span className=\"text-success\">\r\n\t\t\t{\" \"}\r\n\t\t\t{pnlVal} <i className=\"mdi mdi-arrow-up\"></i>\r\n\t\t</span>\r\n\t);\r\n}\r\n\r\nexport function getSubTransactionType(transaction) {\r\n\tlet a_type = transaction.charAt(0);\r\n\tlet is_buy =\r\n\t\ta_type === \"B\" ||\r\n\t\ta_type === \"b\" ||\r\n\t\ta_type === \"buy\" ||\r\n\t\ta_type === \"BUY\" ||\r\n\t\ta_type === \"Buy\";\r\n\tif (is_buy) {\r\n\t\treturn '<label className=\"badge badge-outline-warning\">BUY</label>';\r\n\t} else {\r\n\t\treturn '<label className=\"badge badge-outline-primary\">SELL</label>';\r\n\t}\r\n}\r\n\r\nexport function getOrderStatus(status, reason) {\r\n\tif (status.charAt(0) === \"C\" || status.charAt(0) === \"c\") {\r\n\t\treturn (\r\n\t\t\t<label\r\n\t\t\t\tclassName=\"badge badge-outline-success\"\r\n\t\t\t\tdata-toggle=\"tooltip\"\r\n\t\t\t\tdata-placement=\"top\"\r\n\t\t\t\ttitle={reason}\r\n\t\t\t>\r\n\t\t\t\tCOMPLETED\r\n\t\t\t</label>\r\n\t\t);\r\n\t} else if (status.charAt(0) === \"R\" || status.charAt(0) === \"r\") {\r\n\t\treturn (\r\n\t\t\t<label\r\n\t\t\t\tclassName=\"badge badge-outline-danger\"\r\n\t\t\t\tdata-toggle=\"tooltip\"\r\n\t\t\t\tdata-placement=\"top\"\r\n\t\t\t\ttitle={reason}\r\n\t\t\t>\r\n\t\t\t\tREJECTED\r\n\t\t\t</label>\r\n\t\t);\r\n\t\t//} else if (status.charAt(0) === \"O\" || status.charAt(0) === \"o\") {\r\n\t\t//   return (\r\n\t\t//     <label\r\n\t\t//       className=\"badge badge-outline-secondary\"\r\n\t\t//       data-toggle=\"tooltip\"\r\n\t\t//       data-placement=\"top\"\r\n\t\t//       title={reason}\r\n\t\t//     >\r\n\t\t//       OPEN\r\n\t\t//     </label>\r\n\t\t//   );\r\n\t} else {\r\n\t\treturn (\r\n\t\t\t<label\r\n\t\t\t\tclassName=\"badge badge-outline-info\"\r\n\t\t\t\tdata-toggle=\"tooltip\"\r\n\t\t\t\tdata-placement=\"top\"\r\n\t\t\t\ttitle={reason}\r\n\t\t\t>\r\n\t\t\t\t{status}\r\n\t\t\t</label>\r\n\t\t);\r\n\t}\r\n}\r\n\r\nexport function getConvertedAmount(amount) {\r\n\tif (isNaN(amount)) {\r\n\t\treturn \"...\";\r\n\t}\r\n\tif (amount < 1000) {\r\n\t\treturn amount.toString();\r\n\t}\r\n\tif (amount >= 1000 && amount < 100000) {\r\n\t\treturn (amount / 1000).toFixed(2) + \" K\";\r\n\t}\r\n\tif (amount >= 100000 && amount < 10000000) {\r\n\t\treturn (amount / 100000).toFixed(2) + \" L\";\r\n\t}\r\n\tif (amount >= 10000000) {\r\n\t\treturn (amount / 10000000).toFixed(2) + \" Cr\";\r\n\t}\r\n}\r\n\r\n\r\n\r\n// Function to create dark theme for DataTable\r\nexport const createDataTableDarkTheme = () => {\r\n\tcreateTheme(\r\n\t\t\"solarized\",\r\n\t\t{\r\n\t\t\tbackground: {\r\n\t\t\t\tdefault: \"transparent\",\r\n\t\t\t},\r\n\t\t\taction: {\r\n\t\t\t\tbutton: \"rgba(0,0,0,.54)\",\r\n\t\t\t\thover: \"rgba(0,0,0,.08)\",\r\n\t\t\t\tdisabled: \"rgba(0,0,0,.12)\",\r\n\t\t\t},\r\n\t\t},\r\n\t\t\"dark\"\r\n\t);\r\n}\r\n\r\n// Function to reset DataTable theme to default values\r\nexport const resetDataTableTheme = () => {\r\n\tcreateTheme(\r\n\t\t\"resesolarized\",\r\n\t\t{\r\n\t\t\tbackground: {\r\n\t\t\t\tdefault: \"#fff\",\r\n\t\t\t},\r\n\t\t\taction: {\r\n\t\t\t\tbutton: \"rgba(0,0,0,.54)\",\r\n\t\t\t\thover: \"rgba(0,0,0,.08)\",\r\n\t\t\t\tdisabled: \"rgba(0,0,0,.12)\",\r\n\t\t\t},\r\n\t\t},\r\n\t\t\"light\"\r\n\t);\r\n}\r\n\r\n// theme color for DATATABLE package\r\nexport const getTheme = () => {\r\n\tconst isDarkTheme = localStorage.getItem(\"isDarkTheme\") === \"true\";\r\n\r\n\tif (isDarkTheme) {\r\n\t\tcreateDataTableDarkTheme();\r\n\t\treturn \"solarized\";\r\n\t} else {\r\n\t\tresetDataTableTheme(); // Reset the theme when switching to light theme\r\n\t\treturn \"resesolarized\";\r\n\t}\r\n};\r\n\r\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nvar _excluded = [\"bsPrefix\", \"variant\", \"animation\", \"size\", \"children\", \"as\", \"className\"];\nimport classNames from 'classnames';\nimport React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nvar Spinner = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var bsPrefix = _ref.bsPrefix,\n      variant = _ref.variant,\n      animation = _ref.animation,\n      size = _ref.size,\n      children = _ref.children,\n      _ref$as = _ref.as,\n      Component = _ref$as === void 0 ? 'div' : _ref$as,\n      className = _ref.className,\n      props = _objectWithoutPropertiesLoose(_ref, _excluded);\n\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'spinner');\n  var bsSpinnerPrefix = bsPrefix + \"-\" + animation;\n  return /*#__PURE__*/React.createElement(Component, _extends({\n    ref: ref\n  }, props, {\n    className: classNames(className, bsSpinnerPrefix, size && bsSpinnerPrefix + \"-\" + size, variant && \"text-\" + variant)\n  }), children);\n});\nSpinner.displayName = 'Spinner';\nexport default Spinner;"], "names": ["WrappedComponent", "props", "history", "useHistory", "useEffect", "token", "localStorage", "getItem", "hostname", "window", "location", "login_url", "requestOptions", "method", "headers", "body", "JSON", "stringify", "fetch", "API_URL", "then", "response", "json", "data", "code", "href", "catch", "error", "console", "_jsx", "FailedOrders", "isDataLoaded", "setIsDataLoaded", "useState", "tableDataFormatted", "setTableDataFormatted", "rawUserMap", "useMemo", "Map", "order_id", "useParams", "setData", "selectedDate", "setSelectedDate", "Date", "setTableData", "tableData", "rowTableData", "map", "obj", "index", "push", "getRowFormatForTable", "ind", "ref_name", "trading_symbol", "exchange", "quantity", "price", "status", "emsg", "action_type", "getTransactionType", "order_time", "split", "view", "type", "className", "onClick", "open", "concat", "strategy_id", "children", "useQuery", "GetFailedOrdersData", "format", "res", "jsonArr", "Object", "values", "set", "id", "e", "log", "columns", "name", "selector", "row", "sortable", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "wrap", "sortFunction", "actionTypeSortFunction", "a", "b", "omit", "isDarkTheme", "useTheme", "_jsxs", "Form", "Group", "controlId", "DatePicker", "selected", "onChange", "date", "dateFormat", "<PERSON><PERSON>", "handleShowOrder", "Control", "placeholder", "search_val", "target", "value", "filteredData", "_value$ref_name", "_value$trading_symbol", "_value$order_id", "_value$order_id$toStr", "_value$emsg", "_value$exchange", "_value$price", "_value$quantity", "_value$action_type", "_value$order_time", "_value$order_time2", "toLowerCase", "includes", "toString", "required", "DataTable", "pagination", "paginationPerPage", "striped", "theme", "getTheme", "highlightOnHover", "<PERSON><PERSON><PERSON><PERSON>", "style", "height", "Spinner", "animation", "width", "<PERSON><PERSON><PERSON>", "customStylesForSelect", "option", "provided", "state", "backgroundColor", "isSelected", "color", "hoverEffectOnSelect", "base", "transaction", "a_type", "char<PERSON>t", "is_sell", "toUpperCase", "getSubPnlComponent", "pnlVal", "parseFloat", "toFixed", "getOrderStatus", "reason", "title", "getConvertedAmount", "amount", "isNaN", "createTheme", "background", "default", "action", "button", "hover", "disabled", "_excluded", "React", "_ref", "ref", "bsPrefix", "variant", "size", "_ref$as", "as", "Component", "_objectWithoutPropertiesLoose", "bsSpinnerPrefix", "useBootstrapPrefix", "_extends", "classNames", "displayName"], "sourceRoot": ""}
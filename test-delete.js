const { NodeSSH } = require('node-ssh');
const fs = require('fs-extra');
const chalk = require('chalk');

async function testDelete() {
    try {
        const config = JSON.parse(fs.readFileSync('sync-config.json', 'utf8'));
        const ssh = new NodeSSH();
        
        console.log(chalk.blue('Testing deletion on onepercentdaily.in...'));
        
        await ssh.connect({
            host: config.hostinger.host,
            username: config.hostinger.username,
            port: config.hostinger.port,
            password: config.hostinger.password
        });
        
        const domainPath = '/home/<USER>/domains/onepercentdaily.in';
        
        // Delete the specific file
        console.log(chalk.yellow('Deleting "do not upload here" file...'));
        await ssh.execCommand(`rm -f "${domainPath}/do not upload here"`);
        await ssh.execCommand(`rm -rf "${domainPath}/do not upload here"`);
        
        console.log(chalk.green('✓ File deleted from server!'));
        
        ssh.dispose();
        
    } catch (error) {
        console.error(chalk.red('Error:'), error.message);
    }
}

testDelete();

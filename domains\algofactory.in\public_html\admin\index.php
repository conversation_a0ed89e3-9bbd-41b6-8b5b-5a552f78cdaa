<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/permissions.php';

$auth = new Auth($pdo);

// Check authentication
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$currentUser = $auth->getCurrentUser();

// Only super admin can access this page
if ($currentUser['role'] !== 'super_admin') {
    header('Location: sub-admin.php');
    exit;
}

$permissions = new PermissionManager($pdo, $currentUser['id']);

// Get statistics
$stats = [];
try {
    // Count sub-admins
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM admin_users WHERE role = 'sub_admin' AND is_active = 1");
    $stmt->execute();
    $stats['sub_admins'] = $stmt->fetch()['count'];
    
    // Count total permissions
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM user_permissions");
    $stmt->execute();
    $stats['total_permissions'] = $stmt->fetch()['count'];
    
    // Recent sub-admins
    $stmt = $pdo->prepare("
        SELECT username, email, created_at 
        FROM admin_users 
        WHERE role = 'sub_admin' 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $stats['recent_sub_admins'] = $stmt->fetchAll();
    
} catch (Exception $e) {
    $stats['error'] = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Admin Dashboard - AlgoFactory</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.5rem;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-info span {
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            font-size: 0.9rem;
            transition: background 0.3s;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h3 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
            font-size: 1rem;
            font-weight: 500;
            transition: transform 0.3s;
            display: block;
        }
        
        .action-btn:hover {
            transform: translateY(-3px);
            color: white;
            text-decoration: none;
        }
        
        .recent-list {
            list-style: none;
        }
        
        .recent-list li {
            padding: 0.75rem 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .recent-list li:last-child {
            border-bottom: none;
        }
        
        .user-details {
            flex: 1;
        }
        
        .user-name {
            font-weight: 500;
            color: #333;
        }
        
        .user-email {
            font-size: 0.8rem;
            color: #666;
        }
        
        .user-date {
            font-size: 0.8rem;
            color: #999;
        }
        
        .quick-actions {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .quick-actions h3 {
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-error {
            background: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Super Admin Dashboard</h1>
        <div class="user-info">
            <span>👑 <?php echo htmlspecialchars($currentUser['username']); ?></span>
            <a href="logout.php" class="logout-btn">Logout</a>
        </div>
    </div>
    
    <div class="container">
        <?php if (isset($stats['error'])): ?>
            <div class="alert alert-error">
                Error loading statistics: <?php echo htmlspecialchars($stats['error']); ?>
            </div>
        <?php endif; ?>
        
        <div class="dashboard-grid">
            <div class="card">
                <h3>📊 System Overview</h3>
                <div class="stat-number"><?php echo $stats['sub_admins'] ?? 0; ?></div>
                <div class="stat-label">Active Sub Admins</div>
            </div>
            
            <div class="card">
                <h3>🔐 Permissions</h3>
                <div class="stat-number"><?php echo $stats['total_permissions'] ?? 0; ?></div>
                <div class="stat-label">Total Permissions Granted</div>
            </div>
            
            <div class="card">
                <h3>⚡ Quick Stats</h3>
                <div style="display: flex; justify-content: space-between; margin-top: 1rem;">
                    <div>
                        <div style="font-size: 1.5rem; font-weight: bold; color: #28a745;">✅ Active</div>
                        <div style="font-size: 0.8rem; color: #666;">System Status</div>
                    </div>
                    <div>
                        <div style="font-size: 1.5rem; font-weight: bold; color: #667eea;">🎯 Ready</div>
                        <div style="font-size: 0.8rem; color: #666;">API Status</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="action-buttons">
            <a href="create-sub-admin.php" class="action-btn">
                👥 Create New Sub Admin
            </a>
            <a href="manage-permissions.php" class="action-btn">
                🔐 Manage Permissions
            </a>
            <a href="view-sub-admins.php" class="action-btn">
                📋 View All Sub Admins
            </a>
            <a href="api/proxy.php?action=test" class="action-btn">
                🔗 Test API Connection
            </a>
        </div>
        
        <div class="quick-actions">
            <h3>👥 Recent Sub Admins</h3>
            <?php if (!empty($stats['recent_sub_admins'])): ?>
                <ul class="recent-list">
                    <?php foreach ($stats['recent_sub_admins'] as $subAdmin): ?>
                        <li>
                            <div class="user-details">
                                <div class="user-name"><?php echo htmlspecialchars($subAdmin['username']); ?></div>
                                <div class="user-email"><?php echo htmlspecialchars($subAdmin['email']); ?></div>
                            </div>
                            <div class="user-date">
                                <?php echo date('M j, Y', strtotime($subAdmin['created_at'])); ?>
                            </div>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php else: ?>
                <p style="color: #666; text-align: center; padding: 2rem;">
                    No sub admins created yet. <a href="create-sub-admin.php">Create your first sub admin</a>
                </p>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        // Auto-refresh stats every 30 seconds
        setInterval(function() {
            // You can add AJAX call here to refresh stats without page reload
        }, 30000);
        
        // Add click tracking for analytics
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                console.log('Action clicked:', this.textContent.trim());
            });
        });
    </script>
</body>
</html>

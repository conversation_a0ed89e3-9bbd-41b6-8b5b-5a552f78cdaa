const { NodeSSH } = require('node-ssh');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const inquirer = require('inquirer');

class HostingerDomainManager {
    constructor() {
        this.config = this.loadConfig();
        this.ssh = new NodeSSH();
        this.isConnected = false;
        this.domains = new Map();
    }

    loadConfig() {
        try {
            return JSON.parse(fs.readFileSync('sync-config.json', 'utf8'));
        } catch (error) {
            console.error(chalk.red('Error loading config:'), error.message);
            process.exit(1);
        }
    }

    saveConfig() {
        try {
            fs.writeFileSync('sync-config.json', JSON.stringify(this.config, null, 2));
        } catch (error) {
            console.error(chalk.red('Error saving config:'), error.message);
        }
    }

    async connect() {
        if (this.isConnected) return;

        try {
            console.log(chalk.blue('Connecting to Hostinger...'));
            
            const password = await inquirer.prompt([{
                type: 'password',
                name: 'password',
                message: 'Enter your SSH password:'
            }]);

            await this.ssh.connect({
                host: this.config.hostinger.host,
                username: this.config.hostinger.username,
                port: this.config.hostinger.port,
                password: password.password
            });
            
            this.isConnected = true;
            console.log(chalk.green('✓ Connected to Hostinger successfully!'));
            
        } catch (error) {
            console.error(chalk.red('Connection failed:'), error.message);
            throw error;
        }
    }

    async discoverDomains() {
        await this.connect();
        
        try {
            console.log(chalk.blue('Discovering domains...'));
            
            // List all directories in the domains folder
            const result = await this.ssh.execCommand('find /domains -maxdepth 1 -type d -name "*.*" 2>/dev/null || find /home/<USER>/public_html -maxdepth 1 -type d 2>/dev/null || ls -la /domains/ 2>/dev/null');
            
            if (result.stdout) {
                const domainPaths = result.stdout.split('\n').filter(line => line.trim());
                const domains = [];
                
                for (const domainPath of domainPaths) {
                    const domainName = path.basename(domainPath.trim());
                    if (domainName && domainName.includes('.')) {
                        domains.push({
                            name: domainName,
                            remotePath: domainPath.trim(),
                            localPath: path.join(this.config.hostinger.localBasePath, domainName)
                        });
                    }
                }
                
                if (domains.length === 0) {
                    // Try alternative discovery method
                    const altResult = await this.ssh.execCommand('ls -la /');
                    console.log(chalk.yellow('Domain discovery result:'));
                    console.log(altResult.stdout);
                    
                    // Manual domain entry
                    const manualDomains = await this.promptForDomains();
                    return manualDomains;
                }
                
                console.log(chalk.green(`Found ${domains.length} domains:`));
                domains.forEach(domain => {
                    console.log(chalk.cyan(`  • ${domain.name} → ${domain.remotePath}`));
                });
                
                return domains;
            }
            
        } catch (error) {
            console.error(chalk.red('Error discovering domains:'), error.message);
            return await this.promptForDomains();
        }
    }

    async promptForDomains() {
        console.log(chalk.yellow('Manual domain configuration required.'));
        
        const domains = [];
        let addMore = true;
        
        while (addMore) {
            const domainInfo = await inquirer.prompt([
                {
                    type: 'input',
                    name: 'name',
                    message: 'Enter domain name (e.g., example.com):',
                    validate: input => input.includes('.') || 'Please enter a valid domain name'
                },
                {
                    type: 'input',
                    name: 'remotePath',
                    message: 'Enter remote path (e.g., /domains/example.com/public_html):',
                    default: (answers) => `/domains/${answers.name}/public_html`
                }
            ]);
            
            domains.push({
                name: domainInfo.name,
                remotePath: domainInfo.remotePath,
                localPath: path.join(this.config.hostinger.localBasePath, domainInfo.name)
            });
            
            const continuePrompt = await inquirer.prompt([{
                type: 'confirm',
                name: 'continue',
                message: 'Add another domain?',
                default: false
            }]);
            
            addMore = continuePrompt.continue;
        }
        
        return domains;
    }

    async setupDomains() {
        const domains = await this.discoverDomains();
        
        // Update config with discovered domains
        this.config.domains = {};
        domains.forEach(domain => {
            this.config.domains[domain.name] = {
                remotePath: domain.remotePath,
                localPath: domain.localPath,
                enabled: true,
                lastSync: null
            };
        });
        
        this.saveConfig();
        
        // Create local directories
        for (const domain of domains) {
            await fs.ensureDir(domain.localPath);
            console.log(chalk.green(`✓ Created local directory: ${domain.localPath}`));
        }
        
        return domains;
    }

    async downloadDomain(domainName) {
        const domainConfig = this.config.domains[domainName];
        if (!domainConfig) {
            throw new Error(`Domain ${domainName} not found in config`);
        }

        await this.connect();
        
        console.log(chalk.blue(`Downloading ${domainName}...`));
        
        try {
            // Check if remote path exists
            const checkResult = await this.ssh.execCommand(`test -d "${domainConfig.remotePath}" && echo "exists"`);
            if (!checkResult.stdout.includes('exists')) {
                throw new Error(`Remote path does not exist: ${domainConfig.remotePath}`);
            }
            
            // Create local directory
            await fs.ensureDir(domainConfig.localPath);
            
            // Download files using SFTP
            await this.ssh.getDirectory(domainConfig.localPath, domainConfig.remotePath, {
                recursive: true,
                concurrency: 10,
                validate: (itemPath) => {
                    const relativePath = path.relative(domainConfig.remotePath, itemPath);
                    return !this.shouldIgnoreFile(relativePath);
                }
            });
            
            domainConfig.lastSync = new Date().toISOString();
            this.saveConfig();
            
            console.log(chalk.green(`✓ Downloaded ${domainName} successfully!`));
            
        } catch (error) {
            console.error(chalk.red(`Error downloading ${domainName}:`), error.message);
            throw error;
        }
    }

    async downloadAllDomains() {
        const domainNames = Object.keys(this.config.domains);
        
        for (const domainName of domainNames) {
            if (this.config.domains[domainName].enabled) {
                try {
                    await this.downloadDomain(domainName);
                } catch (error) {
                    console.error(chalk.red(`Failed to download ${domainName}:`), error.message);
                }
            }
        }
    }

    shouldIgnoreFile(filePath) {
        return this.config.hostinger.excludePatterns.some(pattern => {
            if (pattern.endsWith('/')) {
                return filePath.includes(pattern.slice(0, -1));
            }
            return filePath.includes(pattern) || filePath.match(pattern.replace('*', '.*'));
        });
    }

    async listDomains() {
        console.log(chalk.cyan('\n=== Your Domains ==='));
        
        Object.entries(this.config.domains).forEach(([name, config]) => {
            const status = config.enabled ? chalk.green('✓ Enabled') : chalk.red('✗ Disabled');
            const lastSync = config.lastSync ? new Date(config.lastSync).toLocaleString() : 'Never';
            
            console.log(`${chalk.yellow(name)}`);
            console.log(`  Status: ${status}`);
            console.log(`  Local:  ${config.localPath}`);
            console.log(`  Remote: ${config.remotePath}`);
            console.log(`  Last Sync: ${lastSync}`);
            console.log('');
        });
    }

    async disconnect() {
        if (this.isConnected) {
            this.ssh.dispose();
            this.isConnected = false;
            console.log(chalk.blue('Disconnected from Hostinger.'));
        }
    }
}

module.exports = HostingerDomainManager;

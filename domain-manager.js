const { NodeSSH } = require('node-ssh');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const inquirer = require('inquirer');

class HostingerDomainManager {
    constructor() {
        this.config = this.loadConfig();
        this.ssh = new NodeSSH();
        this.isConnected = false;
        this.domains = new Map();
    }

    loadConfig() {
        try {
            return JSON.parse(fs.readFileSync('sync-config.json', 'utf8'));
        } catch (error) {
            console.error(chalk.red('Error loading config:'), error.message);
            process.exit(1);
        }
    }

    saveConfig() {
        try {
            fs.writeFileSync('sync-config.json', JSON.stringify(this.config, null, 2));
        } catch (error) {
            console.error(chalk.red('Error saving config:'), error.message);
        }
    }

    async connect() {
        if (this.isConnected) return;

        try {
            console.log(chalk.blue('Connecting to Hostinger...'));
            
            const password = await inquirer.prompt([{
                type: 'password',
                name: 'password',
                message: 'Enter your SSH password:'
            }]);

            await this.ssh.connect({
                host: this.config.hostinger.host,
                username: this.config.hostinger.username,
                port: this.config.hostinger.port,
                password: password.password
            });
            
            this.isConnected = true;
            console.log(chalk.green('✓ Connected to Hostinger successfully!'));
            
        } catch (error) {
            console.error(chalk.red('Connection failed:'), error.message);
            throw error;
        }
    }

    async discoverDomains() {
        await this.connect();

        try {
            console.log(chalk.blue('Discovering ALL domains and directories...'));

            // First, let's check if there's a domains folder
            const domainsCheck = await this.ssh.execCommand('ls -la /domains 2>/dev/null || echo "not found"');

            let domains = [];

            if (domainsCheck.stdout && !domainsCheck.stdout.includes('not found')) {
                console.log(chalk.green('Found /domains directory! Listing all domains:'));
                console.log(domainsCheck.stdout);

                // Parse domains from /domains directory
                const lines = domainsCheck.stdout.split('\n');
                for (const line of lines) {
                    const parts = line.trim().split(/\s+/);
                    if (parts.length > 8 && parts[0].startsWith('d')) {
                        const dirName = parts[parts.length - 1];
                        if (dirName && dirName !== '.' && dirName !== '..') {
                            domains.push({
                                name: dirName,
                                remotePath: `/domains/${dirName}`,
                                localPath: path.join(this.config.hostinger.localBasePath, dirName)
                            });
                        }
                    }
                }
            } else {
                // Check user's home directory
                const userHome = `/home/<USER>
                console.log(chalk.yellow(`No /domains found. Checking ${userHome}...`));

                const result = await this.ssh.execCommand(`ls -la ${userHome}`);
                console.log(chalk.yellow('Your user directory contents:'));
                console.log(result.stdout);

                if (result.stdout) {
                    const lines = result.stdout.split('\n');
                    for (const line of lines) {
                        const parts = line.trim().split(/\s+/);
                        if (parts.length > 8 && parts[0].startsWith('d')) {
                            const dirName = parts[parts.length - 1];

                            // Include ALL directories except system ones
                            if (dirName &&
                                dirName !== '.' &&
                                dirName !== '..' &&
                                !dirName.startsWith('.') &&
                                dirName !== 'logs' &&
                                dirName !== 'tmp') {

                                domains.push({
                                    name: dirName,
                                    remotePath: `${userHome}/${dirName}`,
                                    localPath: path.join(this.config.hostinger.localBasePath, dirName)
                                });
                            }
                        }
                    }
                }
            }

            if (domains.length === 0) {
                console.log(chalk.yellow('No domains found automatically. Let\'s set them up manually.'));
                return await this.promptForDomains();
            }

            console.log(chalk.green(`Found ${domains.length} domains/directories:`));
            domains.forEach(domain => {
                console.log(chalk.cyan(`  • ${domain.name} → ${domain.remotePath}`));
            });

            // Ask user to confirm or modify
            const confirmDomains = await inquirer.prompt([{
                type: 'confirm',
                name: 'useFound',
                message: 'Download ALL these domains/directories with complete folder structure?',
                default: true
            }]);

            if (confirmDomains.useFound) {
                return domains;
            } else {
                // Let user select which ones to include
                const selectedDomains = await inquirer.prompt([{
                    type: 'checkbox',
                    name: 'domains',
                    message: 'Select domains to manage:',
                    choices: domains.map(d => ({ name: `${d.name} (${d.remotePath})`, value: d }))
                }]);

                return selectedDomains.domains;
            }

        } catch (error) {
            console.error(chalk.red('Error discovering domains:'), error.message);
            return await this.promptForDomains();
        }
    }

    async promptForDomains() {
        console.log(chalk.yellow('Manual domain configuration required.'));
        
        const domains = [];
        let addMore = true;
        
        while (addMore) {
            const domainInfo = await inquirer.prompt([
                {
                    type: 'input',
                    name: 'name',
                    message: 'Enter domain name (e.g., example.com):',
                    validate: input => input.includes('.') || 'Please enter a valid domain name'
                },
                {
                    type: 'input',
                    name: 'remotePath',
                    message: 'Enter remote path (e.g., /domains/example.com/public_html):',
                    default: (answers) => `/domains/${answers.name}/public_html`
                }
            ]);
            
            domains.push({
                name: domainInfo.name,
                remotePath: domainInfo.remotePath,
                localPath: path.join(this.config.hostinger.localBasePath, domainInfo.name)
            });
            
            const continuePrompt = await inquirer.prompt([{
                type: 'confirm',
                name: 'continue',
                message: 'Add another domain?',
                default: false
            }]);
            
            addMore = continuePrompt.continue;
        }
        
        return domains;
    }

    async setupDomains() {
        const domains = await this.discoverDomains();

        if (!domains || domains.length === 0) {
            console.log(chalk.yellow('No domains configured.'));
            return [];
        }

        // Update config with discovered domains
        this.config.domains = {};
        domains.forEach(domain => {
            this.config.domains[domain.name] = {
                remotePath: domain.remotePath,
                localPath: domain.localPath,
                enabled: true,
                lastSync: null
            };
        });

        this.saveConfig();

        // Create local directories
        for (const domain of domains) {
            await fs.ensureDir(domain.localPath);
            console.log(chalk.green(`✓ Created local directory: ${domain.localPath}`));
        }

        return domains;
    }

    async downloadDomain(domainName) {
        const domainConfig = this.config.domains[domainName];
        if (!domainConfig) {
            throw new Error(`Domain ${domainName} not found in config`);
        }

        await this.connect();

        console.log(chalk.blue(`Downloading complete folder structure for ${domainName}...`));

        try {
            // Check if remote path exists
            const checkResult = await this.ssh.execCommand(`test -d "${domainConfig.remotePath}" && echo "exists"`);
            if (!checkResult.stdout.includes('exists')) {
                throw new Error(`Remote path does not exist: ${domainConfig.remotePath}`);
            }

            // Show what we're about to download
            const sizeResult = await this.ssh.execCommand(`du -sh "${domainConfig.remotePath}" 2>/dev/null || echo "Size unknown"`);
            console.log(chalk.gray(`Domain size: ${sizeResult.stdout.trim()}`));

            // Create local directory
            await fs.ensureDir(domainConfig.localPath);

            console.log(chalk.yellow(`Downloading ALL files and folders from ${domainConfig.remotePath}...`));
            console.log(chalk.gray('This includes all subfolders, files, and directory structure.'));

            // Download files using SFTP with full recursive structure
            await this.ssh.getDirectory(domainConfig.localPath, domainConfig.remotePath, {
                recursive: true,
                concurrency: 5, // Reduced for stability
                validate: (itemPath) => {
                    const relativePath = path.relative(domainConfig.remotePath, itemPath);
                    const shouldDownload = !this.shouldIgnoreFile(relativePath);
                    if (!shouldDownload) {
                        console.log(chalk.gray(`Skipping: ${relativePath}`));
                    }
                    return shouldDownload;
                },
                tick: (localPath, remotePath, error) => {
                    if (error) {
                        console.log(chalk.red(`Error: ${localPath} - ${error.message}`));
                    } else {
                        const relativePath = path.relative(domainConfig.localPath, localPath);
                        console.log(chalk.green(`✓ Downloaded: ${relativePath}`));
                    }
                }
            });

            domainConfig.lastSync = new Date().toISOString();
            this.saveConfig();

            // Show final structure
            console.log(chalk.green(`\n✓ Downloaded ${domainName} successfully!`));
            console.log(chalk.cyan('Local folder structure:'));

            try {
                const { execSync } = require('child_process');
                const treeOutput = execSync(`dir "${domainConfig.localPath}" /s /b | findstr /v "__pycache__" | head -20`, { encoding: 'utf8' });
                console.log(treeOutput);
            } catch (e) {
                console.log(chalk.gray('Use Windows Explorer to browse the downloaded files.'));
            }

        } catch (error) {
            console.error(chalk.red(`Error downloading ${domainName}:`), error.message);
            throw error;
        }
    }

    async downloadAllDomains() {
        const domainNames = Object.keys(this.config.domains);
        
        for (const domainName of domainNames) {
            if (this.config.domains[domainName].enabled) {
                try {
                    await this.downloadDomain(domainName);
                } catch (error) {
                    console.error(chalk.red(`Failed to download ${domainName}:`), error.message);
                }
            }
        }
    }

    shouldIgnoreFile(filePath) {
        return this.config.hostinger.excludePatterns.some(pattern => {
            if (pattern.endsWith('/')) {
                return filePath.includes(pattern.slice(0, -1));
            }
            return filePath.includes(pattern) || filePath.match(pattern.replace('*', '.*'));
        });
    }

    async listDomains() {
        console.log(chalk.cyan('\n=== Your Domains ==='));
        
        Object.entries(this.config.domains).forEach(([name, config]) => {
            const status = config.enabled ? chalk.green('✓ Enabled') : chalk.red('✗ Disabled');
            const lastSync = config.lastSync ? new Date(config.lastSync).toLocaleString() : 'Never';
            
            console.log(`${chalk.yellow(name)}`);
            console.log(`  Status: ${status}`);
            console.log(`  Local:  ${config.localPath}`);
            console.log(`  Remote: ${config.remotePath}`);
            console.log(`  Last Sync: ${lastSync}`);
            console.log('');
        });
    }

    async disconnect() {
        if (this.isConnected) {
            this.ssh.dispose();
            this.isConnected = false;
            console.log(chalk.blue('Disconnected from Hostinger.'));
        }
    }
}

module.exports = HostingerDomainManager;

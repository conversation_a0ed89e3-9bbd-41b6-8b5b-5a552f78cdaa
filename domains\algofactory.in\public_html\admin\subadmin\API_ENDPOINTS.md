# AlgoFactory Admin API Endpoints & Role-Based Access System

Based on analysis of the current React application, here are the API endpoints used by the system:

## Proposed Role-Based Access Control Model

### User Hierarchy
```
Super Admin (You)
├── Sub Admin 1 (Strategy Manager)
│   ├── Can manage specific strategies only
│   └── Can view assigned user accounts
├── Sub Admin 2 (Account Manager)
│   ├── Can manage specific user accounts
│   └── Cannot access strategies
└── Sub Admin 3 (View Only)
    ├── Can only view assigned data
    └── Cannot edit anything
```

### Permission Model
```json
{
  "user_id": "number",
  "role": "super_admin|sub_admin|viewer",
  "permissions": {
    "strategies": {
      "view": ["strategy_id_1", "strategy_id_2", "*"],
      "edit": ["strategy_id_1"],
      "delete": [],
      "create": false
    },
    "users": {
      "view": ["user_id_1", "user_id_2"],
      "edit": ["user_id_1"],
      "delete": []
    },
    "brokers": {
      "view": ["broker_id_1"],
      "edit": [],
      "delete": []
    },
    "orders": {
      "view": true,
      "cancel": false,
      "execute": false
    },
    "positions": {
      "view": ["strategy_id_1"],
      "close": [],
      "add": false
    }
  }
}
```

## Base Configuration
- **Base URL**: `https://bpapil1.algodelta.com/api/v1`
- **Authentication**: JWT Token stored in `localStorage` as `admin_access_token`
- **Headers**: `Authorization: Bearer {token}`

## Authentication Endpoints

### Login
- **POST** `/auth/login`
- **Body**: 
  ```json
  {
    "email": "string",
    "password": "string", 
    "domain_name": "string"
  }
  ```

### Reset Password
- **POST** `/admin/resetpassword`
- **Body**:
  ```json
  {
    "old_password": "string",
    "new_password": "string"
  }
  ```

## Admin Management

### Get Constants
- **GET** `/admin/getconstants`
- **Description**: Fetches system constants and configuration

### Get Master Brokers
- **GET** `/admin/getmasterbroker`
- **Description**: Retrieves master broker information

### Get User Brokers
- **GET** `/admin/getuserbrokers`
- **Description**: Fetches all user broker accounts

### Get Users
- **GET** `/admin/getusers`
- **Description**: Retrieves all users in the system

### Get Masters
- **GET** `/admin/getmasters`
- **Description**: Fetches master account information

## Strategy Management

### Get Strategies
- **GET** `/admin/getstrategies`
- **Description**: Retrieves all trading strategies

### Create Strategy
- **POST** `/admin/createstrategy`
- **Body**:
  ```json
  {
    "name": "string",
    "description": "string",
    "required_amount": "number",
    "is_private": "boolean",
    "order_key": "string"
  }
  ```

### Update Trading Flag
- **POST** `/admin/updatetradingflag`
- **Body**:
  ```json
  {
    "strategy_id": "number",
    "trading_flag": "boolean"
  }
  ```

### Update Rejected Flag
- **POST** `/admin/updaterejectedflag`
- **Body**:
  ```json
  {
    "strategy_id": "number",
    "place_rejected": "boolean"
  }
  ```

### Connect Master to Strategy
- **POST** `/admin/connectmastertostrategy`
- **Body**:
  ```json
  {
    "strategy_id": "number",
    "broker_id": "number"
  }
  ```

### Disconnect Master from Strategy
- **POST** `/admin/disconnectmasterfromostrategy`
- **Body**:
  ```json
  {
    "strategy_id": "number"
  }
  ```

### Remove Strategy
- **POST** `/admin/removestrategy`
- **Body**:
  ```json
  {
    "strategy_id": "number"
  }
  ```

### Update Strategy Info
- **POST** `/admin/updatestartegyinfo`
- **Body**: Strategy update object

### Get Strategy Name
- **POST** `/admin/getstrategyname`
- **Body**:
  ```json
  {
    "strategy_id": "number"
  }
  ```

### Get Strategy Record
- **POST** `/admin/getstrategyrecord`
- **Body**:
  ```json
  {
    "strategy_id": "number"
  }
  ```

### Add Strategy Record
- **POST** `/admin/addstrategyrecord`
- **Body**: Strategy record object

### Update Strategy Record
- **POST** `/admin/updatestrategyrecord`
- **Body**: Strategy record update object

### Remove Strategy Record
- **POST** `/admin/removestrategyrecord`
- **Body**:
  ```json
  {
    "record_id": "number"
  }
  ```

## Strategy Pricing

### Add Strategy Price
- **POST** `/admin/addstrategyprice`
- **Body**: Price configuration object

### Get Strategy Price
- **POST** `/admin/getStrategyPrice`
- **Body**:
  ```json
  {
    "strategy_id": "number"
  }
  ```

### Remove Strategy Price
- **POST** `/admin/removestrategyprice`
- **Body**:
  ```json
  {
    "price_id": "number"
  }
  ```

## User Strategy Management

### Get Strategy Allowed Users
- **POST** `/admin/getstrategyallowedusers`
- **Body**:
  ```json
  {
    "strategy_id": "number"
  }
  ```

### Get Strategy Users
- **POST** `/admin/getstrategyusers`
- **Body**:
  ```json
  {
    "strategy_id": "number"
  }
  ```

### Allow User to Strategy
- **POST** `/admin/allowusertostrategy`
- **Body**: User strategy permission object

### Deny User to Strategy
- **POST** `/admin/denyusertostrategy`
- **Body**:
  ```json
  {
    "allowed_id": "number"
  }
  ```

### Subscribe User to Strategy
- **POST** `/admin/subscribeusertostrategy`
- **Body**: User subscription object

### User to Allow
- **POST** `/admin/usertoallow`
- **Body**:
  ```json
  {
    "strategy_id": "number"
  }
  ```

## Position Management

### Get Open Strategy Position
- **POST** `/admin/getopenstrategyposition`
- **Body**:
  ```json
  {
    "strategy_id": "number"
  }
  ```

### Get Close Strategy Position
- **POST** `/admin/getclosestrategyposition`
- **Body**:
  ```json
  {
    "strategy_id": "number"
  }
  ```

### Exit Strategy Position
- **POST** `/admin/exitstrategyposition`
- **Body**: Position exit object

### Add Position Entry
- **POST** `/admin/addpositionentry`
- **Body**:
  ```json
  {
    "strategy_id": "number",
    "symbol_type": "string",
    "symbol": "string", 
    "action_type": "string",
    "quantity": "number",
    "product_type": "string",
    "entry_price": "number",
    "exit_price": "number",
    "entry_time": "string",
    "exit_time": "string",
    "pnl": "number"
  }
  ```

### Delete Position
- **POST** `/admin/deleteposition`
- **Body**:
  ```json
  {
    "position_id": "number"
  }
  ```

## Broker Management

### Get Broker Info
- **POST** `/broker/getbrokerinfo`
- **Body**:
  ```json
  {
    "broker_id": "number"
  }
  ```

### Refresh Broker
- **POST** `/broker/refresh`
- **Body**:
  ```json
  {
    "broker_id": "number"
  }
  ```

### Reconnect Broker
- **POST** `/broker/reconnect`
- **Body**: Broker reconnection object

### Send Kotak Neo OTP
- **POST** `/broker/sendkotakneootp`
- **Body**: OTP request object

### Square Off Position
- **POST** `/broker/squereoffposition`
- **Body**:
  ```json
  {
    "broker_id": "number",
    "symbol": "string"
  }
  ```

### Cancel Order
- **POST** `/broker/cancelorder`
- **Body**:
  ```json
  {
    "broker_id": "number",
    "order_id": "string"
  }
  ```

### Execute Now
- **POST** `/broker/executenow`
- **Body**:
  ```json
  {
    "broker_id": "number", 
    "order_id": "string"
  }
  ```

## Order Management

### Get Orders
- **GET** `/admin/getorders`
- **Description**: Retrieves all orders

### Get Failed Orders
- **GET** `/admin/getfailedorders`
- **Description**: Retrieves failed orders

### Get Transactions
- **GET** `/admin/gettransactions`
- **Description**: Retrieves transaction history

## Symbol Management

### Get Symbols
- **POST** `/admin/getsymbols`
- **Body**:
  ```json
  {
    "symbol_type": "string",
    "search_term": "string"
  }
  ```

## Communication

### Follow Up
- **GET** `/admin/followup`
- **Description**: Retrieves follow-up data

### Communication
- **GET** `/admin/communication`
- **Description**: Retrieves communication data

## Configuration

### Get Config
- **GET** `/admin/getconfig`
- **Description**: Retrieves system configuration

### Bill History
- **GET** `/admin/billhistory`
- **Description**: Retrieves billing history

## Response Format

All endpoints return responses in the following format:

```json
{
  "status": boolean,
  "msg": "string",
  "data": object|array,
  "error": {
    "title": "string",
    "message": "string"
  }
}
```

## Authentication Notes

- All endpoints require authentication except `/auth/login`
- JWT token should be included in Authorization header
- Token is stored in localStorage as `admin_access_token`
- Domain-based authentication is used

## Error Handling

- HTTP status codes are used for response status
- Error messages are returned in the `msg` field
- Detailed error information is in the `error` object when available

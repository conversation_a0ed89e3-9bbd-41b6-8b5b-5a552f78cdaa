{"version": 3, "file": "static/js/581.a96d5f2b.chunk.js", "mappings": "sTAkBe,SAASA,EAAoBC,GAAQ,IAADC,EAClD,MAAOC,EAAkBC,IAAuBC,EAAAA,EAAAA,UAAS,CACxDC,MAAM,EACNC,IAAK,CAAC,KAEAC,EAAcC,IAAmBJ,EAAAA,EAAAA,UAAS,KAC1CK,EAAqBC,IAA0BN,EAAAA,EAAAA,UAAS,CAC9DO,QAAQ,EACRC,OAAQ,CAAC,KAEHC,EAAiBC,IAAsBV,EAAAA,EAAAA,UAAS,CACtDO,QAAQ,EACRI,IAAK,KACLC,IAAK,QAECC,EAAoBC,IAAyBd,EAAAA,EAAAA,UAAS,IACvDe,GAAaC,EAAAA,EAAAA,UAAQ,IAAM,IAAIC,KAAO,IA0D5C,SAASC,EAAkBC,EAAOR,EAAKT,GACtCS,EAAIS,OAASlB,EAAImB,KAAKD,OACtBT,EAAIW,UAAYpB,EAAImB,KAAKC,UACzBX,EAAIY,IAAMrB,EAAImB,KAAKE,IACnBZ,EAAIa,YAActB,EAAImB,KAAKG,YAC3B,IAAIC,EAAUC,EAA2BP,EAAOR,GAChDG,GAAuBa,IACtB,MAAMC,EAAc,IAAID,GAIxB,OAHIR,GAAS,GAAKA,EAAQS,EAAYC,SACrCD,EAAYT,GAASM,GAEfG,CAAW,GAEpB,EArEAE,EAAAA,EAAAA,YAAU,KAAO,IAADC,EACf,GAAoB,QAApBA,EAAInC,EAAMoC,kBAAU,IAAAD,GAAhBA,EAAkBV,KAAM,CAAC,IAADY,EAE3B,MAAMC,EAAUC,OAAOC,OAAuB,QAAjBH,EAACrC,EAAMoC,kBAAU,IAAAC,OAAA,EAAhBA,EAAkBZ,OAoElD,SAAsBgB,GACrB,IAAIC,EAAe,GACnBD,EAAUE,KAAI,CAAC5B,EAAKQ,KACnBmB,EAAaE,KAAKd,EAA2BP,EAAOR,GAAK,IAE1DG,EAAsBwB,EACvB,CAhCEG,CAAaP,GAKf,SAAuBA,GACtBA,EAAQK,KAAI,CAAC5B,EAAKQ,KACjBJ,EAAW2B,IAAI/B,EAAIgC,GAAIhC,EAAI,GAE7B,CAREiC,CAAcV,EACf,IACE,CAAiB,QAAjBrC,EAACD,EAAMoC,kBAAU,IAAAnC,OAAA,EAAhBA,EAAkBwB,QAwCcwB,EAAAA,EAAAA,aACnC,uBACCC,KACAC,EAAAA,EAAAA,IAAoBD,EAAInC,IAAIgC,IAC1BK,MAAM9C,KAEFA,EAAIK,SACP0C,EAAAA,EAAUC,QAAQ,GAADC,OAAIjD,EAAIkD,MACzBlC,EAAkB4B,EAAIlC,IAAKkC,EAAInC,IAAKT,GAC7BA,KAKRmD,OAAOC,IACPC,QAAQC,IAAIF,EAAE,GACb,IAhBL,MAqBMG,GAA2BZ,EAAAA,EAAAA,aAAY,gBAAgB,KAC5Da,EAAAA,EAAAA,IAAajD,EAAgBE,IAAIgC,IAC/BK,MAAM9C,IAhCT,IAA0BiB,EAiCnBjB,EAAIK,QACPQ,EAAW4C,OAAOlD,EAAgBE,IAAIgC,IACtC5C,EAAoB,CAAEE,MAAM,EAAMC,IAAKA,IAnCjBiB,EAoCLV,EAAgBG,IAnCpCE,GAAuBa,IACtB,MAAMC,EAAc,IAAID,GAExB,OADAC,EAAYgC,OAAOzC,EAAO,GACnBS,CAAW,KAkChB7B,EAAoB,CAAEE,MAAM,EAAMC,IAAKA,IAExCQ,EAAmB,CAAEH,QAAQ,EAAOI,IAAK,KAAMC,IAAK,MAAO,IAE3DyC,OAAOC,IACPvD,EAAoB,CAAEE,MAAM,EAAMC,IAAKoD,IAChCO,QAAQC,OAAOR,QAKnBS,GAA6BlB,EAAAA,EAAAA,aAAY,sBAAuBC,IACrEkB,EAAAA,EAAAA,IAAmBlB,EAAImB,cACrBjB,MAAM9C,GACFA,EAAIK,QACPW,EAAkB4B,EAAIlC,IAAKkC,EAAInC,IAAKT,GACpC+C,EAAAA,EAAUC,QAAQ,GAADC,OAAIjD,EAAIkD,MAElBlD,IAEPA,EAAIkD,KAAO,2CACXrD,EAAoB,CAAEE,MAAM,EAAMC,IAAKA,KAChC,KAGRmD,OAAOC,IACPvD,EAAoB,CAAEE,MAAM,EAAMC,IAAKoD,IACvCC,QAAQC,IAAI,WAAYF,EAAE,MAI7B,SAASY,EAAmBC,EAAKC,GAGhC,OAFkB,IAAIC,gBAAgBF,EAAIG,UAAUH,EAAII,QAAQ,OACrCC,IAAIJ,EAEhC,CA4HA,MAAOK,EAAUC,IAAe1E,EAAAA,EAAAA,UAAS,CAAEC,MAAM,EAAO0E,UAAW,CAAC,IA0BpE,SAASC,EAAqB9B,GAED,UAAxBA,EAAInC,IAAIkE,YAtJb,SAAoCC,GAEnC,MAAMzD,EAAO,CACZ0D,UAFaD,EAAQnE,IAAIqE,QAGzBC,aAAcC,EAAAA,GACdC,cAAe,OACfC,MAAO,UAIFC,EAAY,IAAIhB,gBAAgBhD,GAAMiE,WACtCnB,EAAG,GAAAhB,OAFM,+CAEM,KAAAA,OAAIkC,GACzB9B,QAAQC,IAAIW,GAEZ,MAAMoB,EAAQC,OAAOC,KAAKtB,EAAK,SAAU,4BAGnCuB,EAAaC,aAAY,KAC9B,GAAIJ,EAAMK,OACTC,cAAcH,QAEd,IACC,MAAMvB,EAAMoB,EAAMO,SAASC,KAE3B,GADAxC,QAAQC,IAAIW,GACRA,EAAI6B,SAAS,aAAc,CAC9B,IAAIC,EAAY/B,EAAmBC,EAAK,aACxCW,EAAQb,aAAe,CACtBiC,UAAWpB,EAAQnE,IAAIgC,GACvBwD,OAAQF,GAETlC,EAA2BqC,OAAOtB,GAClCe,cAAcH,GACdH,EAAMc,OACP,CACD,CAAE,MAAOC,GAER,CAEF,GACE,IACJ,CA+GEC,CAA2BzD,GACO,kBAAxBA,EAAInC,IAAIkE,YA9GpB,SAAsCC,GACrCvB,QAAQC,IAAIsB,GACZ,IAAIE,EAAUF,EAAQnE,IAAIqE,QAC1B,MAMMK,EAAY,IAAIhB,gBANT,CACZmC,EAAG,EACHxB,QAASA,IAIkCM,WACtCnB,EAAG,GAAAhB,OAFM,yCAEM,KAAAA,OAAIkC,GACzB9B,QAAQC,IAAIW,GAEZ,MAAMoB,EAAQC,OAAOC,KAAKtB,EAAK,SAAU,4BAGnCuB,EAAaC,aAAY,KAC9B,GAAIJ,EAAMK,OACTC,cAAcH,QAEd,IACC,MAAMvB,EAAMoB,EAAMO,SAASC,KAE3B,GADAxC,QAAQC,IAAIW,GACRA,EAAI6B,SAAS,iBAAkB,CAClC,IAAIC,EAAY/B,EAAmBC,EAAK,iBACxCW,EAAQb,aAAe,CACtBiC,UAAWpB,EAAQnE,IAAIgC,GACvBwD,OAAQF,GAETlC,EAA2BqC,OAAOtB,GAClCe,cAAcH,GACdH,EAAMc,OACP,CACD,CAAE,MAAOC,GAER,CAEF,GACE,IACJ,CAwEEG,CAA6B3D,GACK,cAAxBA,EAAInC,IAAIkE,YAvEpB,SAAwCC,GACvC,IAAIE,EAAUF,EAAQnE,IAAIqE,QAC1B,MAKMK,EAAY,IAAIhB,gBALT,CACZqC,QAAS1B,IAIkCM,WACtCnB,EAAG,GAAAhB,OAFM,4BAEM,KAAAA,OAAIkC,GACzB9B,QAAQC,IAAIW,GAEZ,MAAMoB,EAAQC,OAAOC,KAAKtB,EAAK,SAAU,4BAGnCuB,EAAaC,aAAY,KAC9B,GAAIJ,EAAMK,OACTC,cAAcH,QAEd,IACC,MAAMvB,EAAMoB,EAAMO,SAASC,KAE3B,GADAxC,QAAQC,IAAIW,GACRA,EAAI6B,SAAS,QAAS,CACzB,IAAIC,EAAY/B,EAAmBC,EAAK,QACxCW,EAAQb,aAAe,CACtBiC,UAAWpB,EAAQnE,IAAIgC,GACvBwD,OAAQF,GAETlC,EAA2BqC,OAAOtB,GAClCe,cAAcH,GACdH,EAAMc,OACP,CACD,CAAE,MAAOC,GAER,CAEF,GACE,IACJ,CAmCEK,CAA+B7D,IAE/BA,EAAImB,aAAe,CAClBiC,UAAWpD,EAAInC,IAAIgC,IAEpBoB,EAA2BqC,OAAOtD,GAEpC,CAEA,SAASpB,EAA2Bd,EAAKD,GACxC,IAAIiG,EAAW,CAAEhG,IAAKA,EAAKD,IAAKA,GAChC,MAAO,CACNQ,MAAOP,EAAM,EACbiG,YAAalG,EAAImG,WACjB1F,QAAST,EAAIS,QAAU,GAAK2F,QAAQ,GACpCxF,KAAKyF,EAAAA,EAAAA,KAAoBrG,EAAIY,KAAO,GAAKwF,QAAQ,IACjDzF,UAAWX,EAAIW,UAUf2F,OACCC,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACCF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,+BAGjBC,OACCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2BAA0BD,SAAA,EACxCF,EAAAA,EAAAA,KAACM,EAAAA,EAAM,CACNC,QAAQ,UACRJ,UAAU,MACVK,QAASA,IACRlC,OAAOC,KAAK,2BAADtC,OAA4BxC,EAAIgC,IAAM,UACjDyE,UAEDF,EAAAA,EAAAA,KAAA,KAAGG,UAAU,uCAEdH,EAAAA,EAAAA,KAACM,EAAAA,EAAM,CACNC,QAAQ,UACRJ,UAAU,WACVK,QAASA,IACRlC,OAAOC,KAAK,sBAADtC,OAAuBxC,EAAIgH,WAAa,UACnDP,UAEDF,EAAAA,EAAAA,KAAA,KAAGG,UAAU,0CAIhBO,YACCL,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2BAA0BD,SAAA,EACxCF,EAAAA,EAAAA,KAACM,EAAAA,EAAM,CACNC,QAAQ,OACRJ,UAAU,sBACV1E,GAAIhC,EAAIgC,GACR+E,QAASA,IAAM9C,EAAqBgC,GAAUQ,UAE9CF,EAAAA,EAAAA,KAAA,KAAGG,UAAU,yCAEO,IAApB1G,EAAIa,aACJ0F,EAAAA,EAAAA,KAACM,EAAAA,EAAM,CACNC,QAAQ,UACRJ,UAAU,WACVQ,MAAM,YACNC,UAAQ,EAAAV,UAERF,EAAAA,EAAAA,KAAA,KAAGG,UAAU,yCAGdH,EAAAA,EAAAA,KAACM,EAAAA,EAAM,CACNC,QAAQ,SACRJ,UAAU,mCACVK,QAASA,IACRpH,EAAuB,CAAEC,QAAQ,EAAMC,OAAQoG,IAEhDiB,MAAM,aAAYT,UAElBF,EAAAA,EAAAA,KAAA,KAAGG,UAAU,8CAKjBU,QACCb,EAAAA,EAAAA,KAAA,OAAKG,UAAU,2BAA0BD,UACxCF,EAAAA,EAAAA,KAACM,EAAAA,EAAM,CACNC,QAAQ,SACRJ,UAAU,sBACV1E,GAAIhC,EAAIgC,GACR+E,QAASA,IACRhH,EAAmB,CAAEH,QAAQ,EAAMI,IAAKA,EAAKC,IAAKA,IAClDwG,UAEDF,EAAAA,EAAAA,KAAA,KAAGG,UAAU,6CAIhB1G,IAAKA,EAEP,CAEA,MAkBMqH,EAAU,CAQf,CACCC,KAAM,KACNC,SAAWC,GAAQA,EAAIhH,MACvBiH,UAAU,EACVC,SAAU,OACVC,SAAU,QAEX,CACCL,KAAM,0BACNC,SAAWC,GAAQA,EAAItB,YACvBuB,UAAU,EACVC,SAAU,QACVC,SAAU,QACVC,MAAM,GAEP,CACCN,KAAM,SACNC,SAAWC,GAAQA,EAAI/G,OACvBgH,UAAU,EACVI,aA/ByBC,CAACC,EAAGC,IACvBC,WAAWF,EAAE/H,IAAIS,QAAUwH,WAAWD,EAAEhI,IAAIS,SAAW,EAAI,EA+BjEmH,MAAM,GAEP,CACCN,KAAM,MACNC,SAAWC,GAAQA,EAAI5G,IACvB6G,UAAU,EACVI,aA1CsBK,CAACH,EAAGC,IACpBC,WAAWF,EAAE/H,IAAIY,KAAOqH,WAAWD,EAAEhI,IAAIY,MAAQ,EAAI,GA2C5D,CAAE0G,KAAM,MAAOC,SAAWC,GAAQA,EAAI7G,UAAW8G,UAAU,GAQ3D,CACCH,KAAM,QACNC,SAAWC,GAAQA,EAAIb,MACvBc,UAAU,EACVC,SAAU,QACVC,SAAU,SAGX,CACCL,KAAM,aACNC,SAAWC,GAAQA,EAAIP,WACvBQ,UAAU,EACVI,aA1E6BM,CAACJ,EAAGC,KACR,IAAtBD,EAAE/H,IAAIa,cAA8C,IAAtBmH,EAAEhI,IAAIa,aAC/B,IAEiB,IAAtBkH,EAAE/H,IAAIa,aAAyBmH,EAAEhI,IAAIa,YACjC,GAsEP6G,SAAU,QACVC,SAAU,SAEX,CAAEL,KAAM,SAAUC,SAAWC,GAAQA,EAAIJ,OAAQK,UAAU,GAC3D,CAAEH,KAAM,MAAOC,SAAWC,GAAQA,EAAIxH,IAAKoI,MAAM,IAwClD,MAWM,YAAEC,IAAgBC,EAAAA,EAAAA,KACxB,OACC1B,EAAAA,EAAAA,MAAAJ,EAAAA,SAAA,CAAAC,SAAA,EACCG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBD,SAAA,EAShCF,EAAAA,EAAAA,KAACgC,EAAAA,EAAKC,MAAK,CAAA/B,UACVF,EAAAA,EAAAA,KAACgC,EAAAA,EAAKE,QAAO,CACZC,KAAK,OACLC,YAAY,SACZjC,UAAS,GAAAlE,OAAK6F,GAAe,oBAAmB,yBAChDO,SAhEiBjG,IACrB,IAAIkG,EAAalG,EAAEmG,OAAOC,MACtBC,EAAe,GACnB,IAAK,IAAID,KAAS3I,EAAWqB,SAAU,CAAC,IAADwH,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACtC,MACMC,GADSvD,EAAAA,EAAAA,KAAoB0C,EAAMnI,KAAO,GAAKwF,QAAQ,IAC9BnH,MAAMwH,SAAS,IAE5B,QAAjBwC,EAAAF,EAAM7E,mBAAW,IAAA+E,GAAjBA,EAAmBY,cAAcxE,SAAmB,OAAVwD,QAAU,IAAVA,OAAU,EAAVA,EAAYgB,gBACxC,QADsDX,EACpEH,EAAMe,gBAAQ,IAAAZ,GAAdA,EAAgBW,cAAcxE,SAAmB,OAAVwD,QAAU,IAAVA,OAAU,EAAVA,EAAYgB,gBACnC,QADiDV,EACjEJ,EAAM5C,kBAAU,IAAAgD,GAAhBA,EAAkBU,cAAcxE,SAAmB,OAAVwD,QAAU,IAAVA,OAAU,EAAVA,EAAYgB,gBACpC,QADkDT,EACnEL,EAAM7C,mBAAW,IAAAkD,GAAjBA,EAAmBS,cAAcxE,SAAmB,OAAVwD,QAAU,IAAVA,OAAU,EAAVA,EAAYgB,gBAC1C,QADwDR,EACpEN,EAAMtI,cAAM,IAAA4I,GACC,QADDC,EAAZD,EACG1E,kBAAU,IAAA2E,GADbA,EAEGO,cACDxE,SAAmB,OAAVwD,QAAU,IAAVA,OAAU,EAAVA,EAAYgB,gBACd,QAD4BN,EACrCR,EAAMnI,WAAG,IAAA2I,GACI,QADJC,EAATD,EACG5E,kBAAU,IAAA6E,GADbA,EAEGK,cACDxE,SAAmB,OAAVwD,QAAU,IAAVA,OAAU,EAAVA,EAAYgB,gBACR,OAAfD,QAAe,IAAfA,GACa,QADEH,EAAfG,EACGjF,kBAAU,IAAA8E,GADbA,EAEGI,cACDxE,SAAmB,OAAVwD,QAAU,IAAVA,OAAU,EAAVA,EAAYgB,gBACR,QADsBH,EACrCX,EAAMpI,iBAAS,IAAA+I,GACF,QADEC,EAAfD,EACG/E,kBAAU,IAAAgF,GADbA,EAEGE,cACDxE,SAAmB,OAAVwD,QAAU,IAAVA,OAAU,EAAVA,EAAYgB,iBAEvBb,EAAanH,KAAKkH,EAEpB,CACA5I,EACC6I,EAAapH,KAAI,CAAC5B,EAAKQ,IAAUO,EAA2BP,EAAOR,KACnE,EAgCG+J,UAAQ,OAGVxD,EAAAA,EAAAA,KAACyD,EAAAA,GAAS,CACT3C,QAASA,EACT3G,KAAMR,EACN+J,YAAY,EACZC,kBAAmB,GACnBC,kBAAgB,EAChBC,UAAQ,EACRC,OAAOC,EAAAA,EAAAA,MAEPC,yBAAuB,EACvBC,sBAAwBhD,GAAQA,EAAIiD,SACpCC,qBA3CkBjG,IAEfA,EAAMkG,YAAY,IA2CrBxL,EAAiBG,OACjBiH,EAAAA,EAAAA,KAACqE,EAAAA,EAAa,CACbrL,IAAKJ,EAAiBI,IACtBH,oBAAqBA,EACrBqD,IAAKtD,EAAiBI,IAAIkD,MAG3B/C,EAAoBE,SACpB2G,EAAAA,EAAAA,KAACsE,EAAAA,EAA6B,CAC7BlL,uBAAwBA,EACxBD,oBAAqBA,EACrBoL,QAASpL,EAAoBG,OAC7BT,oBAAqBA,EACrBmB,kBAAmBA,IAGpBT,EAAgBF,SAChBgH,EAAAA,EAAAA,MAACmE,EAAAA,EAAK,CAACzL,MAAM,EAAM0L,WAAW,EAAMC,KAAK,KAAKvE,UAAS,GAAAlE,OAAK6F,GAAe,aAAY,SAAQ5B,SAAA,EAC9FF,EAAAA,EAAAA,KAACwE,EAAAA,EAAMG,OAAM,CAACxE,UAAU,mBAAkBD,UACzCF,EAAAA,EAAAA,KAACwE,EAAAA,EAAMI,MAAK,CAACzE,UAAU,cAAaD,UACnCF,EAAAA,EAAAA,KAAA,MAAIG,UAAU,OAAMD,SAAC,4BAGvBF,EAAAA,EAAAA,KAACwE,EAAAA,EAAMK,KAAI,CAAC1E,UAAU,OAAMD,SAAC,qCAG7BG,EAAAA,EAAAA,MAACmE,EAAAA,EAAMM,OAAM,CAAC3E,UAAU,qCAAoCD,SAAA,EAC3DF,EAAAA,EAAAA,KAAA,OAAAE,UACCF,EAAAA,EAAAA,KAACM,EAAAA,EAAM,CACNC,QAAQ,iBACRC,QAASA,IAAMhH,EAAmB,CAAEH,QAAQ,EAAOoC,GAAI,KAAMyE,SAC7D,UAIFF,EAAAA,EAAAA,KAAA,OAAAE,UACCF,EAAAA,EAAAA,KAACM,EAAAA,EAAM,CACNC,QAAQ,kBACRJ,UAAU,YACVK,QAASA,IAAMjE,EAAyB2C,SAASgB,SACjD,oBAQNF,EAAAA,EAAAA,KAAC+E,EAAAA,EAA2B,CAC3BhM,KAAMwE,EAASxE,KACf0E,UAAWF,EAASE,UACpBuH,iBAAkBnI,MAItB,E,8QCxoBA,SAASoI,IACR,MAAOrM,EAAkBC,IAAuBC,EAAAA,EAAAA,UAAS,CACxDC,MAAM,EACNC,IAAK,CAAC,KAEAkM,EAAYC,IAAiBrM,EAAAA,EAAAA,WAAS,IACtCgC,EAAYsK,IAAiBtM,EAAAA,EAAAA,UAAS,OACzBuM,EAAAA,EAAAA,mBAoBpBzK,EAAAA,EAAAA,YAAU,KAjBTuK,GAAc,IACdG,EAAAA,EAAAA,MACExJ,MAAM9C,IACNmM,GAAc,KACVnM,EAAIK,SACP+L,EAAcpM,GACPA,MAKRmD,OAAOC,IACPC,QAAQC,IAAI,WAAYF,GACxB+I,GAAc,EAAM,GAKL,GACf,IAEH,MAAM,YAAErD,IAAgBC,EAAAA,EAAAA,KAExB,OACC1B,EAAAA,EAAAA,MAAA,OAAAH,SAAA,EACCF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,cAAaD,UAC3BF,EAAAA,EAAAA,KAAA,MAAIG,UAAS,cAAAlE,OAAgB6F,GAAe,mBAAoB5B,SAAC,mBAElEF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,MAAKD,UACnBF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,qCAAoCD,UAClDF,EAAAA,EAAAA,KAAA,OAAKG,UAAS,QAAAlE,OAAU6F,GAAe,mBAAoB5B,UAC1DF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,YAAWD,SACxBgF,GACAlF,EAAAA,EAAAA,KAAA,OACCG,UAAU,mDACVoF,MAAO,CAAEC,OAAQ,QAAStF,UAE1BF,EAAAA,EAAAA,KAACyF,EAAAA,EAAO,CACPhB,UAAU,SACVc,MAAO,CAAEG,MAAO,OAAQF,OAAQ,aAIlCxF,EAAAA,EAAAA,KAACvH,EAAAA,EAAmB,CAACqC,WAAYA,YAOrClC,EAAiBG,OACjBiH,EAAAA,EAAAA,KAACqE,EAAAA,EAAa,CACbrL,IAAKJ,EAAiBI,IACtBH,oBAAqBA,EACrBqD,IAAKtD,EAAiBI,IAAIkD,QAK/B,E,gCAEA,SAAeyJ,EAAAA,EAAAA,GAASV,G,qHCzFpBW,EAAY,CAAC,WAAY,UAAW,YAAa,OAAQ,WAAY,KAAM,aAI3EH,EAAuBI,EAAAA,YAAiB,SAAUC,EAAMC,GAC1D,IAAIC,EAAWF,EAAKE,SAChBzF,EAAUuF,EAAKvF,QACfkE,EAAYqB,EAAKrB,UACjBC,EAAOoB,EAAKpB,KACZxE,EAAW4F,EAAK5F,SAChB+F,EAAUH,EAAKI,GACfC,OAAwB,IAAZF,EAAqB,MAAQA,EACzC9F,EAAY2F,EAAK3F,UACjBzH,GAAQ0N,EAAAA,EAAAA,GAA8BN,EAAMF,GAG5CS,GADJL,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,YACP,IAAMvB,EACvC,OAAoBoB,EAAAA,cAAoBM,GAAWI,EAAAA,EAAAA,GAAS,CAC1DR,IAAKA,GACJrN,EAAO,CACRyH,UAAWqG,IAAWrG,EAAWkG,EAAiB3B,GAAQ2B,EAAkB,IAAM3B,EAAMnE,GAAW,QAAUA,KAC3GL,EACN,IACAuF,EAAQgB,YAAc,UACtB,S", "sources": ["app/components/table/UserBrokerDataTable.jsx", "app/user-pages/user-broker/UserBrokerManagement.jsx", "../node_modules/react-bootstrap/esm/Spinner.js"], "sourcesContent": ["import React, { useState, useMemo, useEffect } from \"react\";\r\nimport {\r\n\tRefreshMasterBroker,\r\n\tRefreshBrokerToken,\r\n\tRemoveBroker,\r\n\tSendDataForKotakOtp,\r\n} from \"../../../services/backendServices\";\r\nimport DataTable from \"react-data-table-component\";\r\nimport { useMutation } from \"react-query\";\r\nimport { Button, Form, Modal } from \"react-bootstrap\";\r\nimport { getSubPnlComponent, getTheme } from \"../../user-pages/ui-helper\";\r\nimport ResponseModal from \"../modal/ResponseModal\";\r\nimport { TOKEN_URL } from \"../../../Util/constant\";\r\nimport { BrokerConnectionModalForTable } from \"../modal/BrokerConnectionModelForTable\";\r\nimport OTPForBrokerConnectionModel from \"../modal/OTPForBrokerConnectionModel\";\r\nimport cogoToast from \"cogo-toast\";\r\nimport { useTheme } from \"../../context/ThemeContext\";\r\n\r\nexport default function UserBrokerDataTable(props) {\r\n\tconst [apiResponseModal, setApiResponseModal] = useState({\r\n\t\tshow: false,\r\n\t\tres: {},\r\n\t}); // Modal : on API Response\r\n\tconst [squareOfUser, setSquareOfUser] = useState([]);\r\n\tconst [showConnectionPopup, setShowConnectionPopup] = useState({\r\n\t\tstatus: false,\r\n\t\tRowObj: {},\r\n\t});\r\n\tconst [showDeletePopup, setShowDeletePopup] = useState({\r\n\t\tstatus: false,\r\n\t\tobj: null,\r\n\t\tind: null,\r\n\t});\r\n\tconst [tableDataFormatted, setTableDataFormatted] = useState([]);\r\n\tconst rawUserMap = useMemo(() => new Map(), []);\r\n\r\n\tuseEffect(() => {\r\n\t\tif (props.brokerData?.data) {\r\n\t\t\t// console.log(props.brokerData?.data);\r\n\t\t\tconst jsonArr = Object.values(props.brokerData?.data);\r\n\t\t\t// const jsonArr = Object.values([\r\n\t\t\t//   {\r\n\t\t\t//     id: 138,\r\n\t\t\t//     broker_name: \"angelone\",\r\n\t\t\t//     trading_flag: true,\r\n\t\t\t//     broker_username: \"S658742\",\r\n\t\t\t//     parent_id: 298,\r\n\t\t\t//     nic_name: \"Sejpalsinh\",\r\n\t\t\t//     demat_name: \"Sejpalsinh - S658742 - angelone\",\r\n\t\t\t//     broker_flag: true,\r\n\t\t\t//     pnl: 418.5,\r\n\t\t\t//     positions: 0,\r\n\t\t\t//     margin: 4705.73,\r\n\t\t\t//   },\r\n\t\t\t//   {\r\n\t\t\t//     id: 139,\r\n\t\t\t//     broker_name: \"angelone\",\r\n\t\t\t//     trading_flag: true,\r\n\t\t\t//     broker_username: \"S658742\",\r\n\t\t\t//     parent_id: 298,\r\n\t\t\t//     nic_name: \"Sejpalsinh\",\r\n\t\t\t//     demat_name: \"Sejpalsinh - S658742 - angelone\",\r\n\t\t\t//     broker_flag: true,\r\n\t\t\t//     pnl: 418.5,\r\n\t\t\t//     positions: 0,\r\n\t\t\t//     margin: 4705.73,\r\n\t\t\t//   },\r\n\t\t\t//   {\r\n\t\t\t//     id: 140,\r\n\t\t\t//     broker_name: \"angelone\",\r\n\t\t\t//     trading_flag: true,\r\n\t\t\t//     broker_username: \"S658742\",\r\n\t\t\t//     parent_id: 298,\r\n\t\t\t//     nic_name: \"Sejpalsinh\",\r\n\t\t\t//     demat_name: \"Sejpalsinh - S658742 - angelone\",\r\n\t\t\t//     broker_flag: true,\r\n\t\t\t//     pnl: 418.5,\r\n\t\t\t//     positions: 0,\r\n\t\t\t//     margin: 4705.73,\r\n\t\t\t//   },\r\n\t\t\t// ]);\r\n\t\t\tsetTableData(jsonArr); // function call\r\n\t\t\tsetRawResData(jsonArr); // function call\r\n\t\t}\r\n\t}, [props.brokerData?.data]);\r\n\r\n\tfunction setRawResData(jsonArr) {\r\n\t\tjsonArr.map((obj, index) => {\r\n\t\t\trawUserMap.set(obj.id, obj);\r\n\t\t});\r\n\t}\r\n\r\n\tfunction updateAccountInfo(index, obj, res) {\r\n\t\tobj.margin = res.data.margin;\r\n\t\tobj.positions = res.data.positions;\r\n\t\tobj.pnl = res.data.pnl;\r\n\t\tobj.broker_flag = res.data.broker_flag;\r\n\t\tvar newData = getRowFormatForBrokerTable(index, obj);\r\n\t\tsetTableDataFormatted((prevData) => {\r\n\t\t\tconst updatedData = [...prevData];\r\n\t\t\tif (index >= 0 && index < updatedData.length) {\r\n\t\t\t\tupdatedData[index] = newData;\r\n\t\t\t}\r\n\t\t\treturn updatedData;\r\n\t\t});\r\n\t}\r\n\r\n\tfunction setTableData(tableData) {\r\n\t\tvar rowTableData = [];\r\n\t\ttableData.map((obj, index) => {\r\n\t\t\trowTableData.push(getRowFormatForBrokerTable(index, obj));\r\n\t\t});\r\n\t\tsetTableDataFormatted(rowTableData);\r\n\t}\r\n\r\n\tfunction removeRowByIndex(index) {\r\n\t\tsetTableDataFormatted((prevData) => {\r\n\t\t\tconst updatedData = [...prevData];\r\n\t\t\tupdatedData.splice(index, 1);\r\n\t\t\treturn updatedData;\r\n\t\t});\r\n\t}\r\n\r\n\t// API CALL to Refresh\r\n\tconst RefreshMasterBrokerMutation = useMutation(\r\n\t\t\"RefreshMasterBroker\",\r\n\t\t(val) => {\r\n\t\t\tRefreshMasterBroker(val.obj.id)\r\n\t\t\t\t.then((res) => {\r\n\t\t\t\t\t// console.log(\"res\", res);\r\n\t\t\t\t\tif (res.status) {\r\n\t\t\t\t\t\tcogoToast.success(`${res.msg}`);\r\n\t\t\t\t\t\tupdateAccountInfo(val.ind, val.obj, res);\r\n\t\t\t\t\t\treturn res;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((e) => {\r\n\t\t\t\t\tconsole.log(e);\r\n\t\t\t\t});\r\n\t\t}\r\n\t);\r\n\r\n\t// API CALL to Remove Broker\r\n\tconst RemoveUserBrokerMutation = useMutation(\"RemoveBroker\", () =>\r\n\t\tRemoveBroker(showDeletePopup.obj.id)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\trawUserMap.delete(showDeletePopup.obj.id);\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\tremoveRowByIndex(showDeletePopup.ind);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t}\r\n\t\t\t\tsetShowDeletePopup({ status: false, obj: null, ind: null });\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t\treturn Promise.reject(e); // wrap in Promise\r\n\t\t\t})\r\n\t);\r\n\r\n\t// API CALL to Refresh Broker\r\n\tconst RefreshBrokerTokenmutation = useMutation(\"RefreshBrokerToken\", (val) =>\r\n\t\tRefreshBrokerToken(val.refresh_data)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tupdateAccountInfo(val.ind, val.obj, res);\r\n\t\t\t\t\tcogoToast.success(`${res.msg}`);\r\n\t\t\t\t\t// setApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tres.msg += \" Click on Re-Connect update credentials.\";\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t})\r\n\t);\r\n\r\n\tfunction getAuthCodeFromURL(url, auth_key) {\r\n\t\tconst urlParams = new URLSearchParams(url.substring(url.indexOf(\"?\")));\r\n\t\tconst authCode = urlParams.get(auth_key);\r\n\t\treturn authCode;\r\n\t}\r\n\r\n\tfunction handleFyersTokenGeneration(val_obj) {\r\n\t\tvar api_key = val_obj.obj.api_key;\r\n\t\tconst data = {\r\n\t\t\tclient_id: api_key,\r\n\t\t\tredirect_uri: TOKEN_URL,\r\n\t\t\tresponse_type: \"code\",\r\n\t\t\tstate: \"sample\",\r\n\t\t};\r\n\r\n\t\tconst apiUrl = \"http://api.fyers.in/api/v2/generate-authcode\";\r\n\t\tconst urlParams = new URLSearchParams(data).toString();\r\n\t\tconst url = `${apiUrl}?${urlParams}`;\r\n\t\tconsole.log(url);\r\n\t\t// Open the popup window with specified dimensions\r\n\t\tconst popup = window.open(url, \"newwin\", \"height=700px,width=700px\");\r\n\r\n\t\t// Check for the token in the callback URL\r\n\t\tconst checkToken = setInterval(() => {\r\n\t\t\tif (popup.closed) {\r\n\t\t\t\tclearInterval(checkToken);\r\n\t\t\t} else {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst url = popup.location.href;\r\n\t\t\t\t\tconsole.log(url);\r\n\t\t\t\t\tif (url.includes(\"auth_code\")) {\r\n\t\t\t\t\t\tvar auth_code = getAuthCodeFromURL(url, \"auth_code\");\r\n\t\t\t\t\t\tval_obj.refresh_data = {\r\n\t\t\t\t\t\t\tbroker_id: val_obj.obj.id,\r\n\t\t\t\t\t\t\tapp_id: auth_code,\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tRefreshBrokerTokenmutation.mutate(val_obj);\r\n\t\t\t\t\t\tclearInterval(checkToken);\r\n\t\t\t\t\t\tpopup.close();\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\t// Handle error accessing popup location, if necessary\r\n\t\t\t\t\t// console.log('Error accessing popup location:', error);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}, 500);\r\n\t}\r\n\r\n\tfunction handleZerodhaTokenGeneration(val_obj) {\r\n\t\tconsole.log(val_obj);\r\n\t\tvar api_key = val_obj.obj.api_key;\r\n\t\tconst data = {\r\n\t\t\tv: 3,\r\n\t\t\tapi_key: api_key,\r\n\t\t};\r\n\r\n\t\tconst apiUrl = \"https://kite.zerodha.com/connect/login\";\r\n\t\tconst urlParams = new URLSearchParams(data).toString();\r\n\t\tconst url = `${apiUrl}?${urlParams}`;\r\n\t\tconsole.log(url);\r\n\t\t// Open the popup window with specified dimensions\r\n\t\tconst popup = window.open(url, \"newwin\", \"height=700px,width=700px\");\r\n\r\n\t\t// Check for the token in the callback URL\r\n\t\tconst checkToken = setInterval(() => {\r\n\t\t\tif (popup.closed) {\r\n\t\t\t\tclearInterval(checkToken);\r\n\t\t\t} else {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst url = popup.location.href;\r\n\t\t\t\t\tconsole.log(url);\r\n\t\t\t\t\tif (url.includes(\"request_token\")) {\r\n\t\t\t\t\t\tvar auth_code = getAuthCodeFromURL(url, \"request_token\");\r\n\t\t\t\t\t\tval_obj.refresh_data = {\r\n\t\t\t\t\t\t\tbroker_id: val_obj.obj.id,\r\n\t\t\t\t\t\t\tapp_id: auth_code,\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tRefreshBrokerTokenmutation.mutate(val_obj);\r\n\t\t\t\t\t\tclearInterval(checkToken);\r\n\t\t\t\t\t\tpopup.close();\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\t// Handle error accessing popup location, if necessary\r\n\t\t\t\t\t// console.log('Error accessing popup location:', error);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}, 500);\r\n\t}\r\n\r\n\tfunction handleFlattradeTokenGeneration(val_obj) {\r\n\t\tvar api_key = val_obj.obj.api_key;\r\n\t\tconst data = {\r\n\t\t\tapp_key: api_key,\r\n\t\t};\r\n\r\n\t\tconst apiUrl = \"https://auth.flattrade.in\";\r\n\t\tconst urlParams = new URLSearchParams(data).toString();\r\n\t\tconst url = `${apiUrl}?${urlParams}`;\r\n\t\tconsole.log(url);\r\n\t\t// Open the popup window with specified dimensions\r\n\t\tconst popup = window.open(url, \"newwin\", \"height=700px,width=700px\");\r\n\r\n\t\t// Check for the token in the callback URL\r\n\t\tconst checkToken = setInterval(() => {\r\n\t\t\tif (popup.closed) {\r\n\t\t\t\tclearInterval(checkToken);\r\n\t\t\t} else {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst url = popup.location.href;\r\n\t\t\t\t\tconsole.log(url);\r\n\t\t\t\t\tif (url.includes(\"code\")) {\r\n\t\t\t\t\t\tvar auth_code = getAuthCodeFromURL(url, \"code\");\r\n\t\t\t\t\t\tval_obj.refresh_data = {\r\n\t\t\t\t\t\t\tbroker_id: val_obj.obj.id,\r\n\t\t\t\t\t\t\tapp_id: auth_code,\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tRefreshBrokerTokenmutation.mutate(val_obj);\r\n\t\t\t\t\t\tclearInterval(checkToken);\r\n\t\t\t\t\t\tpopup.close();\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\t// Handle error accessing popup location, if necessary\r\n\t\t\t\t\t// console.log('Error accessing popup location:', error);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}, 500);\r\n\t}\r\n\r\n\tconst [otpModel, setOtpModel] = useState({ show: false, form_data: {} });\r\n\t// currently not in use\r\n\tasync function handleKotakNeoTokenGeneration(val_obj) {\r\n\t\tconst form_data = val_obj.obj;\r\n\t\tconst neo_data = await SendDataForKotakOtp(form_data);\r\n\t\tif (neo_data.status) {\r\n\t\t\tform_data.broker_id = form_data.id;\r\n\t\t\tform_data.is_refresh = true;\r\n\t\t\tform_data.broker_name = \"kotakneo\";\r\n\t\t\tform_data.auth_token = neo_data.auth_token;\r\n\t\t\tform_data.neo_sid = neo_data.neo_sid;\r\n\t\t\tform_data.neo_userid = neo_data.neo_userid;\r\n\t\t\tform_data.login_auth_token = neo_data.login_auth_token;\r\n\t\t\tsetOtpModel({\r\n\t\t\t\tshow: true,\r\n\t\t\t\tform_data: {\r\n\t\t\t\t\tind: val_obj.ind,\r\n\t\t\t\t\tobj: val_obj.obj,\r\n\t\t\t\t\trefresh_data: form_data,\r\n\t\t\t\t},\r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\tsetApiResponseModal({ show: true, res: neo_data });\r\n\t\t}\r\n\t}\r\n\r\n\tfunction Refresh_broker_token(val) {\r\n\t\t// console.log(val)\r\n\t\tif (val.obj.broker_name === \"fyers\") {\r\n\t\t\thandleFyersTokenGeneration(val);\r\n\t\t} else if (val.obj.broker_name === \"zerodhamaster\") {\r\n\t\t\thandleZerodhaTokenGeneration(val);\r\n\t\t} else if (val.obj.broker_name === \"flattrade\") {\r\n\t\t\thandleFlattradeTokenGeneration(val);\r\n\t\t} else {\r\n\t\t\tval.refresh_data = {\r\n\t\t\t\tbroker_id: val.obj.id,\r\n\t\t\t};\r\n\t\t\tRefreshBrokerTokenmutation.mutate(val);\r\n\t\t}\r\n\t}\r\n\r\n\tfunction getRowFormatForBrokerTable(ind, obj) {\r\n\t\tvar pass_val = { ind: ind, obj: obj };\r\n\t\treturn {\r\n\t\t\tindex: ind + 1,\r\n\t\t\tbroker_user: obj.demat_name,\r\n\t\t\tmargin: (obj.margin || 0.0).toFixed(2),\r\n\t\t\tpnl: getSubPnlComponent((obj.pnl || 0.0).toFixed(2)),\r\n\t\t\tpositions: obj.positions,\r\n\t\t\t// refresh: (\r\n\t\t\t// \t<Button\r\n\t\t\t// \t\tvariant=\"info\"\r\n\t\t\t// \t\tonClick={() => RefreshMasterBrokerMutation.mutate(pass_val)}\r\n\t\t\t// \t\tdisabled={obj.broker_flag === false}\r\n\t\t\t// \t>\r\n\t\t\t// \t\t<i className=\"mdi mdi-refresh btn-icon-append m-0\"></i>\r\n\t\t\t// \t</Button>\r\n\t\t\t// ),\r\n\t\t\tcheck: (\r\n\t\t\t\t<>\r\n\t\t\t\t\t<div className=\"d-flex align-item-center\"></div>\r\n\t\t\t\t</>\r\n\t\t\t),\r\n\t\t\tdemat: (\r\n\t\t\t\t<div className=\"d-flex align-item-center\">\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"primary\"\r\n\t\t\t\t\t\tclassName=\"p-2\"\r\n\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\twindow.open(`/admin/panel/brokerinfo/${obj.id}`, \"_blank\")\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<i className=\"mdi mdi-eye btn-icon-append m-0\"></i>\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"warning\"\r\n\t\t\t\t\t\tclassName=\"p-2 ml-2\"\r\n\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\twindow.open(`/admin/panel/users/${obj.parent_id}`, \"_blank\")\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<i className=\"mdi mdi-account-card-details m-0\"></i>\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</div>\r\n\t\t\t),\r\n\t\t\tconnection: (\r\n\t\t\t\t<div className=\"d-flex align-item-center\">\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"info\"\r\n\t\t\t\t\t\tclassName=\"p-2 btnRefreshToken\"\r\n\t\t\t\t\t\tid={obj.id}\r\n\t\t\t\t\t\tonClick={() => Refresh_broker_token(pass_val)}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<i className=\"mdi mdi-rotate-left card-text m-0\"></i>\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t\t{obj.broker_flag === true ? (\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"success\"\r\n\t\t\t\t\t\t\tclassName=\"p-2 ml-2\"\r\n\t\t\t\t\t\t\ttitle=\"Connected\"\r\n\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<i className=\"mdi mdi-lan-connect card-text m-0\"></i>\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t) : (\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"danger\"\r\n\t\t\t\t\t\t\tclassName=\"btnUserBrokerConnection p-2 ml-2\"\r\n\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\tsetShowConnectionPopup({ status: true, RowObj: pass_val })\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\ttitle=\"Re-Connect\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<i className=\"mdi mdi-lan-disconnect card-text m-0\"></i>\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</div>\r\n\t\t\t),\r\n\t\t\taction: (\r\n\t\t\t\t<div className=\"d-flex align-item-center\">\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"danger\"\r\n\t\t\t\t\t\tclassName=\"btnRemoveUserBroker\"\r\n\t\t\t\t\t\tid={obj.id}\r\n\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\tsetShowDeletePopup({ status: true, obj: obj, ind: ind })\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<i className=\"mdi mdi-delete-forever card-text m-0\"></i>\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</div>\r\n\t\t\t),\r\n\t\t\tobj: obj,\r\n\t\t};\r\n\t}\r\n\r\n\tconst connectionSortFunction = (a, b) => {\r\n\t\tif (a.obj.broker_flag === true && b.obj.broker_flag === false) {\r\n\t\t\treturn -1;\r\n\t\t}\r\n\t\tif (a.obj.broker_flag === false && b.obj.broker_flag === true) {\r\n\t\t\treturn 1;\r\n\t\t}\r\n\t\treturn 1;\r\n\t};\r\n\r\n\tconst pnlSortFunction = (a, b) => {\r\n\t\treturn parseFloat(a.obj.pnl) > parseFloat(b.obj.pnl) ? -1 : 1;\r\n\t};\r\n\r\n\tconst marginSortFunction = (a, b) => {\r\n\t\treturn parseFloat(a.obj.margin) > parseFloat(b.obj.margin) ? -1 : 1;\r\n\t};\r\n\r\n\tconst columns = [\r\n\t\t// {\r\n\t\t// \tname: \"\",\r\n\t\t// \tselector: (row) => row.check,\r\n\t\t// \tsortable: true,\r\n\t\t// \tmaxWidth: \"65px\",\r\n\t\t// \tminWidth: \"65px\",\r\n\t\t// },\r\n\t\t{\r\n\t\t\tname: \"Id\",\r\n\t\t\tselector: (row) => row.index,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"65px\",\r\n\t\t\tminWidth: \"65px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Broker - User Id - User\",\r\n\t\t\tselector: (row) => row.broker_user,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"290px\",\r\n\t\t\tminWidth: \"290px\",\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Margin\",\r\n\t\t\tselector: (row) => row.margin,\r\n\t\t\tsortable: true,\r\n\t\t\tsortFunction: marginSortFunction,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"P&L\",\r\n\t\t\tselector: (row) => row.pnl,\r\n\t\t\tsortable: true,\r\n\t\t\tsortFunction: pnlSortFunction,\r\n\t\t},\r\n\t\t{ name: \"Pos\", selector: (row) => row.positions, sortable: true },\r\n\t\t// {\r\n\t\t// \tname: \"Refresh\",\r\n\t\t// \tselector: (row) => row.refresh,\r\n\t\t// \tsortable: false,\r\n\t\t// \tmaxWidth: \"80px\",\r\n\t\t// \tminWidth: \"80px\",\r\n\t\t// },\r\n\t\t{\r\n\t\t\tname: \"Demat\",\r\n\t\t\tselector: (row) => row.demat,\r\n\t\t\tsortable: false,\r\n\t\t\tmaxWidth: \"150px\",\r\n\t\t\tminWidth: \"150px\",\r\n\t\t},\r\n\t\t// { name: 'Acc Info', selector: (row) => row.acc_info, sortable: false, omit: props.is_master, maxWidth: \"80px\", minWidth: \"80px\" },\r\n\t\t{\r\n\t\t\tname: \"Connection\",\r\n\t\t\tselector: (row) => row.connection,\r\n\t\t\tsortable: true,\r\n\t\t\tsortFunction: connectionSortFunction,\r\n\t\t\tmaxWidth: \"180px\",\r\n\t\t\tminWidth: \"180px\",\r\n\t\t},\r\n\t\t{ name: \"Action\", selector: (row) => row.action, sortable: false },\r\n\t\t{ name: \"obj\", selector: (row) => row.obj, omit: true },\r\n\t];\r\n\r\n\t// const [searchText, setSearchText] = useState(\"\");\r\n\tconst handleSearch = (e) => {\r\n\t\tvar search_val = e.target.value;\r\n\t\tvar filteredData = [];\r\n\t\tfor (let value of rawUserMap.values()) {\r\n\t\t\tconst pnlVal = getSubPnlComponent((value.pnl || 0.0).toFixed(2));\r\n\t\t\tconst formattedPnlVal = pnlVal.props.children[1]; // Access element at index 1\r\n\t\t\tif (\r\n\t\t\t\tvalue.broker_name?.toLowerCase().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.nic_name?.toLowerCase().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.demat_name?.toLowerCase().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.broker_user?.toLowerCase().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.margin\r\n\t\t\t\t\t?.toString()\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.pnl\r\n\t\t\t\t\t?.toString()\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tformattedPnlVal\r\n\t\t\t\t\t?.toString()\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.positions\r\n\t\t\t\t\t?.toString()\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(search_val?.toLowerCase())\r\n\t\t\t) {\r\n\t\t\t\tfilteredData.push(value);\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetTableDataFormatted(\r\n\t\t\tfilteredData.map((obj, index) => getRowFormatForBrokerTable(index, obj))\r\n\t\t);\r\n\t};\r\n\tvar arr = [];\r\n\tconst handleChange = (state) => {\r\n\t\t// Handle the selection changes here\r\n\t\tarr = state.selectedRows;\r\n\t};\r\n\tconst handleSquareOffData = () => {\r\n\t\t//extract out the id from array of the selected row's\r\n\t\tconst squareOffUserId = arr.map((data) => data.obj.id);\r\n\t\tconsole.log(squareOffUserId);\r\n\t};\r\n\t// \r\n\r\n\tconst { isDarkTheme } = useTheme()\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<div className=\"table-responsive\">\r\n\t\t\t\t{/* <Form.Group>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"outline-primary\"\r\n\t\t\t\t\t\tclassName=\"mx-2 px-3\"\r\n\t\t\t\t\t\tonClick={handleSquareOffData}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tSquare OFF\r\n\t\t\t\t\t</Button> */}\r\n\t\t\t\t<Form.Group>\r\n\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\tplaceholder=\"Search\"\r\n\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"} mb-2 searchbox-style`}\r\n\t\t\t\t\t\tonChange={handleSearch}\r\n\t\t\t\t\t\trequired\r\n\t\t\t\t\t/>\r\n\t\t\t\t</Form.Group>\r\n\t\t\t\t<DataTable\r\n\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\tdata={tableDataFormatted}\r\n\t\t\t\t\tpagination={false}\r\n\t\t\t\t\tpaginationPerPage={10}\r\n\t\t\t\t\thighlightOnHover\r\n\t\t\t\t\tnoHeader\r\n\t\t\t\t\ttheme={getTheme()}\r\n\t\t\t\t\t// selectableRows\r\n\t\t\t\t\tselectableRowsHighlight\r\n\t\t\t\t\tselectableRowSelected={(row) => row.selected}\r\n\t\t\t\t\tonSelectedRowsChange={handleChange}\r\n\t\t\t\t/>\r\n\t\t\t\t{apiResponseModal.show && (\r\n\t\t\t\t\t<ResponseModal\r\n\t\t\t\t\t\tres={apiResponseModal.res}\r\n\t\t\t\t\t\tsetApiResponseModal={setApiResponseModal}\r\n\t\t\t\t\t\tmsg={apiResponseModal.res.msg}\r\n\t\t\t\t\t/>\r\n\t\t\t\t)}\r\n\t\t\t\t{showConnectionPopup.status && (\r\n\t\t\t\t\t<BrokerConnectionModalForTable\r\n\t\t\t\t\t\tsetShowConnectionPopup={setShowConnectionPopup}\r\n\t\t\t\t\t\tshowConnectionPopup={showConnectionPopup}\r\n\t\t\t\t\t\trow_obj={showConnectionPopup.RowObj}\r\n\t\t\t\t\t\tsetApiResponseModal={setApiResponseModal}\r\n\t\t\t\t\t\tupdateAccountInfo={updateAccountInfo}\r\n\t\t\t\t\t/>\r\n\t\t\t\t)}\r\n\t\t\t\t{showDeletePopup.status && (\r\n\t\t\t\t\t<Modal show={true} animation={true} size=\"md\" className={`${isDarkTheme && \"dark-modal\"} mt-5`}>\r\n\t\t\t\t\t\t<Modal.Header className=\"text-center py-3\">\r\n\t\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t\t<h5 className=\"mb-0\">Delete User Broker</h5>\r\n\t\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t\t<Modal.Body className=\"p-10\">\r\n\t\t\t\t\t\t\tDo you want to remove this user\r\n\t\t\t\t\t\t</Modal.Body>\r\n\t\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\t\tonClick={() => setShowDeletePopup({ status: false, id: \"\" })}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tvariant=\"outline-primary\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"mx-2 px-3\"\r\n\t\t\t\t\t\t\t\t\tonClick={() => RemoveUserBrokerMutation.mutate()}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tYes\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t\t</Modal>\r\n\t\t\t\t)}\r\n\t\t\t</div>\r\n\t\t\t<OTPForBrokerConnectionModel\r\n\t\t\t\tshow={otpModel.show}\r\n\t\t\t\tform_data={otpModel.form_data}\r\n\t\t\t\tmutationFunction={RefreshBrokerTokenmutation}\r\n\t\t\t/>\r\n\t\t</>\r\n\t);\r\n}\r\n", "import React, { useEffect, useState } from \"react\";\r\nimport { <PERSON><PERSON>, Form, Spinner } from \"react-bootstrap\";\r\nimport { useMutation, useQuery, useQueryClient } from \"react-query\";\r\nimport {\r\n\tAddBroker,\r\n\tGetAvailableBrokers,\r\n\tGetMsaterBrokers,\r\n\tGetUserBrokers,\r\n\tSendDataForKotakOtp,\r\n} from \"../../../services/backendServices\";\r\nimport ResponseModal from \"../../components/modal/ResponseModal\";\r\nimport \"../userpages.css\";\r\nimport Select from \"react-select\";\r\nimport withAuth from \"../../components/higher-order/withauth\";\r\nimport BrokerDataTable from \"../../components/table/BrokerDataTable\";\r\nimport { TOKEN_URL } from \"../../../Util/constant\";\r\nimport OTPForBrokerConnectionModel from \"../../components/modal/OTPForBrokerConnectionModel\";\r\nimport UserBrokerDataTable from \"../../components/table/UserBrokerDataTable\";\r\nimport { useTheme } from \"../../context/ThemeContext\";\r\n\r\nfunction UserBrokerManagement() {\r\n\tconst [apiResponseModal, setApiResponseModal] = useState({\r\n\t\tshow: false,\r\n\t\tres: {},\r\n\t}); // Modal : on API Response\r\n\tconst [isFetching, setIsFetching] = useState(false); // Fetch data inside UserBroker Table\r\n\tconst [brokerData, setBrokerData] = useState(null);\r\n\tconst queryClient = useQueryClient();\r\n\r\n\tconst fetchBrokerData = () => {\r\n\t\tsetIsFetching(true);\r\n\t\tGetUserBrokers()\r\n\t\t\t.then((res) => {\r\n\t\t\t\tsetIsFetching(false);\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tsetBrokerData(res);\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\tsetIsFetching(false);\r\n\t\t\t});\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tfetchBrokerData();\r\n\t}, []);\r\n\r\n\tconst { isDarkTheme } = useTheme()\r\n\r\n\treturn (\r\n\t\t<div>\r\n\t\t\t<div className=\"page-header\">\r\n\t\t\t\t<h3 className={`page-title ${isDarkTheme && \"dark-page-title\"}`}>User Broker</h3>\r\n\t\t\t</div>\r\n\t\t\t<div className=\"row\">\r\n\t\t\t\t<div className=\"col-lg-12 grid-margin stretch-card\">\r\n\t\t\t\t\t<div className={`card ${isDarkTheme && \"dark-theme-card\"}`}>\r\n\t\t\t\t\t\t<div className=\"card-body\">\r\n\t\t\t\t\t\t\t{isFetching ? (\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tclassName=\"d-flex justify-content-center align-items-center\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ height: \"100%\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Spinner\r\n\t\t\t\t\t\t\t\t\t\tanimation=\"border\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"5rem\", height: \"5rem\" }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t<UserBrokerDataTable brokerData={brokerData} />\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t\t{apiResponseModal.show && (\r\n\t\t\t\t<ResponseModal\r\n\t\t\t\t\tres={apiResponseModal.res}\r\n\t\t\t\t\tsetApiResponseModal={setApiResponseModal}\r\n\t\t\t\t\tmsg={apiResponseModal.res.msg}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t</div>\r\n\t);\r\n}\r\n\r\nexport default withAuth(UserBrokerManagement);\r\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nvar _excluded = [\"bsPrefix\", \"variant\", \"animation\", \"size\", \"children\", \"as\", \"className\"];\nimport classNames from 'classnames';\nimport React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nvar Spinner = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var bsPrefix = _ref.bsPrefix,\n      variant = _ref.variant,\n      animation = _ref.animation,\n      size = _ref.size,\n      children = _ref.children,\n      _ref$as = _ref.as,\n      Component = _ref$as === void 0 ? 'div' : _ref$as,\n      className = _ref.className,\n      props = _objectWithoutPropertiesLoose(_ref, _excluded);\n\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'spinner');\n  var bsSpinnerPrefix = bsPrefix + \"-\" + animation;\n  return /*#__PURE__*/React.createElement(Component, _extends({\n    ref: ref\n  }, props, {\n    className: classNames(className, bsSpinnerPrefix, size && bsSpinnerPrefix + \"-\" + size, variant && \"text-\" + variant)\n  }), children);\n});\nSpinner.displayName = 'Spinner';\nexport default Spinner;"], "names": ["UserBrokerDataTable", "props", "_props$brokerData3", "apiResponseModal", "setApiResponseModal", "useState", "show", "res", "squareOfUser", "setSquareOfUser", "showConnectionPopup", "setShowConnectionPopup", "status", "RowObj", "showDeletePopup", "setShowDeletePopup", "obj", "ind", "tableDataFormatted", "setTableDataFormatted", "rawUserMap", "useMemo", "Map", "updateAccountInfo", "index", "margin", "data", "positions", "pnl", "broker_flag", "newData", "getRowFormatForBrokerTable", "prevData", "updatedData", "length", "useEffect", "_props$brokerData", "brokerData", "_props$brokerData2", "jsonArr", "Object", "values", "tableData", "rowTableData", "map", "push", "setTableData", "set", "id", "setRawResData", "useMutation", "val", "RefreshMasterBroker", "then", "cogoToast", "success", "concat", "msg", "catch", "e", "console", "log", "RemoveUserBrokerMutation", "RemoveBroker", "delete", "splice", "Promise", "reject", "RefreshBrokerTokenmutation", "RefreshBrokerToken", "refresh_data", "getAuthCodeFromURL", "url", "auth_key", "URLSearchParams", "substring", "indexOf", "get", "otpModel", "setOtpModel", "form_data", "Refresh_broker_token", "broker_name", "val_obj", "client_id", "api_key", "redirect_uri", "TOKEN_URL", "response_type", "state", "urlParams", "toString", "popup", "window", "open", "checkToken", "setInterval", "closed", "clearInterval", "location", "href", "includes", "auth_code", "broker_id", "app_id", "mutate", "close", "error", "handleFyersTokenGeneration", "v", "handleZerodhaTokenGeneration", "app_key", "handleFlattradeTokenGeneration", "pass_val", "broker_user", "demat_name", "toFixed", "getSubPnlComponent", "check", "_jsx", "_Fragment", "children", "className", "demat", "_jsxs", "<PERSON><PERSON>", "variant", "onClick", "parent_id", "connection", "title", "disabled", "action", "columns", "name", "selector", "row", "sortable", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "wrap", "sortFunction", "marginSortFunction", "a", "b", "parseFloat", "pnlSortFunction", "connectionSortFunction", "omit", "isDarkTheme", "useTheme", "Form", "Group", "Control", "type", "placeholder", "onChange", "search_val", "target", "value", "filteredData", "_value$broker_name", "_value$nic_name", "_value$demat_name", "_value$broker_user", "_value$margin", "_value$margin$toStrin", "_value$pnl", "_value$pnl$toString", "_formattedPnlVal$toSt", "_value$positions", "_value$positions$toSt", "formattedPnlVal", "toLowerCase", "nic_name", "required", "DataTable", "pagination", "paginationPerPage", "highlightOnHover", "<PERSON><PERSON><PERSON><PERSON>", "theme", "getTheme", "selectableRowsHighlight", "selectableRowSelected", "selected", "onSelectedRowsChange", "selectedRows", "ResponseModal", "BrokerConnectionModalForTable", "row_obj", "Modal", "animation", "size", "Header", "Title", "Body", "Footer", "OTPForBrokerConnectionModel", "mutationFunction", "UserBrokerManagement", "isFetching", "setIsFetching", "setBrokerData", "useQueryClient", "GetUserBrokers", "style", "height", "Spinner", "width", "<PERSON><PERSON><PERSON>", "_excluded", "React", "_ref", "ref", "bsPrefix", "_ref$as", "as", "Component", "_objectWithoutPropertiesLoose", "bsSpinnerPrefix", "useBootstrapPrefix", "_extends", "classNames", "displayName"], "sourceRoot": ""}
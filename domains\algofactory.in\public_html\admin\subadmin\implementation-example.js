// Role-Based Admin System - Implementation Example

// 1. Permission Service Class
class PermissionService {
    constructor(userPermissions) {
        this.permissions = userPermissions;
    }

    // Check if user can perform action on resource
    canPerform(action, resource, resourceId = null) {
        const resourcePerms = this.permissions[resource];
        if (!resourcePerms) return false;

        const actionPerms = resourcePerms[action];
        if (!actionPerms) return false;

        // If it's a boolean permission (like create)
        if (typeof actionPerms === 'boolean') {
            return actionPerms;
        }

        // If it's an array of allowed IDs
        if (Array.isArray(actionPerms)) {
            return actionPerms.includes('*') || 
                   actionPerms.includes(String(resourceId));
        }

        return false;
    }

    // Convenience methods
    canView(resource, resourceId) {
        return this.canPerform('view', resource, resourceId);
    }

    canEdit(resource, resourceId) {
        return this.canPerform('edit', resource, resourceId);
    }

    canDelete(resource, resourceId) {
        return this.canPerform('delete', resource, resourceId);
    }

    canCreate(resource) {
        return this.canPerform('create', resource);
    }

    // Filter data based on permissions
    filterStrategies(strategies) {
        return strategies.filter(strategy => 
            this.canView('strategies', strategy.strategy_id)
        );
    }

    filterUsers(users) {
        return users.filter(user => 
            this.canView('users', user.user_id)
        );
    }
}

// 2. API Wrapper with Permission Checking
class PermissionAwareAPI {
    constructor(userPermissions, originalAPI) {
        this.permissions = new PermissionService(userPermissions);
        this.api = originalAPI;
    }

    // Strategy Methods
    async getStrategies() {
        const strategies = await this.api.getStrategies();
        return this.permissions.filterStrategies(strategies.data || []);
    }

    async updateStrategy(strategyId, data) {
        if (!this.permissions.canEdit('strategies', strategyId)) {
            throw new Error('You do not have permission to edit this strategy');
        }
        return await this.api.updateStrategy(strategyId, data);
    }

    async deleteStrategy(strategyId) {
        if (!this.permissions.canDelete('strategies', strategyId)) {
            throw new Error('You do not have permission to delete this strategy');
        }
        return await this.api.deleteStrategy(strategyId);
    }

    async createStrategy(data) {
        if (!this.permissions.canCreate('strategies')) {
            throw new Error('You do not have permission to create strategies');
        }
        return await this.api.createStrategy(data);
    }

    // User Methods
    async getUsers() {
        const users = await this.api.getUsers();
        return this.permissions.filterUsers(users.data || []);
    }

    async updateUser(userId, data) {
        if (!this.permissions.canEdit('users', userId)) {
            throw new Error('You do not have permission to edit this user');
        }
        return await this.api.updateUser(userId, data);
    }

    // Position Methods
    async getPositions(strategyId) {
        if (!this.permissions.canView('positions', strategyId)) {
            throw new Error('You do not have permission to view positions for this strategy');
        }
        return await this.api.getPositions(strategyId);
    }

    async closePosition(positionId, strategyId) {
        if (!this.permissions.canEdit('positions', strategyId)) {
            throw new Error('You do not have permission to close positions for this strategy');
        }
        return await this.api.closePosition(positionId);
    }
}

// 3. React Component Example - Strategy Management
const StrategyManagement = ({ userPermissions }) => {
    const [strategies, setStrategies] = useState([]);
    const [loading, setLoading] = useState(true);
    const permissionAPI = new PermissionAwareAPI(userPermissions, originalAPI);

    useEffect(() => {
        loadStrategies();
    }, []);

    const loadStrategies = async () => {
        try {
            const data = await permissionAPI.getStrategies();
            setStrategies(data);
        } catch (error) {
            console.error('Error loading strategies:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleEdit = async (strategyId, newData) => {
        try {
            await permissionAPI.updateStrategy(strategyId, newData);
            loadStrategies(); // Reload data
        } catch (error) {
            alert(error.message);
        }
    };

    const handleDelete = async (strategyId) => {
        if (!confirm('Are you sure you want to delete this strategy?')) return;
        
        try {
            await permissionAPI.deleteStrategy(strategyId);
            loadStrategies(); // Reload data
        } catch (error) {
            alert(error.message);
        }
    };

    if (loading) return <div>Loading...</div>;

    return (
        <div className="strategy-management">
            <div className="header">
                <h2>Strategy Management</h2>
                {permissionAPI.permissions.canCreate('strategies') && (
                    <button onClick={() => setShowCreateForm(true)}>
                        Create New Strategy
                    </button>
                )}
            </div>

            <div className="strategy-list">
                {strategies.map(strategy => (
                    <div key={strategy.strategy_id} className="strategy-card">
                        <h3>{strategy.strategy_name}</h3>
                        <p>{strategy.description}</p>
                        
                        <div className="actions">
                            {permissionAPI.permissions.canView('strategies', strategy.strategy_id) && (
                                <button onClick={() => viewStrategy(strategy.strategy_id)}>
                                    View Details
                                </button>
                            )}
                            
                            {permissionAPI.permissions.canEdit('strategies', strategy.strategy_id) && (
                                <button onClick={() => editStrategy(strategy.strategy_id)}>
                                    Edit
                                </button>
                            )}
                            
                            {permissionAPI.permissions.canDelete('strategies', strategy.strategy_id) && (
                                <button 
                                    onClick={() => handleDelete(strategy.strategy_id)}
                                    className="danger"
                                >
                                    Delete
                                </button>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

// 4. Permission Management Component (Super Admin Only)
const PermissionManagement = () => {
    const [subAdmins, setSubAdmins] = useState([]);
    const [strategies, setStrategies] = useState([]);
    const [users, setUsers] = useState([]);

    const createSubAdmin = async (formData) => {
        const newUser = {
            username: formData.username,
            email: formData.email,
            password: formData.password,
            role: 'sub_admin',
            permissions: {
                strategies: {
                    view: formData.strategyViewPermissions,
                    edit: formData.strategyEditPermissions,
                    delete: [],
                    create: false
                },
                users: {
                    view: formData.userViewPermissions,
                    edit: formData.userEditPermissions,
                    delete: []
                },
                positions: {
                    view: formData.positionViewPermissions,
                    close: formData.positionClosePermissions
                },
                orders: {
                    view: formData.orderViewPermissions,
                    cancel: formData.orderCancelPermissions
                }
            }
        };

        // Save to your custom database
        await saveSubAdminToDatabase(newUser);
        loadSubAdmins();
    };

    return (
        <div className="permission-management">
            <h2>Sub Admin Management</h2>
            
            <div className="create-sub-admin">
                <h3>Create New Sub Admin</h3>
                <SubAdminForm 
                    strategies={strategies}
                    users={users}
                    onSubmit={createSubAdmin}
                />
            </div>

            <div className="existing-sub-admins">
                <h3>Existing Sub Admins</h3>
                {subAdmins.map(admin => (
                    <div key={admin.id} className="sub-admin-card">
                        <h4>{admin.username}</h4>
                        <p>Email: {admin.email}</p>
                        <p>Created: {admin.created_at}</p>
                        
                        <div className="permissions-summary">
                            <p>Strategies: {admin.permissions.strategies.view.length} view, {admin.permissions.strategies.edit.length} edit</p>
                            <p>Users: {admin.permissions.users.view.length} view, {admin.permissions.users.edit.length} edit</p>
                        </div>
                        
                        <button onClick={() => editPermissions(admin.id)}>
                            Edit Permissions
                        </button>
                    </div>
                ))}
            </div>
        </div>
    );
};

// 5. Example Permission Configuration
const examplePermissions = {
    // Super Admin (You)
    superAdmin: {
        strategies: { view: ['*'], edit: ['*'], delete: ['*'], create: true },
        users: { view: ['*'], edit: ['*'], delete: ['*'] },
        brokers: { view: ['*'], edit: ['*'], delete: ['*'] },
        orders: { view: true, cancel: true, execute: true },
        positions: { view: ['*'], close: ['*'], add: true }
    },
    
    // Sub Admin - Strategy Manager
    strategyManager: {
        strategies: { view: ['1', '2', '3'], edit: ['1', '2'], delete: [], create: false },
        users: { view: ['10', '11', '12'], edit: [], delete: [] },
        brokers: { view: [], edit: [], delete: [] },
        orders: { view: true, cancel: false, execute: false },
        positions: { view: ['1', '2', '3'], close: ['1'], add: false }
    },
    
    // Sub Admin - Account Manager
    accountManager: {
        strategies: { view: [], edit: [], delete: [], create: false },
        users: { view: ['10', '11', '12', '13', '14'], edit: ['10', '11'], delete: [] },
        brokers: { view: ['1', '2'], edit: [], delete: [] },
        orders: { view: true, cancel: false, execute: false },
        positions: { view: [], close: [], add: false }
    },
    
    // Sub Admin - View Only
    viewer: {
        strategies: { view: ['1', '2'], edit: [], delete: [], create: false },
        users: { view: ['10', '11'], edit: [], delete: [] },
        brokers: { view: ['1'], edit: [], delete: [] },
        orders: { view: true, cancel: false, execute: false },
        positions: { view: ['1', '2'], close: [], add: false }
    }
};

export { PermissionService, PermissionAwareAPI, StrategyManagement, PermissionManagement };

# PHP-Based Role Management System for Hostinger

## System Architecture for Shared Hosting

```
┌─────────────────────────────────────────┐
│         Your Domain Structure           │
├─────────────────────────────────────────┤
│  yourdomain.com/                        │
│  ├── admin/                             │
│  │   ├── index.php (Super Admin)        │
│  │   ├── sub-admin.php (Sub Admin)      │
│  │   ├── login.php                      │
│  │   ├── api/                           │
│  │   │   ├── proxy.php (API Wrapper)    │
│  │   │   └── permissions.php            │
│  │   ├── includes/                      │
│  │   │   ├── config.php                 │
│  │   │   ├── auth.php                   │
│  │   │   └── permissions.php            │
│  │   └── assets/                        │
│  │       ├── css/                       │
│  │       └── js/                        │
└─────────────────────────────────────────┘
```

## Database Schema (MySQL)

### 1. Admin Users Table
```sql
CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `email` varchar(100) NOT NULL UNIQUE,
  `password_hash` varchar(255) NOT NULL,
  `role` enum('super_admin','sub_admin','viewer') NOT NULL DEFAULT 'sub_admin',
  `created_by` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  FOREIGN KEY (`created_by`) REFERENCES `admin_users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 2. User Permissions Table
```sql
CREATE TABLE `user_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `resource_type` enum('strategy','user','broker','order','position','general') NOT NULL,
  `resource_id` varchar(50) DEFAULT NULL,
  `permission_type` enum('view','edit','delete','create','all') NOT NULL,
  `granted_by` int(11) NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_permission` (`user_id`,`resource_type`,`resource_id`,`permission_type`),
  KEY `user_id` (`user_id`),
  KEY `granted_by` (`granted_by`),
  FOREIGN KEY (`user_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`granted_by`) REFERENCES `admin_users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 3. Session Management Table
```sql
CREATE TABLE `admin_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `session_token` varchar(255) NOT NULL,
  `expires_at` timestamp NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_token` (`session_token`),
  KEY `user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## PHP Implementation Structure

### 1. Configuration File (`includes/config.php`)
```php
<?php
// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database_name');
define('DB_USER', 'your_db_username');
define('DB_PASS', 'your_db_password');

// AlgoFactory API Configuration
define('ALGOFACTORY_API_BASE', 'https://bpapil1.algodelta.com/api/v1');
define('ALGOFACTORY_TOKEN_KEY', 'admin_access_token');

// Session Configuration
define('SESSION_TIMEOUT', 3600); // 1 hour
define('COOKIE_DOMAIN', '.yourdomain.com');

// Security
define('ENCRYPTION_KEY', 'your-secret-encryption-key-here');
define('PASSWORD_SALT', 'your-password-salt-here');

// Error Reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database Connection
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}
?>
```

### 2. Authentication Class (`includes/auth.php`)
```php
<?php
class Auth {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        session_start();
    }
    
    public function login($username, $password) {
        $stmt = $this->pdo->prepare("
            SELECT id, username, email, password_hash, role, is_active 
            FROM admin_users 
            WHERE (username = ? OR email = ?) AND is_active = 1
        ");
        $stmt->execute([$username, $username]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password_hash'])) {
            $this->createSession($user);
            $this->updateLastLogin($user['id']);
            return true;
        }
        return false;
    }
    
    private function createSession($user) {
        $sessionToken = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', time() + SESSION_TIMEOUT);
        
        // Store in database
        $stmt = $this->pdo->prepare("
            INSERT INTO admin_sessions (user_id, session_token, expires_at) 
            VALUES (?, ?, ?)
        ");
        $stmt->execute([$user['id'], $sessionToken, $expiresAt]);
        
        // Store in PHP session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['session_token'] = $sessionToken;
        
        // Set cookie
        setcookie('admin_session', $sessionToken, time() + SESSION_TIMEOUT, '/', COOKIE_DOMAIN, true, true);
    }
    
    public function isLoggedIn() {
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['session_token'])) {
            return false;
        }
        
        // Verify session in database
        $stmt = $this->pdo->prepare("
            SELECT user_id FROM admin_sessions 
            WHERE session_token = ? AND expires_at > NOW()
        ");
        $stmt->execute([$_SESSION['session_token']]);
        
        return $stmt->fetch() !== false;
    }
    
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) return null;
        
        $stmt = $this->pdo->prepare("
            SELECT id, username, email, role 
            FROM admin_users 
            WHERE id = ?
        ");
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch();
    }
    
    public function logout() {
        if (isset($_SESSION['session_token'])) {
            // Remove from database
            $stmt = $this->pdo->prepare("DELETE FROM admin_sessions WHERE session_token = ?");
            $stmt->execute([$_SESSION['session_token']]);
        }
        
        // Clear session
        session_destroy();
        setcookie('admin_session', '', time() - 3600, '/', COOKIE_DOMAIN);
    }
    
    public function createSubAdmin($username, $email, $password, $permissions) {
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);
        
        try {
            $this->pdo->beginTransaction();
            
            // Create user
            $stmt = $this->pdo->prepare("
                INSERT INTO admin_users (username, email, password_hash, role, created_by) 
                VALUES (?, ?, ?, 'sub_admin', ?)
            ");
            $stmt->execute([$username, $email, $passwordHash, $_SESSION['user_id']]);
            $newUserId = $this->pdo->lastInsertId();
            
            // Add permissions
            $this->addPermissions($newUserId, $permissions);
            
            $this->pdo->commit();
            return $newUserId;
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            throw $e;
        }
    }
    
    private function addPermissions($userId, $permissions) {
        $stmt = $this->pdo->prepare("
            INSERT INTO user_permissions (user_id, resource_type, resource_id, permission_type, granted_by) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        foreach ($permissions as $permission) {
            $stmt->execute([
                $userId,
                $permission['resource_type'],
                $permission['resource_id'],
                $permission['permission_type'],
                $_SESSION['user_id']
            ]);
        }
    }
}
?>
```

### 3. Permission Manager (`includes/permissions.php`)
```php
<?php
class PermissionManager {
    private $pdo;
    private $userId;
    private $userPermissions = null;
    
    public function __construct($pdo, $userId) {
        $this->pdo = $pdo;
        $this->userId = $userId;
        $this->loadPermissions();
    }
    
    private function loadPermissions() {
        $stmt = $this->pdo->prepare("
            SELECT resource_type, resource_id, permission_type 
            FROM user_permissions 
            WHERE user_id = ?
        ");
        $stmt->execute([$this->userId]);
        
        $this->userPermissions = [];
        while ($row = $stmt->fetch()) {
            $resourceType = $row['resource_type'];
            $resourceId = $row['resource_id'] ?: '*';
            $permissionType = $row['permission_type'];
            
            if (!isset($this->userPermissions[$resourceType])) {
                $this->userPermissions[$resourceType] = [];
            }
            if (!isset($this->userPermissions[$resourceType][$permissionType])) {
                $this->userPermissions[$resourceType][$permissionType] = [];
            }
            
            $this->userPermissions[$resourceType][$permissionType][] = $resourceId;
        }
    }
    
    public function canPerform($action, $resourceType, $resourceId = null) {
        // Super admin can do everything
        if ($this->isSuperAdmin()) {
            return true;
        }
        
        if (!isset($this->userPermissions[$resourceType][$action])) {
            return false;
        }
        
        $allowedResources = $this->userPermissions[$resourceType][$action];
        
        // Check if user has permission for all resources (*) or specific resource
        return in_array('*', $allowedResources) || 
               ($resourceId && in_array((string)$resourceId, $allowedResources));
    }
    
    public function canView($resourceType, $resourceId = null) {
        return $this->canPerform('view', $resourceType, $resourceId);
    }
    
    public function canEdit($resourceType, $resourceId = null) {
        return $this->canPerform('edit', $resourceType, $resourceId);
    }
    
    public function canDelete($resourceType, $resourceId = null) {
        return $this->canPerform('delete', $resourceType, $resourceId);
    }
    
    public function canCreate($resourceType) {
        return $this->canPerform('create', $resourceType);
    }
    
    public function filterStrategies($strategies) {
        if ($this->isSuperAdmin()) {
            return $strategies;
        }
        
        return array_filter($strategies, function($strategy) {
            return $this->canView('strategy', $strategy['strategy_id']);
        });
    }
    
    public function filterUsers($users) {
        if ($this->isSuperAdmin()) {
            return $users;
        }
        
        return array_filter($users, function($user) {
            return $this->canView('user', $user['user_id']);
        });
    }
    
    private function isSuperAdmin() {
        $stmt = $this->pdo->prepare("SELECT role FROM admin_users WHERE id = ?");
        $stmt->execute([$this->userId]);
        $user = $stmt->fetch();
        return $user && $user['role'] === 'super_admin';
    }
    
    public function getAllowedStrategies() {
        if ($this->isSuperAdmin()) {
            return ['*'];
        }
        
        $strategies = [];
        if (isset($this->userPermissions['strategy']['view'])) {
            $strategies = array_merge($strategies, $this->userPermissions['strategy']['view']);
        }
        if (isset($this->userPermissions['strategy']['edit'])) {
            $strategies = array_merge($strategies, $this->userPermissions['strategy']['edit']);
        }
        
        return array_unique($strategies);
    }
}
?>
```

## File Structure for Hostinger

```
public_html/
├── admin/
│   ├── index.php              # Super Admin Dashboard
│   ├── sub-admin.php          # Sub Admin Dashboard  
│   ├── login.php              # Login Page
│   ├── logout.php             # Logout Handler
│   ├── create-sub-admin.php   # Create Sub Admin Form
│   ├── manage-permissions.php # Permission Management
│   ├── api/
│   │   ├── proxy.php          # API Proxy with Permission Checks
│   │   ├── strategies.php     # Strategy API Endpoints
│   │   ├── users.php          # User API Endpoints
│   │   └── auth.php           # Authentication API
│   ├── includes/
│   │   ├── config.php         # Database & API Configuration
│   │   ├── auth.php           # Authentication Class
│   │   ├── permissions.php    # Permission Manager Class
│   │   ├── api-client.php     # AlgoFactory API Client
│   │   └── header.php         # Common Header
│   ├── assets/
│   │   ├── css/
│   │   │   └── admin.css      # Admin Panel Styles
│   │   └── js/
│   │       └── admin.js       # Admin Panel JavaScript
│   └── install/
│       └── setup.php          # Database Setup Script
```

This PHP-based solution will work perfectly on Hostinger shared hosting and gives you:

✅ **Complete role-based access control**
✅ **Easy sub-admin creation and management**  
✅ **Secure session management**
✅ **API proxy with permission filtering**
✅ **Works with existing AlgoFactory backend**
✅ **No additional hosting requirements**

Would you like me to create the actual PHP files for any specific part of this system?

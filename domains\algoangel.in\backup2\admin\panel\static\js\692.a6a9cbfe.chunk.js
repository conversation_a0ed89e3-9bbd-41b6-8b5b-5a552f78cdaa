"use strict";(self.webpackChunkadminpanel=self.webpackChunkadminpanel||[]).push([[692],{3599:(e,t,a)=>{a.d(t,{l:()=>n});const n=e=>{const t=new Date(e);if(isNaN(t.getTime()))return e;{const e=t.getFullYear(),a=(t.getMonth()+1).toString().padStart(2,"0"),n=t.getDate().toString().padStart(2,"0"),r=t.getHours().toString().padStart(2,"0"),s=t.getMinutes().toString().padStart(2,"0"),o=t.getSeconds().toString().padStart(2,"0");return"".concat(e,"-").concat(a,"-").concat(n," ").concat(r,":").concat(s,":").concat(o)}}},7455:(e,t,a)=>{a.a(e,(async(e,n)=>{try{a.d(t,{Z:()=>c});var r=a(2791),s=a(4880),o=a(7971),l=a(184),i=e([o]);o=(i.then?(await i)():i)[0];const c=e=>t=>{const a=(0,s.k6)();return(0,r.useEffect)((()=>{const e=localStorage.getItem("admin_access_token"),{hostname:t}=window.location,a="https://"+t+"/admin/";if(e){const t={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:e})};fetch(o.T5+"/auth/verify",t).then((e=>e.json())).then((e=>{"token_not_valid"===e.code&&(window.location.href=a)})).catch((e=>{console.error("Error:",e),window.location.href=a}))}else window.location.href=a}),[a]),(0,l.jsx)(e,{...t})};n()}catch(c){n(c)}}))},3149:(e,t,a)=>{a.a(e,(async(e,n)=>{try{a.d(t,{Z:()=>w});var r=a(2791),s=a(1662),o=a(4912),l=a(3360),i=a(1933),c=a(1025),d=a(5742),u=a(6960),m=a(7691),h=a(4970),p=a(2240),x=a(9513),b=a.n(x),v=(a(8639),a(1951)),j=a(4536),f=a(184),g=e([m]);function w(e){const[t,a]=(0,r.useState)(!0);function n(){e.setPlaceOrderModal({show:!1}),a(!1)}const[x,g]=(0,r.useState)({show:!1,res:{}}),[y,w]=(0,r.useState)(1),[N,C]=(0,r.useState)({value:"BUY",label:"BUY"}),[k,S]=(0,r.useState)({value:"PE",label:"PE"}),[Z,L]=(0,r.useState)({value:"EQUITY",label:"EQUITY"}),[E,T]=(0,r.useState)({value:"CNC",label:"CNC"}),[P,O]=(0,r.useState)(),[D,M]=(0,r.useState)(new Date),[B,U]=(0,r.useState)(),[F,I]=(0,r.useState)(),_=(0,i.useMutation)("PlaceOrderApi",(e=>(0,m.qd)(e).then((e=>{e.status?(n(),u.Z.success("".concat(e.msg," OK"))):(I(!1),g({show:!0,res:e}))})).catch((e=>{console.log(e),g({show:!0,res:e})})))),{isDarkTheme:q}=(0,j.F)();return(0,f.jsxs)(s.Z,{show:t,animation:!0,size:"lg",className:" ".concat(q&&"dark-modal"," mt-5  custom-modal"),children:[(0,f.jsx)(s.Z.Header,{className:"text-center py-2",children:(0,f.jsx)(s.Z.Title,{className:"text-center",children:(0,f.jsx)("h3",{className:"mb-0",children:"Place order"})})}),(0,f.jsx)(s.Z.Body,{className:"p-10 ",children:(0,f.jsxs)("div",{children:[(0,f.jsx)("div",{className:"row",children:(0,f.jsx)("div",{className:"col-lg-12 grid-margin stretch-card mb-0",children:(0,f.jsx)("div",{className:"card-body pb-1",children:(0,f.jsxs)(o.Z,{children:[(0,f.jsxs)("div",{className:"row scroll",children:[(0,f.jsx)("div",{className:"col-md-3",children:(0,f.jsxs)(o.Z.Group,{children:[(0,f.jsx)(o.Z.Label,{className:"".concat(q?"dark-text":"text-muted"),children:"Type"}),(0,f.jsx)(d.ZP,{classNamePrefix:"react-select",className:q?"dark-select":"",options:[{value:"EQUITY",label:"EQUITY"},{value:"FUTURE",label:"FUTURE"},{value:"OPTION",label:"OPTION"}],value:Z,onChange:e=>L(e),styles:(0,p.y0)(h.b4,h.UO)})]})}),(0,f.jsx)("div",{className:"col-md-4",children:(0,f.jsxs)(o.Z.Group,{children:[(0,f.jsx)(o.Z.Label,{className:"".concat(q?"dark-text":"text-muted"),children:"Script"}),(0,f.jsx)(o.Z.Control,{className:"".concat(q&&"dark-form-control"),type:"text",placeholder:"Script",value:P,onChange:e=>{var t;return O(null===(t=e.target.value)||void 0===t?void 0:t.toUpperCase())},required:!0})]})}),("FUTURE"===Z.value||"OPTION"===Z.value)&&(0,f.jsx)("div",{className:"col-md-3",children:(0,f.jsxs)(o.Z.Group,{controlId:"datePicker",className:"".concat(q&&"dark-datepicker"," mb-0"),children:[(0,f.jsx)(o.Z.Label,{className:"".concat(q?"dark-text":"text-muted"),children:"Date"}),(0,f.jsx)(b(),{selected:D,onChange:e=>{M(e)},className:"form-control",dateFormat:"dd-MM-yyyy"})]})}),(0,f.jsx)("div",{className:"col-md-2",children:(0,f.jsxs)(o.Z.Group,{children:[(0,f.jsx)(o.Z.Label,{className:"".concat(q?"dark-text":"text-muted"),children:"EQUITY"===Z.value?"Qty":"Lot"}),(0,f.jsx)(o.Z.Control,{className:"".concat(q&&"dark-form-control"),type:"number",placeholder:"Qty",value:y,onChange:e=>w(e.target.value),required:!0})]})}),"OPTION"===Z.value&&(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)("div",{className:"col-md-3",children:(0,f.jsxs)(o.Z.Group,{children:[(0,f.jsx)(o.Z.Label,{className:"".concat(q?"dark-text":"text-muted"),children:"Strike Price"}),(0,f.jsx)(o.Z.Control,{className:"".concat(q&&"dark-form-control"),type:"number",placeholder:"Strike Price",value:B,onChange:e=>U(e.target.value),required:!0})]})}),(0,f.jsx)("div",{className:"col-md-3",children:(0,f.jsxs)(o.Z.Group,{children:[(0,f.jsx)(o.Z.Label,{className:"".concat(q?"dark-text":"text-muted"),children:"Option Side"}),(0,f.jsx)(d.ZP,{classNamePrefix:"react-select",className:q?"dark-select":"",options:[{value:"PE",label:"PE"},{value:"CE",label:"CE"}],value:k,onChange:e=>S(e),styles:(0,p.y0)(h.b4,h.UO)})]})})]}),(0,f.jsx)("div",{className:"col-md-3",children:(0,f.jsxs)(o.Z.Group,{children:[(0,f.jsx)(o.Z.Label,{className:"".concat(q?"dark-text":"text-muted"),children:"Action"}),(0,f.jsx)(d.ZP,{classNamePrefix:"react-select",className:q?"dark-select":"",options:[{value:"BUY",label:"BUY"},{value:"SELL",label:"SELL"}],value:N,onChange:e=>C(e),placeholder:"Search for an item...",styles:(0,p.y0)(h.b4,h.UO)})]})}),(0,f.jsx)("div",{className:"col-md-3",children:(0,f.jsxs)(o.Z.Group,{children:[(0,f.jsx)(o.Z.Label,{className:"".concat(q?"dark-text":"text-muted"),children:"Product"}),(0,f.jsx)(d.ZP,{classNamePrefix:"react-select",className:q?"dark-select":"",options:[{value:"CNC",label:"CNC"},{value:"NRML",label:"NRML"},{value:"MIS",label:"MIS"}],value:E,onChange:e=>T(e),styles:(0,p.y0)(h.b4,h.UO)})]})})]}),(0,f.jsx)("div",{className:"row scroll",children:(0,f.jsxs)("div",{className:"col-md-6 mt-2",children:[(0,f.jsx)(l.Z,{type:"button",className:"btn btn-md btn-danger mr-3",onClick:()=>{C({value:"BUY",label:"BUY"}),S({value:"PE",label:"PE"}),L({value:"EQUITY",label:"EQUITY"}),T({value:"CNC",label:"CNC"}),w(""),U(null),M(new Date)},children:"Clear"}),(0,f.jsx)(l.Z,{className:"btn btn-md mr-3",onClick:e=>{e.preventDefault(),"EQUITY"===Z.value&&y&&null!==N&&void 0!==N&&N.value&&P||"FUTURE"===Z.value&&y&&null!==N&&void 0!==N&&N.value&&P||"OPTION"===Z.value&&y&&null!==N&&void 0!==N&&N.value&&P&&B&&null!==k&&void 0!==k&&k.value?I(!0):u.Z.error("Please fill all fields")},children:"PlaceOrder"})]})})]})})})}),F&&(0,f.jsxs)(s.Z,{show:F,animation:!0,size:"md",className:" ".concat(q&&"dark-modal"," mt-5  custom-modal"),children:[(0,f.jsx)(s.Z.Header,{className:"d-flex justify-content-center py-2",children:(0,f.jsx)(s.Z.Title,{children:(0,f.jsx)("div",{children:(0,f.jsx)("h3",{className:"mb-0",children:"Confirm"})})})}),(0,f.jsx)(s.Z.Body,{className:"p-10",style:{backgroundColor:!q&&"white"},children:(0,f.jsx)("table",{style:{width:"60%",margin:"0 auto",borderCollapse:"collapse"},children:(0,f.jsxs)("tbody",{children:[(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{style:{color:!q&&"black"},children:"Order Type"}),(0,f.jsx)("td",{style:{color:!q&&"black"},children:": "}),(0,f.jsx)("td",{style:{color:!q&&"black"},children:(0,f.jsx)("b",{children:Z.value})})]}),(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{style:{color:!q&&"black"},children:"Script"}),(0,f.jsx)("td",{style:{color:!q&&"black"},children:": "}),(0,f.jsx)("td",{style:{color:!q&&"black"},children:(0,f.jsx)("b",{children:P})})]}),"FUTURE"===Z.value&&(0,f.jsx)("tr",{children:D&&(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)("td",{style:{color:!q&&"black"},children:"Date"}),(0,f.jsx)("td",{style:{color:!q&&"black"},children:": "}),(0,f.jsx)("td",{style:{color:!q&&"black"},children:(0,v.default)(D,"dd-MM-yyyy")})]})}),(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{style:{color:!q&&"black"},children:"EQUITY"===Z.value?"Qty":"Lot"}),(0,f.jsx)("td",{style:{color:!q&&"black"},children:": "}),(0,f.jsx)("td",{style:{color:!q&&"black"},children:(0,f.jsx)("b",{children:y})})]}),"OPTION"===Z.value&&(0,f.jsxs)(f.Fragment,{children:[(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{style:{color:!q&&"black"},children:"Strike Price"}),(0,f.jsx)("td",{style:{color:!q&&"black"},children:": "}),(0,f.jsx)("td",{style:{color:!q&&"black"},children:(0,f.jsx)("b",{children:B})})]}),(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{style:{color:!q&&"black"},children:"Option Side"}),(0,f.jsx)("td",{style:{color:!q&&"black"},children:": "}),(0,f.jsx)("td",{style:{color:!q&&"black"},children:(0,f.jsx)("b",{children:k.value})})]})]}),(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{style:{color:!q&&"black"},children:"Action"}),(0,f.jsx)("td",{style:{color:!q&&"black"},children:": "}),(0,f.jsx)("td",{style:{color:!q&&"black"},children:(0,f.jsx)("b",{children:N.value})})]}),(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{style:{color:!q&&"black"},children:"Product"}),(0,f.jsx)("td",{style:{color:!q&&"black"},children:": "}),(0,f.jsx)("td",{style:{color:!q&&"black"},children:(0,f.jsx)("b",{children:E.value})})]})]})})}),(0,f.jsxs)(s.Z.Footer,{className:"py-1 d-flex justify-content-center",children:[(0,f.jsx)("div",{children:(0,f.jsx)(l.Z,{variant:"outline-primary",onClick:t=>{let a;a="EQUITY"===Z.value?{order_type:"eq",script_name:P,strategy_id:Number(e.id),quantity:Number(y),transaction:N.value,product:E.value}:"OPTION"===Z.value?{order_type:"option",script_name:P,expiry_date:(0,v.default)(D,"MM-dd-yyyy"),strike_price:Number(B),option_type:k.value,strategy_id:Number(e.id),quantity:Number(y),transaction:N.value,product:E.value}:{order_type:"future",script_name:P,expiry_date:(0,v.default)(D,"MM-dd-yyyy"),strategy_id:Number(e.id),quantity:Number(y),transaction:N.value,product:E.value},a&&_.mutate(a)},children:"Confirm"})}),(0,f.jsx)("div",{children:(0,f.jsx)(l.Z,{variant:"outline-danger",onClick:()=>I(!1),children:"Cancel"})})]})]}),x.show&&(0,f.jsx)(c.Z,{res:x.res,setApiResponseModal:g,msg:x.res.msg})]})}),(0,f.jsx)(s.Z.Footer,{className:"py-1 d-flex justify-content-center",children:(0,f.jsx)("div",{children:(0,f.jsx)(l.Z,{variant:"outline-danger",onClick:n,children:"Cancel"})})})]})}m=(g.then?(await g)():g)[0],n()}catch(y){n(y)}}))},7889:(e,t,a)=>{a.a(e,(async(e,n)=>{try{a.d(t,{q:()=>j});var r=a(2791),s=a(3513),o=a(3360),l=a(1662),i=a(4970),c=a(4912),d=a(3599),u=a(7691),m=a(1933),h=a(1025),p=a(4880),x=a(4536),b=a(184),v=e([u]);u=(v.then?(await v)():v)[0];const j=e=>{const{user_id:t}=(0,p.UO)(),[a,n]=(0,r.useState)([]),v=(0,r.useMemo)((()=>new Map),[]),[j,f]=(0,r.useState)({status:!1,obj:null,ind:null}),[g,y]=(0,r.useState)({status:!1,obj:null,ind:null}),[w,N]=(0,r.useState)({show:!1,res:{}});function C(e,t){return{index:e+1,symbol:t.symbol,transaction:(0,i.le)(t.transaction),product:t.product,ordertype:t.ordertype,quantity:t.quantity,price:t.price,time:(0,d.l)(t.time),orderid:t.orderid,status:t.is_open?(0,b.jsxs)("div",{className:"d-flex align-item-center",children:[(0,b.jsx)(o.Z,{variant:"primary",className:"btnRemoveUserBroker",id:t.id,onClick:()=>y({status:!0,obj:t,ind:e}),children:(0,b.jsx)("i",{className:"mdi mdi mdi-checkbox-marked-circle-outline\r\ncard-text m-0"})}),(0,b.jsx)(o.Z,{variant:"danger",className:"ml-3 btnRemoveUserBroker",id:t.id,onClick:()=>f({status:!0,obj:t,ind:e}),children:(0,b.jsx)("i",{className:"mdi mdi mdi mdi-close-circle-outline\r card-text m-0"})})]}):(0,i.SX)(null===t||void 0===t?void 0:t.status,null===t||void 0===t?void 0:t.reason,t),obj:t}}(0,r.useEffect)((()=>{var t;!function(e){var t=[];e.map(((e,a)=>t.push(C(a,e)))),n(t)}(null===(t=e.data)||void 0===t?void 0:t.orders),e.data.orders.map(((e,t)=>v.set(e.orderid,e)))}),[e.data]);const k=[{name:"Id",selector:e=>e.index,sortable:!0,maxWidth:"65px",minWidth:"65px"},{name:"Symbol",selector:e=>e.symbol,wrap:!0,sortable:!0,wrap:!0},{name:"Trans",selector:e=>e.transaction,sortable:!0,sortFunction:(e,t)=>"B"===e.obj.transaction||"b"===e.obj.transaction||"buy"===e.obj.transaction||"BUY"===e.obj.transaction||"Buy"===e.obj.transaction?1:-1,maxWidth:"90px",minWidth:"90px"},{name:"Product",selector:e=>e.product,sortable:!0,maxWidth:"120px",minWidth:"120px"},{name:"Type",selector:e=>e.ordertype,sortable:!0,maxWidth:"120px",minWidth:"120px"},{name:"Qty",selector:e=>e.quantity,sortable:!0,maxWidth:"80px",minWidth:"80px"},{name:"Price",selector:e=>e.price,sortable:!0,maxWidth:"100px",minWidth:"100px"},{name:"Time",selector:e=>e.time,sortable:!0,maxWidth:"100px",minWidth:"100px",wrap:!0},{name:"Order Id",selector:e=>e.orderid,wrap:!0,sortable:!0},{name:"Status",selector:e=>e.status,sortable:!0},{name:"obj",selector:e=>e.obj,omit:!0}],S=(0,m.useMutation)("CancelOrderApi",(()=>(0,u.zx)(t,j.obj.orderid).then((e=>{var t;e.status?(v.delete(j.obj.id),N({show:!0,res:e}),t=j.ind,n((e=>{const a=[...e];return a.splice(t,1),a}))):N({show:!0,res:e}),f({status:!1,obj:null,ind:null})})).catch((e=>(N({show:!0,res:e}),Promise.reject(e)))))),Z=(0,m.useMutation)("ExecuteOrderNow",(()=>(0,u.Ww)(t,g.obj.orderid).then((e=>{e.status,N({show:!0,res:e}),y({status:!1,obj:null,ind:null})})).catch((e=>(N({show:!0,res:e}),Promise.reject(e))))));const{isDarkTheme:L}=(0,x.F)();return(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)("div",{className:"table-responsive mt-3",children:[(0,b.jsx)(c.Z.Group,{children:(0,b.jsx)(c.Z.Control,{type:"text",placeholder:"Search",className:"".concat(L&&"dark-form-control"," mb-2 searchbox-style"),onChange:e=>{var t=e.target.value,a=null===t||void 0===t?void 0:t.toLowerCase(),r=[];for(let n of v.values()){var s,o,l,c,u,m,h,p,x,b,j,f;const e=(0,d.l)(null===n||void 0===n?void 0:n.time),t=(0,i.SX)(null===n||void 0===n?void 0:n.status,null===n||void 0===n?void 0:n.reason,n),v=null===t||void 0===t||null===(s=t.props)||void 0===s?void 0:s.children;(null!==(o=n.symbol)&&void 0!==o&&o.toLowerCase().includes(a.toLowerCase())||null!==(l=n.transaction)&&void 0!==l&&l.toLowerCase().includes(a.toLowerCase())||null!==(c=n.product)&&void 0!==c&&c.toLowerCase().includes(a.toLowerCase())||null!==(u=n.ordertype)&&void 0!==u&&u.toLowerCase().includes(a.toLowerCase())||null!==(m=n.quantity)&&void 0!==m&&null!==(h=m.toString())&&void 0!==h&&h.toLowerCase().includes(a.toLowerCase())||null!==(p=n.price)&&void 0!==p&&null!==(x=p.toString())&&void 0!==x&&x.toLowerCase().includes(a.toLowerCase())||null!==(b=n.orderid)&&void 0!==b&&null!==(j=b.toString())&&void 0!==j&&j.toLowerCase().includes(a.toLowerCase())||null!==(f=n.time)&&void 0!==f&&f.toLowerCase().includes(a.toLowerCase())||null!==e&&void 0!==e&&e.toLowerCase().includes(a.toLowerCase())||null!==v&&void 0!==v&&v.toLowerCase().includes(a.toLowerCase()))&&r.push(n)}n(r.map(((e,t)=>C(t,e))))},required:!0})}),(0,b.jsx)(s.ZP,{columns:k,data:a,pagination:!1,paginationPerPage:10,highlightOnHover:!0,theme:(0,i.gh)()})]}),j.status&&(0,b.jsxs)(l.Z,{show:!0,animation:!0,size:"md",className:"".concat(L&&"dark-modal"," mt-5"),children:[(0,b.jsx)(l.Z.Header,{className:"text-center py-3",children:(0,b.jsx)(l.Z.Title,{className:"text-center",children:(0,b.jsx)("h5",{className:"mb-0",children:"Cancel Order"})})}),(0,b.jsx)(l.Z.Body,{className:"p-10",children:"Do you want cancel order ?"}),(0,b.jsxs)(l.Z.Footer,{className:"py-1 d-flex justify-content-center",children:[(0,b.jsx)("div",{children:(0,b.jsx)(o.Z,{variant:"outline-danger",onClick:()=>f({status:!1,id:""}),children:"No"})}),(0,b.jsx)("div",{children:(0,b.jsx)(o.Z,{variant:"outline-primary",className:"mx-2 px-3",onClick:()=>S.mutate(),children:"Yes"})})]})]}),g.status&&(0,b.jsxs)(l.Z,{show:!0,animation:!0,size:"md",className:"".concat(L&&"dark-modal"," mt-5"),children:[(0,b.jsx)(l.Z.Header,{className:"text-center py-3",children:(0,b.jsx)(l.Z.Title,{className:"text-center",children:(0,b.jsx)("h5",{className:"mb-0",children:"Execute Order Now"})})}),(0,b.jsx)(l.Z.Body,{className:"p-10",children:"Do you want Execute Order Now ?"}),(0,b.jsxs)(l.Z.Footer,{className:"py-1 d-flex justify-content-center",children:[(0,b.jsx)("div",{children:(0,b.jsx)(o.Z,{variant:"outline-danger",onClick:()=>y({status:!1,id:""}),children:"No"})}),(0,b.jsx)("div",{children:(0,b.jsx)(o.Z,{variant:"outline-primary",className:"mx-2 px-3",onClick:()=>Z.mutate(),children:"Yes"})})]})]}),w.show&&(0,b.jsx)(h.Z,{res:w.res,setApiResponseModal:N,msg:w.res.msg})]})};n()}catch(j){n(j)}}))},8920:(e,t,a)=>{a.a(e,(async(e,n)=>{try{a.d(t,{r:()=>b});var r=a(2791),s=a(3513),o=a(1933),l=a(7691),i=a(1025),c=a(4912),d=a(1662),u=a(3360),m=a(4970),h=a(4536),p=a(184),x=e([l]);l=(x.then?(await x)():x)[0];const b=e=>{const[t,a]=(0,r.useState)({show:!1,res:{}}),[n,x]=(0,r.useState)({status:!1,values:{}}),[b,v]=(0,r.useState)([]),j=(0,r.useMemo)((()=>new Map),[]);const f=(0,o.useMutation)("SquareOff",(t=>(0,l.cd)(t.user_id,t.symbol).then((t=>{t.status&&(a({show:!0,res:t}),e.fetchBrokerData()),x({status:!1,values:{}})})).catch((e=>{console.log("error : ",e),a({show:!0,res:e})}))));function g(t,a){return{index:t+1,tradingsymbol:a.tradingsymbol,producttype:a.producttype,quantity:a.quantity,pnl:(0,m.nZ)(a.pnl),ltp:a.ltp,avgprice:a.avgprice,action:(0,m.le)(a.action),squareoff:(n=a.quantity,r=a.tradingsymbol,s=e.user_id,(0,p.jsxs)("button",{className:0===n?"btn btn-outline-danger":"btn btn-danger btnSquareOff",disabled:0===n,onClick:()=>x({status:!0,values:{user_id:s,symbol:r,is_all:!1}}),children:[" ","Square OFF"]})),obj:a};var n,r,s}(0,r.useEffect)((()=>{var t;!function(e){var t=[];e.map(((e,a)=>t.push(g(a,e)))),v(t)}(null===(t=e.data)||void 0===t?void 0:t.positions),e.data.positions.map(((e,t)=>j.set(e.tradingsymbol,e)))}),[e.data]);const y=[{name:"Id",selector:e=>e.index,sortable:!0,maxWidth:"65px",minWidth:"65px"},{name:"Symbol",selector:e=>e.tradingsymbol,wrap:!0,sortable:!0,wrap:!0},{name:"Type",selector:e=>e.producttype,sortable:!0,wrap:!0},{name:"Qty",selector:e=>e.quantity,sortable:!0,maxWidth:"100px",minWidth:"100px"},{name:"P&L",selector:e=>e.pnl,sortable:!0,sortFunction:(e,t)=>e.obj.pnl>t.obj.pnl?-1:1},{name:"LTP",selector:e=>e.ltp,sortable:!0},{name:"Avg Price",selector:e=>e.avgprice,sortable:!0},{name:"Trans",selector:e=>e.action,sortable:!0,sortFunction:(e,t)=>"B"===e.obj.action||"b"===e.obj.action||"buy"===e.obj.action||"BUY"===e.obj.action||"Buy"===e.obj.action?1:-1},{name:"Square Off",selector:e=>e.squareoff},{name:"obj",selector:e=>e.obj,omit:!0}],{isDarkTheme:w}=(0,h.F)();return(0,p.jsx)(p.Fragment,{children:(0,p.jsxs)("div",{className:"table-responsive",children:[(0,p.jsx)(c.Z.Group,{children:(0,p.jsx)(c.Z.Control,{type:"text",placeholder:"Search",className:"".concat(w&&"dark-form-control"," mb-2 searchbox-style"),onChange:e=>{var t=e.target.value,a=null===t||void 0===t?void 0:t.toLowerCase(),n=[];for(let d of j.values()){var r,s,o,l,i,c;(d.tradingsymbol.toLowerCase().includes(a.toLowerCase())||d.producttype.toLowerCase().includes(a.toLowerCase())||d.quantity.toString().includes(a.toLowerCase())||null!==(r=d.pnl)&&void 0!==r&&null!==(s=r.toString())&&void 0!==s&&s.toLowerCase().includes(a.toLowerCase())||null!==(o=d.ltp)&&void 0!==o&&null!==(l=o.toString())&&void 0!==l&&l.toLowerCase().includes(a.toLowerCase())||null!==(i=d.avgprice)&&void 0!==i&&null!==(c=i.toString())&&void 0!==c&&c.toLowerCase().includes(a.toLowerCase())||d.action.toLowerCase().includes(a.toLowerCase()))&&n.push(d)}v(n.map(((e,t)=>g(t,e))))},required:!0})}),(0,p.jsx)(s.ZP,{columns:y,data:b,pagination:!1,paginationPerPage:10,highlightOnHover:!0,theme:(0,m.gh)()}),t.show&&(0,p.jsx)(i.Z,{res:t.res,setApiResponseModal:a,msg:t.res.msg}),n.status&&(0,p.jsxs)(d.Z,{show:!0,animation:!0,size:"md",className:"".concat(w&&"dark-modal"," mt-5"),children:[(0,p.jsx)(d.Z.Header,{className:"text-center py-3",children:(0,p.jsx)(d.Z.Title,{className:"text-center",children:(0,p.jsx)("h5",{className:"mb-0",children:"Square Off"})})}),(0,p.jsx)(d.Z.Body,{className:"p-10",children:"Do you want to Square Off ?"}),(0,p.jsxs)(d.Z.Footer,{className:"py-1 d-flex justify-content-center",children:[(0,p.jsx)("div",{children:(0,p.jsx)(u.Z,{variant:"outline-danger",onClick:()=>x({status:!1,values:{}}),children:"No"})}),(0,p.jsx)("div",{children:(0,p.jsx)(u.Z,{variant:"outline-primary",className:"mx-2 px-3",onClick:()=>{return e=n.values,void f.mutate(e);var e},children:"Yes"})})]})]})]})})};n()}catch(b){n(b)}}))},3956:(e,t,a)=>{a.d(t,{P:()=>d});var n=a(2791),r=a(3513),s=a(4970),o=a(4912),l=a(3599),i=a(4536),c=a(184);const d=e=>{const[t,a]=(0,n.useState)([]),d=(0,n.useMemo)((()=>new Map),[]);function u(e,t){return{index:e+1,symbol:t.symbol,orderid:t.orderid,product:t.product,transaction:(0,s.le)(t.transaction),price:t.price,time:(0,l.l)(t.time),obj:t}}(0,n.useEffect)((()=>{var t;!function(e){var t=[];e.map(((e,a)=>t.push(u(a,e)))),a(t)}(null===(t=e.data)||void 0===t?void 0:t.trades),e.data.trades.map(((e,t)=>d.set(e.orderid,e)))}),[e.data]);const m=[{name:"Id",selector:e=>e.index,sortable:!0,maxWidth:"65px",minWidth:"65px",wrap:!0},{name:"Symbol",selector:e=>e.symbol,wrap:!0,sortable:!0,maxWidth:"220px",minWidth:"220px"},{name:"Order ID",selector:e=>e.orderid,sortable:!0,wrap:!0},{name:"Product",selector:e=>e.product,sortable:!0,wrap:!0},{name:"Trans",selector:e=>e.transaction,sortable:!0,sortFunction:(e,t)=>"B"===e.obj.transaction||"b"===e.obj.transaction||"buy"===e.obj.transaction||"BUY"===e.obj.transaction||"Buy"===e.obj.transaction?1:-1,wrap:!0},{name:"Price",selector:e=>e.price,sortable:!0,wrap:!0},{name:"Time",selector:e=>e.time,sortable:!0,wrap:!0},{name:"obj",selector:e=>e.obj,omit:!0}],{isDarkTheme:h}=(0,i.F)();return(0,c.jsx)(c.Fragment,{children:(0,c.jsxs)("div",{className:"table-responsive mt-3",children:[(0,c.jsx)(o.Z.Group,{children:(0,c.jsx)(o.Z.Control,{type:"text",placeholder:"Search",className:"".concat(h&&"dark-form-control"," mb-2 searchbox-style"),onChange:e=>{var t=e.target.value,n=null===t||void 0===t?void 0:t.toLowerCase(),r=[];for(let a of d.values()){var s,o,i,c,m,h;const e=(0,l.l)(null===a||void 0===a?void 0:a.time);(null!==(s=a.symbol)&&void 0!==s&&s.toLowerCase().includes(n.toLowerCase())||null!==(o=a.transaction)&&void 0!==o&&o.toLowerCase().includes(n.toLowerCase())||null!==(i=a.orderid)&&void 0!==i&&i.toLowerCase().includes(n.toLowerCase())||a.product.toString().toLowerCase().includes(n.toLowerCase())||null!==(c=a.price)&&void 0!==c&&null!==(m=c.toString())&&void 0!==m&&m.toLowerCase().includes(n.toLowerCase())||null!==e&&void 0!==e&&e.toLowerCase().includes(n.toLowerCase())||null!==(h=a.time)&&void 0!==h&&h.toLowerCase().includes(n.toLowerCase()))&&r.push(a)}a(r.map(((e,t)=>u(t,e))))},required:!0})}),(0,c.jsx)(r.ZP,{columns:m,data:t,pagination:!1,paginationPerPage:10,highlightOnHover:!0,theme:(0,s.gh)()})]})})}},3692:(e,t,a)=>{a.a(e,(async(e,n)=>{try{a.r(t),a.d(t,{MasterTradeBookContent:()=>f,default:()=>y});var r=a(2791),s=a(4880),o=(a(4129),a(4849)),l=a(7878),i=a(3637),c=a(7889),d=a(7691),u=a(8920),m=a(3956),h=a(4970),p=a(7455),x=a(3149),b=a(4536),v=a(184),j=e([c,d,u,p,x]);[c,d,u,p,x]=j.then?(await j)():j;class f extends r.Component{constructor(e){super(e),this.handleTabSelect=e=>{this.setState({selectedTab:e})},this.setPlaceOrderModal=()=>{this.setState({placeOrderModal:{show:!1,res:{}}})},this.handleOpenPlaceOrderModal=()=>{this.setState({placeOrderModal:{show:!0}})},this.state={selectedTab:"positions",isDataLoaded:!1,brokerData:null,isLoading:!1,placeOrderModal:{show:!1,res:{}}},this.fetchBrokerData=this.fetchBrokerData.bind(this)}fetchBrokerData(e,t){this.setState({isLoading:!0}),(0,d.K1)(e,t).then((e=>!!e.status&&(this.setState({brokerData:e,isDataLoaded:!0}),e))).catch((e=>{console.log("error : ",e)})).finally((()=>{this.setState({isLoading:!1})}))}componentDidMount(){const e=this.props.user_id,t=localStorage.getItem("user_id");this.fetchBrokerData(Number(e),Number(t))}render(){var e,t,a;const{brokerData:n,isDataLoaded:r,isLoading:s}=this.state,d=this.props.user_id;if(!r)return(0,v.jsx)("h1",{children:"Loading Tradebook, Orderbook, Positions and P&L from your demat account..."});const{isDarkTheme:p}=this.props;return(0,v.jsxs)("div",{children:[(0,v.jsx)("div",{className:"card ".concat(p&&"dark-theme-card"," mb-4"),children:(0,v.jsx)("div",{className:"card-body py-3",children:(0,v.jsxs)("div",{className:"row align-items-center justify-content-between",children:[(0,v.jsx)("div",{className:"d-flex align-items-center",children:(0,v.jsxs)("button",{className:"btn btn-primary p-1",onClick:()=>window.close(),children:[" ",(0,v.jsx)("i",{className:"mdi mdi-arrow-left-bold m-0",style:{fontSize:"20px"}})," "]})}),(0,v.jsx)("h3",{className:"mb-0 order-tradebook-user-text",children:null===(e=n.data)||void 0===e?void 0:e.demat_name}),(0,v.jsxs)("h3",{className:"mb-0 order-tradebook-user-text",children:[null===(t=n.data)||void 0===t?void 0:t.margin,(0,v.jsx)("sub",{className:"".concat(p?"dark-text":"text-muted"),children:"Margin"})]}),(0,v.jsxs)("h3",{className:"mb-0 order-tradebook-user-text",children:[(0,h.nZ)(null===(a=n.data)||void 0===a?void 0:a.pnl),(0,v.jsx)("sub",{className:"".concat(p?"dark-text":"text-muted"),children:"PnL"})]}),(0,v.jsx)("button",{className:"btn btn-info p-1",onClick:()=>this.fetchBrokerData(d),children:s?(0,v.jsx)(o.Z,{animation:"border",size:"sm"}):(0,v.jsx)("i",{className:"mdi mdi-refresh m-0 text-bold",style:{fontSize:"20px"}})})]})})}),(0,v.jsx)("div",{className:"row customtab",children:(0,v.jsx)("div",{className:"col-lg-12 grid-margin stretch-card",children:(0,v.jsx)("div",{className:"card ".concat(p&&"dark-theme-card"),children:(0,v.jsx)("div",{className:"card-body",children:(0,v.jsxs)(l.Z,{defaultActiveKey:"positions",id:"fill-tab-example",className:"".concat(p&&"dark-nav-tabs"," mb-3"),fill:!0,justify:!0,activeKey:this.state.selectedTab,onSelect:this.handleTabSelect,children:[(0,v.jsx)(i.Z,{eventKey:"positions",title:"Positions",tabClassName:"".concat(p&&"dark-nav-tabs"),children:n&&(0,v.jsx)(u.r,{data:n.data,user_id:d,fetchBrokerData:this.fetchBrokerData})}),(0,v.jsx)(i.Z,{eventKey:"orders",title:"Orders",tabClassName:"".concat(p&&"dark-nav-tabs"),children:n&&(0,v.jsx)(c.q,{data:n.data})}),(0,v.jsx)(i.Z,{eventKey:"trades",title:"Trades",tabClassName:"".concat(p&&"dark-nav-tabs"),children:n&&(0,v.jsx)(m.P,{data:n.data})})]})})})})}),this.state.placeOrderModal.show&&(0,v.jsx)(x.Z,{setPlaceOrderModal:this.setPlaceOrderModal,id:this.props.user_id})]})}}const g=()=>{const{isDarkTheme:e}=(0,b.F)(),{user_id:t}=(0,s.UO)();return(0,v.jsx)(f,{isDarkTheme:e,user_id:t})},y=(0,p.Z)(g);n()}catch(f){n(f)}}))},4970:(e,t,a)=>{a.d(t,{Gr:()=>d,SX:()=>c,UO:()=>s,b4:()=>o,gh:()=>u,le:()=>l,nZ:()=>i});a(2791);var n=a(3513),r=a(184);const s={option:(e,t)=>({...e,backgroundColor:t.isSelected?"blue":"white",color:t.isSelected?"white":"black"})},o={option:(e,t)=>({...s.option(e,t),"&:hover":{backgroundColor:"lightgray",color:"black"}})};function l(e){let t=e.charAt(0),a="S"===t||"s"===t||"sell"===t||"SELL"===t||"Sell"===t;return"B"===t||"b"===t||"buy"===t||"BUY"===t||"Buy"===t?(0,r.jsx)("label",{className:"badge badge-outline-warning",children:"BUY"}):a?(0,r.jsx)("label",{className:"badge badge-outline-primary",children:"SELL"}):e&&(0,r.jsx)("label",{className:"badge badge-outline-danger",children:e.toUpperCase()})}function i(e){return(e=parseFloat(e||0).toFixed(2))<0?(0,r.jsxs)("span",{className:"text-danger",children:[" ",e," ",(0,r.jsx)("i",{className:"mdi mdi-arrow-down"})]}):0===e||null===e?(e=0,(0,r.jsxs)("span",{children:[" ",e]})):(0,r.jsxs)("span",{className:"text-success",children:[" ",e," ",(0,r.jsx)("i",{className:"mdi mdi-arrow-up"})]})}function c(e,t){return"C"===e.charAt(0)||"c"===e.charAt(0)?(0,r.jsx)("label",{className:"badge badge-outline-success","data-toggle":"tooltip","data-placement":"top",title:t,children:"COMPLETED"}):"R"===e.charAt(0)||"r"===e.charAt(0)?(0,r.jsx)("label",{className:"badge badge-outline-danger","data-toggle":"tooltip","data-placement":"top",title:t,children:"REJECTED"}):(0,r.jsx)("label",{className:"badge badge-outline-info","data-toggle":"tooltip","data-placement":"top",title:t,children:e})}function d(e){return isNaN(e)?"...":e<1e3?e.toString():e>=1e3&&e<1e5?(e/1e3).toFixed(2)+" K":e>=1e5&&e<1e7?(e/1e5).toFixed(2)+" L":e>=1e7?(e/1e7).toFixed(2)+" Cr":void 0}const u=()=>"true"===localStorage.getItem("isDarkTheme")?((0,n.jG)("solarized",{background:{default:"transparent"},action:{button:"rgba(0,0,0,.54)",hover:"rgba(0,0,0,.08)",disabled:"rgba(0,0,0,.12)"}},"dark"),"solarized"):((0,n.jG)("resesolarized",{background:{default:"#fff"},action:{button:"rgba(0,0,0,.54)",hover:"rgba(0,0,0,.08)",disabled:"rgba(0,0,0,.12)"}},"light"),"resesolarized")},6960:(e,t,a)=>{a.d(t,{Z:()=>x});var n=a(2791),r=a(4164),s=a(2007),o=function(){return(o=Object.assign||function(e){for(var t,a=1,n=arguments.length;a<n;a++)for(var r in t=arguments[a])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)};var l={success:function(e){return n.createElement("svg",o({viewBox:"0 0 426.667 426.667",width:18,height:18},e),n.createElement("path",{d:"M213.333 0C95.518 0 0 95.514 0 213.333s95.518 213.333 213.333 213.333c117.828 0 213.333-95.514 213.333-213.333S331.157 0 213.333 0zm-39.134 322.918l-93.935-93.931 31.309-31.309 62.626 62.622 140.894-140.898 31.309 31.309-172.203 172.207z",fill:"#6ac259"}))},warn:function(e){return n.createElement("svg",o({viewBox:"0 0 310.285 310.285",width:18,height:18},e),n.createElement("path",{d:"M264.845 45.441C235.542 16.139 196.583 0 155.142 0 113.702 0 74.743 16.139 45.44 45.441 16.138 74.743 0 113.703 0 155.144c0 41.439 16.138 80.399 45.44 109.701 29.303 29.303 68.262 45.44 109.702 45.44s80.399-16.138 109.702-45.44c29.303-29.302 45.44-68.262 45.44-109.701.001-41.441-16.137-80.401-45.439-109.703zm-132.673 3.895a12.587 12.587 0 0 1 9.119-3.873h28.04c3.482 0 6.72 1.403 9.114 3.888 2.395 2.485 3.643 5.804 3.514 9.284l-4.634 104.895c-.263 7.102-6.26 12.933-13.368 12.933H146.33c-7.112 0-13.099-5.839-13.345-12.945L128.64 58.594c-.121-3.48 1.133-6.773 3.532-9.258zm23.306 219.444c-16.266 0-28.532-12.844-28.532-29.876 0-17.223 12.122-30.211 28.196-30.211 16.602 0 28.196 12.423 28.196 30.211.001 17.591-11.456 29.876-27.86 29.876z",fill:"#FFDA44"}))},loading:function(e){return n.createElement("div",o({className:"ct-icon-loading"},e))},info:function(e){return n.createElement("svg",o({viewBox:"0 0 23.625 23.625",width:18,height:18},e),n.createElement("path",{d:"M11.812 0C5.289 0 0 5.289 0 11.812s5.289 11.813 11.812 11.813 11.813-5.29 11.813-11.813S18.335 0 11.812 0zm2.459 18.307c-.608.24-1.092.422-1.455.548a3.838 3.838 0 0 1-1.262.189c-.736 0-1.309-.18-1.717-.539s-.611-.814-.611-1.367c0-.215.015-.435.045-.659a8.23 8.23 0 0 1 .147-.759l.761-2.688c.067-.258.125-.503.171-.731.046-.23.068-.441.068-.633 0-.342-.071-.582-.212-.717-.143-.135-.412-.201-.813-.201-.196 0-.398.029-.605.09-.205.063-.383.12-.529.176l.201-.828c.498-.203.975-.377 1.43-.521a4.225 4.225 0 0 1 1.29-.218c.731 0 1.295.178 1.692.53.395.353.594.812.594 1.376 0 .117-.014.323-.041.617a4.129 4.129 0 0 1-.152.811l-.757 2.68a7.582 7.582 0 0 0-.167.736 3.892 3.892 0 0 0-.073.626c0 .356.079.599.239.728.158.129.435.194.827.194.185 0 .392-.033.626-.097.232-.064.4-.121.506-.17l-.203.827zm-.134-10.878a1.807 1.807 0 0 1-1.275.492c-.496 0-.924-.164-1.28-.492a1.57 1.57 0 0 1-.533-1.193c0-.465.18-.865.533-1.196a1.812 1.812 0 0 1 1.28-.497c.497 0 .923.165 1.275.497.353.331.53.731.53 1.196 0 .467-.177.865-.53 1.193z",fill:"#006DF0"}))},error:function(e){return n.createElement("svg",o({viewBox:"0 0 51.976 51.976",width:18,height:18},e),n.createElement("path",{d:"M44.373 7.603c-10.137-10.137-26.632-10.138-36.77 0-10.138 10.138-10.137 26.632 0 36.77s26.632 10.138 36.77 0c10.137-10.138 10.137-26.633 0-36.77zm-8.132 28.638a2 2 0 0 1-2.828 0l-7.425-7.425-7.778 7.778a2 2 0 1 1-2.828-2.828l7.778-7.778-7.425-7.425a2 2 0 1 1 2.828-2.828l7.425 7.425 7.071-7.071a2 2 0 1 1 2.828 2.828l-7.071 7.071 7.425 7.425a2 2 0 0 1 0 2.828z",fill:"#D80027"}))}},i={success:"#6EC05F",info:"#1271EC",warn:"#FED953",error:"#D60A2E",loading:"#0088ff"},c=function(e){var t,a,r,s,c="margin"+((e.position||"top-center").includes("bottom")?"Bottom":"Top"),d=["ct-toast",e.onClick?" ct-cursor-pointer":"","ct-toast-"+e.type].join(" "),u=((null===(a=e.bar)||void 0===a?void 0:a.size)||"3px")+" "+((null===(r=e.bar)||void 0===r?void 0:r.style)||"solid")+" "+((null===(s=e.bar)||void 0===s?void 0:s.color)||i[e.type]),m=l[e.type],h=(0,n.useState)(((t={opacity:0})[c]=-15,t)),p=h[0],x=h[1],b=o({paddingLeft:e.heading?25:void 0,minHeight:e.heading?50:void 0,borderLeft:u},p),v=function(){var t;x(((t={opacity:0})[c]="-15px",t)),setTimeout((function(){e.onHide(e.id,e.position)}),300)};(0,n.useEffect)((function(){var t,a=setTimeout((function(){var e;x(((e={opacity:1})[c]="15px",e))}),50);return 0!==e.hideAfter&&(t=setTimeout((function(){v()}),1e3*e.hideAfter)),function(){clearTimeout(a),t&&clearTimeout(t)}}),[]),(0,n.useEffect)((function(){e.show||v()}),[e.show]);var j={tabIndex:0,onClick:e.onClick,onKeyPress:function(t){13===t.keyCode&&e.onClick(t)}};return n.createElement("div",o({className:d,role:e.role?e.role:"status",style:b},e.onClick?j:{}),e.renderIcon?e.renderIcon():n.createElement(m,null),n.createElement("div",{className:e.heading?"ct-text-group-heading":"ct-text-group"},e.heading&&n.createElement("h4",{className:"ct-heading"},e.heading),n.createElement("div",{className:"ct-text"},e.text)))};c.propTypes={type:s.string.isRequired,text:(0,s.oneOfType)([s.string,s.node]).isRequired,show:s.bool,onHide:s.func,id:(0,s.oneOfType)([s.string,s.number]),hideAfter:s.number,heading:s.string,position:s.string,renderIcon:s.func,bar:(0,s.shape)({}),onClick:s.func,role:s.string},c.defaultProps={id:void 0,show:!0,onHide:void 0,hideAfter:3,heading:void 0,position:"top-center",renderIcon:void 0,bar:{},onClick:void 0,role:"status"};var d=function(e){return e.replace(/-([a-z])/g,(function(e){return e[1].toUpperCase()}))},u={topLeft:[],topCenter:[],topRight:[],bottomLeft:[],bottomCenter:[],bottomRight:[]},m=function(e){var t=e.toast,a=e.hiddenID,r=(0,n.useState)(u),s=r[0],l=r[1];(0,n.useEffect)((function(){t&&l((function(e){var a,n=d(t.position||"top-center");return o(o({},e),((a={})[n]=function(){for(var e=0,t=0,a=arguments.length;t<a;t++)e+=arguments[t].length;var n=Array(e),r=0;for(t=0;t<a;t++)for(var s=arguments[t],o=0,l=s.length;o<l;o++,r++)n[r]=s[o];return n}(e[n],[t]),a))}))}),[t]);var i=function(e,t){l((function(a){var n,r=d(t||"top-center");return o(o({},a),((n={})[r]=a[r].filter((function(t){return t.id!==e})),n))}))},m=["Left","Center","Right"];return n.createElement(n.Fragment,null,["top","bottom"].map((function(e){return n.createElement("div",{key:"row_"+e,className:"ct-row"},m.map((function(t){var r=""+e+t,l=["ct-group","bottom"===e?"ct-flex-bottom":""].join(" ");return n.createElement("div",{key:r,className:l},s[r].map((function(e){return n.createElement(c,o({key:r+"_"+e.id},e,{id:e.id,text:e.text,type:e.type,onClick:e.onClick,hideAfter:e.hideAfter,show:a!==e.id,onHide:i}))})))})))})))};m.propTypes={toast:(0,s.shape)({}),hiddenID:s.number},m.defaultProps={toast:void 0,hiddenID:void 0};!function(e,t){void 0===t&&(t={});var a=t.insertAt;if(e&&"undefined"!=typeof document){var n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css","top"===a&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}}("#ct-container {\n\tposition: fixed;\n\twidth: 100%;\n\theight: 100vh;\n\ttop: 0px;\n\tleft: 0px;\n\tz-index: 2000;\n\tdisplay: flex;\n\tflex-direction: column;\n\tjustify-content: space-between;\n\tpointer-events: none;\n}\n\n.ct-row {\n\tdisplay: flex;\n\tjustify-content: space-between;\n}\n\n.ct-group {\n\tflex: 1;\n\tmargin: 10px 20px;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n}\n\n.ct-group:first-child {\n\talign-items: flex-start;\n}\n\n.ct-group:last-child {\n\talign-items: flex-end;\n}\n\n.ct-flex-bottom {\n\tjustify-content: flex-end;\n}\n\n.ct-toast {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tpadding: 12px 20px;\n\tbackground-color: #fff;\n\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n\tcolor: #000;\n\tborder-radius: 4px;\n\tmargin: 0px;\n\topacity: 1;\n\ttransition: 0.3s all ease-in-out;\n\tmin-height: 45px;\n\tpointer-events: all;\n}\n\n.ct-toast:focus {\n\toutline: none;\n}\n\n.ct-toast svg {\n\tmin-width: 18px;\n}\n\n.ct-cursor-pointer {\n\tcursor: pointer;\n}\n\n.ct-icon-loading {\n\tdisplay: inline-block;\n\twidth: 20px;\n\theight: 20px;\n}\n\n.ct-icon-loading:after {\n\tcontent: ' ';\n\tdisplay: block;\n\twidth: 14px;\n\theight: 14px;\n\tmargin: 1px;\n\tborder-radius: 50%;\n\tborder: 2px solid #0088ff;\n\tborder-color: #0088ff transparent #0088ff transparent;\n\tanimation: ct-icon-loading 1.2s linear infinite;\n}\n\n@keyframes ct-icon-loading {\n\t0% {\n\t\ttransform: rotate(0deg);\n\t}\n\t100% {\n\t\ttransform: rotate(360deg);\n\t}\n}\n\n.ct-text-group {\n\tmargin-left: 15px;\n}\n\n.ct-text-group-heading {\n\tmargin-left: 25px;\n}\n\n.ct-heading {\n\tfont-size: 18px;\n\tmargin: 0px;\n\tmargin-bottom: 5px;\n}\n\n.ct-text {\n\tfont-size: 14px;\n}\n\n@media (max-width: 768px) {\n\t.ct-row {\n\t\tjustify-content: flex-start;\n\t\tflex-direction: column;\n\t\tmargin: 7px 0px;\n\t}\n\n\t.ct-group {\n\t\tflex: none;\n\t\tmargin: 0px;\n\t}\n\n\t.ct-toast {\n\t\tmargin: 8px 15px;\n\t\twidth: initial;\n\t}\n}\n");var h=0,p=function(e,t){var a,s,l=document.getElementById((null===(a=t)||void 0===a?void 0:a.toastContainerID)||"ct-container");l||((l=document.createElement("div")).id="ct-container",document.body.appendChild(l)),h+=1;var i=1e3*(void 0===(null===(s=t)||void 0===s?void 0:s.hideAfter)?3:t.hideAfter),c=o({id:h,text:e},t);r.render(n.createElement(m,{toast:c}),l);var d=new Promise((function(e){setTimeout((function(){e()}),i)}));return d.hide=function(){r.render(n.createElement(m,{hiddenID:c.id}),l)},d};p.success=function(e,t){return p(e,o(o({},t),{type:"success"}))},p.warn=function(e,t){return p(e,o(o({},t),{type:"warn"}))},p.info=function(e,t){return p(e,o(o({},t),{type:"info"}))},p.error=function(e,t){return p(e,o(o({},t),{type:"error"}))},p.loading=function(e,t){return p(e,o(o({},t),{type:"loading"}))};const x=p},4849:(e,t,a)=>{a.d(t,{Z:()=>u});var n=a(7462),r=a(3366),s=a(1694),o=a.n(s),l=a(2791),i=a(162),c=["bsPrefix","variant","animation","size","children","as","className"],d=l.forwardRef((function(e,t){var a=e.bsPrefix,s=e.variant,d=e.animation,u=e.size,m=e.children,h=e.as,p=void 0===h?"div":h,x=e.className,b=(0,r.Z)(e,c),v=(a=(0,i.vE)(a,"spinner"))+"-"+d;return l.createElement(p,(0,n.Z)({ref:t},b,{className:o()(x,v,u&&v+"-"+u,s&&"text-"+s)}),m)}));d.displayName="Spinner";const u=d},4129:()=>{}}]);
//# sourceMappingURL=692.a6a9cbfe.chunk.js.map
# Hostinger Admin Sync Tool

This tool allows you to download your admin folder from Hostinger shared hosting and automatically sync changes between your local development environment and the live server.

## Features

- 🔄 **Auto-sync**: Automatically uploads changes to your Hostinger server
- 📁 **File watching**: Monitors local file changes in real-time
- 🚀 **Easy setup**: Simple configuration and one-command deployment
- 🔒 **Secure**: Uses SSH/SFTP for secure file transfer
- 📋 **Logging**: Clear console output showing sync status
- 🎯 **Selective sync**: Exclude patterns for files you don't want to sync

## Quick Start

### 1. Setup
```powershell
# Run the setup script
.\setup.ps1
```

### 2. Configure
Edit `sync-config.json` with your Hostinger details:
```json
{
  "hostinger": {
    "host": "yourdomain.com",
    "username": "your_ssh_username",
    "port": 22,
    "remotePath": "/public_html/admin",
    "localPath": "./admin"
  }
}
```

### 3. Download your admin folder
```powershell
.\download-admin.ps1 -Host yourdomain.com -Username your_username
```

### 4. Start auto-sync
```powershell
npm start
```

## Commands

| Command | Description |
|---------|-------------|
| `npm start` | Start the file watcher and auto-sync |
| `npm run download` | Download admin folder from server |
| `npm run upload` | Manual upload of all changes |
| `npm run backup` | Create backup of remote files |

## Configuration Options

### sync-config.json

```json
{
  "hostinger": {
    "host": "your-domain.com",           // Your domain or server IP
    "username": "your-ssh-username",     // SSH username
    "port": 22,                          // SSH port (usually 22)
    "remotePath": "/public_html/admin",  // Path to admin folder on server
    "localPath": "./admin",              // Local folder path
    "excludePatterns": [                 // Files/folders to exclude from sync
      "node_modules/",
      ".git/",
      "*.log",
      ".env",
      "temp/",
      "cache/"
    ]
  },
  "sync": {
    "watchEnabled": true,                // Enable file watching
    "autoUpload": true,                  // Auto-upload on file change
    "backupBeforeSync": true,           // Backup before major changes
    "syncInterval": 5000                // Delay before upload (ms)
  }
}
```

## How It Works

1. **Download**: The tool downloads your entire admin folder from Hostinger
2. **Watch**: It monitors your local admin folder for any file changes
3. **Sync**: When you save a file, it automatically uploads the change to your server
4. **Real-time**: Changes appear on your live website almost instantly

## Workflow

```
Local Development → File Change Detected → Auto Upload → Live Server Updated
```

## Troubleshooting

### SSH Connection Issues
- Ensure SSH access is enabled in your Hostinger control panel
- Verify your SSH username and host are correct
- Check if your IP is whitelisted (if required)

### File Permission Issues
- Make sure your SSH user has write permissions to the admin folder
- Check that the remote path exists and is accessible

### Sync Not Working
- Verify the file isn't in the exclude patterns
- Check console output for error messages
- Ensure stable internet connection

## Security Notes

- Never commit your SSH credentials to version control
- Use SSH keys instead of passwords when possible
- Regularly backup your admin folder before major changes
- Test changes in a staging environment first

## Support

If you encounter issues:
1. Check the console output for error messages
2. Verify your sync-config.json settings
3. Test SSH connection manually: `ssh <EMAIL>`
4. Ensure all dependencies are installed: `npm install`

## Example Usage

After setup, your workflow becomes:
1. Edit files in the local `./admin` folder
2. Save your changes
3. Watch the console - files are automatically uploaded
4. Check your live website to see changes

Perfect for working with an AI agent on your admin panel while seeing real-time results!

# Hostinger Multi-Domain Management System

This comprehensive tool allows you to manage ALL your domains from Hostinger shared hosting locally with real-time synchronization. Work on any domain locally and see changes instantly on your live websites.

## Features

- 🌐 **Multi-Domain Support**: Manage all your domains from one local system
- 🔄 **Real-time Auto-sync**: Automatically uploads changes to your Hostinger server
- 📁 **File watching**: Monitors local file changes across all domains
- 🚀 **Easy setup**: Automatic domain discovery and configuration
- 🔒 **Secure**: Uses SSH/SFTP for secure file transfer
- 📋 **Domain Management**: Enable/disable sync per domain
- 🎯 **Selective sync**: Exclude patterns for files you don't want to sync
- ⚡ **Concurrent uploads**: Multiple domains can sync simultaneously
- 🗂️ **Organized structure**: Each domain in its own local folder

## Quick Start

### 1. Setup
```powershell
# Run the setup script (installs dependencies)
.\setup.ps1
```

### 2. Discover and Configure Domains
```powershell
# Automatically discover all your domains
npm run setup
```

### 3. Download All Domains
```powershell
# Download all domain files to local folders
npm run download
```

### 4. Start Real-time Sync
```powershell
# Start watching and syncing all domains
npm start
```

**Your SSH details are pre-configured:**
- Host: `***************`
- Port: `65002`
- Username: `u352667016`

## Commands

| Command | Description |
|---------|-------------|
| `npm run setup` | Discover and configure all domains |
| `npm run list` | List all configured domains |
| `npm run download` | Download all domains |
| `npm run download-domain` | Interactively select domains to download |
| `npm start` | Start file watcher and auto-sync for all domains |
| `npm run sync` | Alternative sync command |
| `npm run status` | Show current sync status |
| `npm run enable <domain>` | Enable specific domain for sync |
| `npm run disable <domain>` | Disable specific domain from sync |

### Advanced Commands

```powershell
# Download specific domain only
node domain-cli.js download -d yourdomain.com

# Sync only specific domain
node domain-cli.js sync -d yourdomain.com

# Enable/disable domains
node domain-cli.js enable yourdomain.com
node domain-cli.js disable yourdomain.com
```

## Configuration Options

### sync-config.json

```json
{
  "hostinger": {
    "host": "your-domain.com",           // Your domain or server IP
    "username": "your-ssh-username",     // SSH username
    "port": 22,                          // SSH port (usually 22)
    "remotePath": "/public_html/admin",  // Path to admin folder on server
    "localPath": "./admin",              // Local folder path
    "excludePatterns": [                 // Files/folders to exclude from sync
      "node_modules/",
      ".git/",
      "*.log",
      ".env",
      "temp/",
      "cache/"
    ]
  },
  "sync": {
    "watchEnabled": true,                // Enable file watching
    "autoUpload": true,                  // Auto-upload on file change
    "backupBeforeSync": true,           // Backup before major changes
    "syncInterval": 5000                // Delay before upload (ms)
  }
}
```

## How It Works

1. **Download**: The tool downloads your entire admin folder from Hostinger
2. **Watch**: It monitors your local admin folder for any file changes
3. **Sync**: When you save a file, it automatically uploads the change to your server
4. **Real-time**: Changes appear on your live website almost instantly

## Workflow

```
Local Development → File Change Detected → Auto Upload → Live Server Updated
```

## Troubleshooting

### SSH Connection Issues
- Ensure SSH access is enabled in your Hostinger control panel
- Verify your SSH username and host are correct
- Check if your IP is whitelisted (if required)

### File Permission Issues
- Make sure your SSH user has write permissions to the admin folder
- Check that the remote path exists and is accessible

### Sync Not Working
- Verify the file isn't in the exclude patterns
- Check console output for error messages
- Ensure stable internet connection

## Security Notes

- Never commit your SSH credentials to version control
- Use SSH keys instead of passwords when possible
- Regularly backup your admin folder before major changes
- Test changes in a staging environment first

## Support

If you encounter issues:
1. Check the console output for error messages
2. Verify your sync-config.json settings
3. Test SSH connection manually: `ssh <EMAIL>`
4. Ensure all dependencies are installed: `npm install`

## Example Usage

After setup, your workflow becomes:
1. Edit files in the local `./admin` folder
2. Save your changes
3. Watch the console - files are automatically uploaded
4. Check your live website to see changes

Perfect for working with an AI agent on your admin panel while seeing real-time results!

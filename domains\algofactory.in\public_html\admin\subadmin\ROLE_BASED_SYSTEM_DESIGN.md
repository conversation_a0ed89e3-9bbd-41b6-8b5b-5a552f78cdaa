# Role-Based Admin System Design

## System Architecture

### 1. Frontend Layer (Your Custom Admin Panel)
```
┌─────────────────────────────────────────┐
│           Custom Frontend               │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ Super Admin │  │   Sub Admin     │   │
│  │  Dashboard  │  │   Dashboard     │   │
│  └─────────────┘  └─────────────────┘   │
│           │                 │           │
│           └─────────────────┘           │
│                     │                   │
└─────────────────────┼─────────────────────┘
                      │
┌─────────────────────┼─────────────────────┐
│        Permission Middleware            │
│  ┌─────────────────────────────────────┐ │
│  │   Role & Permission Checker         │ │
│  └─────────────────────────────────────┘ │
└─────────────────────┼─────────────────────┘
                      │
┌─────────────────────┼─────────────────────┐
│         Existing Backend API            │
│    https://bpapil1.algodelta.com        │
└─────────────────────────────────────────┘
```

### 2. Database Schema (New Tables Needed)

#### Users Table (Enhanced)
```sql
CREATE TABLE admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(100) UNIQUE,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255),
    role ENUM('super_admin', 'sub_admin', 'viewer'),
    created_by INT, -- References super admin who created this user
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREI<PERSON><PERSON> KEY (created_by) REFERENCES admin_users(id)
);
```

#### Permissions Table
```sql
CREATE TABLE user_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    resource_type ENUM('strategy', 'user', 'broker', 'order', 'position'),
    resource_id VARCHAR(50), -- Can be specific ID or '*' for all
    permission_type ENUM('view', 'edit', 'delete', 'create'),
    granted_by INT, -- Super admin who granted this permission
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES admin_users(id),
    FOREIGN KEY (granted_by) REFERENCES admin_users(id),
    UNIQUE KEY unique_permission (user_id, resource_type, resource_id, permission_type)
);
```

#### Permission Groups (Optional - for easier management)
```sql
CREATE TABLE permission_groups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100),
    description TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id)
);

CREATE TABLE group_permissions (
    group_id INT,
    resource_type ENUM('strategy', 'user', 'broker', 'order', 'position'),
    resource_id VARCHAR(50),
    permission_type ENUM('view', 'edit', 'delete', 'create'),
    FOREIGN KEY (group_id) REFERENCES permission_groups(id)
);

CREATE TABLE user_groups (
    user_id INT,
    group_id INT,
    assigned_by INT,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES admin_users(id),
    FOREIGN KEY (group_id) REFERENCES permission_groups(id),
    FOREIGN KEY (assigned_by) REFERENCES admin_users(id)
);
```

## 3. Implementation Approach

### Option A: Middleware Approach (Recommended)
Create a middleware layer between your frontend and existing backend:

```javascript
// Permission Middleware
class PermissionMiddleware {
    async checkPermission(userId, resource, action, resourceId = null) {
        const permissions = await this.getUserPermissions(userId);
        return this.hasPermission(permissions, resource, action, resourceId);
    }
    
    async filterApiResponse(userId, resource, data) {
        const permissions = await this.getUserPermissions(userId);
        return this.filterDataByPermissions(permissions, resource, data);
    }
}
```

### Option B: Frontend-Only Approach (Simpler)
Store permissions in your custom database and filter on frontend:

```javascript
// Frontend Permission Service
class PermissionService {
    constructor(userPermissions) {
        this.permissions = userPermissions;
    }
    
    canView(resource, resourceId) {
        return this.hasPermission('view', resource, resourceId);
    }
    
    canEdit(resource, resourceId) {
        return this.hasPermission('edit', resource, resourceId);
    }
    
    filterStrategies(strategies) {
        return strategies.filter(strategy => 
            this.canView('strategy', strategy.id)
        );
    }
}
```

## 4. User Interface Design

### Super Admin Dashboard
```
┌─────────────────────────────────────────┐
│  Super Admin Dashboard                  │
├─────────────────────────────────────────┤
│  📊 Overview                            │
│  ├─ Total Sub Admins: 5                 │
│  ├─ Active Strategies: 12               │
│  └─ Total Users: 150                    │
├─────────────────────────────────────────┤
│  👥 Sub Admin Management                │
│  ├─ Create New Sub Admin                │
│  ├─ Manage Permissions                  │
│  └─ View Activity Logs                  │
├─────────────────────────────────────────┤
│  🎯 Strategy Assignment                 │
│  ├─ Assign Strategies to Sub Admins     │
│  └─ View Strategy Permissions           │
├─────────────────────────────────────────┤
│  👤 User Assignment                     │
│  ├─ Assign Users to Sub Admins          │
│  └─ View User Permissions               │
└─────────────────────────────────────────┘
```

### Sub Admin Dashboard (Limited View)
```
┌─────────────────────────────────────────┐
│  Sub Admin Dashboard - John Doe         │
├─────────────────────────────────────────┤
│  📊 My Assigned Resources               │
│  ├─ Strategies: 3 (View: 3, Edit: 1)    │
│  ├─ Users: 25 (View: 25, Edit: 10)      │
│  └─ Orders: View Only                   │
├─────────────────────────────────────────┤
│  🎯 My Strategies                       │
│  ├─ Strategy A (Edit Access) ✏️         │
│  ├─ Strategy B (View Only) 👁️           │
│  └─ Strategy C (View Only) 👁️           │
├─────────────────────────────────────────┤
│  👤 My Users                            │
│  ├─ User Group 1 (10 users) ✏️          │
│  └─ User Group 2 (15 users) 👁️          │
└─────────────────────────────────────────┘
```

## 5. Permission Management Interface

### Create Sub Admin Form
```javascript
const CreateSubAdminForm = () => {
    return (
        <form>
            <input name="username" placeholder="Username" />
            <input name="email" placeholder="Email" />
            <input name="password" placeholder="Password" />
            
            <h3>Strategy Permissions</h3>
            {strategies.map(strategy => (
                <div key={strategy.id}>
                    <span>{strategy.name}</span>
                    <label>
                        <input type="checkbox" name={`strategy_view_${strategy.id}`} />
                        View
                    </label>
                    <label>
                        <input type="checkbox" name={`strategy_edit_${strategy.id}`} />
                        Edit
                    </label>
                </div>
            ))}
            
            <h3>User Permissions</h3>
            {users.map(user => (
                <div key={user.id}>
                    <span>{user.name}</span>
                    <label>
                        <input type="checkbox" name={`user_view_${user.id}`} />
                        View
                    </label>
                    <label>
                        <input type="checkbox" name={`user_edit_${user.id}`} />
                        Edit
                    </label>
                </div>
            ))}
        </form>
    );
};
```

## 6. API Wrapper Functions

```javascript
// Wrapper functions that check permissions before API calls
class PermissionAwareAPI {
    constructor(userPermissions) {
        this.permissions = userPermissions;
        this.api = new AlgoFactoryAPI();
    }
    
    async getStrategies() {
        const allStrategies = await this.api.getStrategies();
        return allStrategies.filter(strategy => 
            this.permissions.canView('strategy', strategy.id)
        );
    }
    
    async updateStrategy(strategyId, data) {
        if (!this.permissions.canEdit('strategy', strategyId)) {
            throw new Error('Permission denied');
        }
        return await this.api.updateStrategy(strategyId, data);
    }
    
    async getUsers() {
        const allUsers = await this.api.getUsers();
        return allUsers.filter(user => 
            this.permissions.canView('user', user.id)
        );
    }
}
```

## 7. Implementation Steps

1. **Phase 1: Setup Custom Database**
   - Create admin_users table
   - Create user_permissions table
   - Seed with your super admin account

2. **Phase 2: Build Authentication**
   - Custom login system for your admin panel
   - JWT token management
   - Permission loading on login

3. **Phase 3: Create Permission Management**
   - Sub admin creation interface
   - Permission assignment interface
   - Permission checking middleware

4. **Phase 4: Build Custom Dashboards**
   - Super admin dashboard
   - Sub admin dashboard with filtered data
   - Role-based navigation

5. **Phase 5: Integrate with Existing API**
   - Wrap existing API calls with permission checks
   - Filter responses based on user permissions
   - Handle permission errors gracefully

Would you like me to start implementing any specific part of this system?

{"version": 3, "file": "static/js/125.1c440bb1.chunk.js", "mappings": "gOAGA,MAuCA,EAvCkBA,GACIC,IAKpB,MAAMC,GAAUC,EAAAA,EAAAA,MA2BhB,OA1BAC,EAAAA,EAAAA,YAAU,KACT,MAAMC,EAAQC,aAAaC,QAAQ,uBAC7B,SAAEC,GAAaC,OAAOC,SACtBC,EAAY,WAAaH,EAAW,UAC1C,GAAKH,EAEE,CACN,MAAMO,EAAiB,CACtBC,OAAQ,OACRC,QAAS,CAAE,eAAgB,oBAC3BC,KAAMC,KAAKC,UAAU,CAAEZ,WAExBa,MAAMC,EAAAA,GAAU,eAAgBP,GAC9BQ,MAAMC,GAAaA,EAASC,SAC5BF,MAAMG,IACY,oBAAdA,EAAKC,OACRf,OAAOC,SAASe,KAAOd,EACxB,IAEAe,OAAOC,IACPC,QAAQD,MAAM,SAAUA,GACxBlB,OAAOC,SAASe,KAAOd,CAAS,GAEnC,MAlBCF,OAAOC,SAASe,KAAOd,CAkBxB,GACE,CAACT,KAEG2B,EAAAA,EAAAA,KAAC7B,EAAgB,IAAKC,GAAS,E,yLC5BxC,SAAS6B,IACR,MAAOC,EAAcC,IAAmBC,EAAAA,EAAAA,WAAS,IAC1CC,EAAiBC,IAAsBF,EAAAA,EAAAA,UAAS,OAChDG,EAAWC,IAAgBJ,EAAAA,EAAAA,WAAS,IAkB3C7B,EAAAA,EAAAA,YAAU,KAfTiC,GAAa,IACbC,EAAAA,EAAAA,KAAe,GACblB,MAAMmB,IAENJ,EAAmBI,EAAIhB,MACvBS,GAAgB,EAAK,IAErBN,OAAOc,IACPZ,QAAQa,IAAI,WAAYD,EAAE,IAE1BE,SAAQ,KACRL,GAAa,EAAM,GAKC,GACpB,IAEH,MAAM,YAAEM,IAAgBC,EAAAA,EAAAA,KAExB,OAAKb,GAYJc,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACCjB,EAAAA,EAAAA,KAAA,OAAKkB,UAAU,cAAaD,UAC3BjB,EAAAA,EAAAA,KAAA,MAAIkB,UAAS,cAAAC,OAAgBL,GAAe,mBAAoBG,SAAC,mBAElEjB,EAAAA,EAAAA,KAAA,OAAKkB,UAAU,WAAUD,UACxBjB,EAAAA,EAAAA,KAAA,OAAKkB,UAAU,qCAAoCD,UAClDjB,EAAAA,EAAAA,KAAA,OAAKkB,UAAS,QAAAC,OAAUL,GAAe,mBAAoBG,UAC1DjB,EAAAA,EAAAA,KAAA,OAAKkB,UAAU,YAAWD,SACxBZ,IACAL,EAAAA,EAAAA,KAACoB,EAAAA,EAAqB,CAACC,QAAShB,gBAnBrCL,EAAAA,EAAAA,KAAA,OACCkB,UAAU,mDACVI,MAAO,CAAEC,OAAQ,QAASN,UAE1BjB,EAAAA,EAAAA,KAACwB,EAAAA,EAAO,CAACC,UAAU,SAASH,MAAO,CAAEI,MAAO,OAAQH,OAAQ,WAuBhE,E,0BAEA,SAAeI,EAAAA,EAAAA,GAAS1B,G,sHCoDxB,QAhHA,SAA+B7B,GAAQ,IAADwD,EAAAC,EACrC,MAAMC,EAAmB,OAAL1D,QAAK,IAALA,GAAc,QAATwD,EAALxD,EAAOiD,eAAO,IAAAO,OAAT,EAALA,EAAgBG,QACnC,CAACC,EAAKC,IAAWD,EAAMC,EAAOC,QAC9B,IA4CMC,EAAaC,IAAkBhC,EAAAA,EAAAA,UAAS,IAIzCiC,EAAuB,OAALjE,QAAK,IAALA,GAAc,QAATyD,EAALzD,EAAOiD,eAAO,IAAAQ,OAAT,EAALA,EACrBS,QAAO,CAACL,EAAQM,KAAW,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAC3B,MAAMC,EAAkC,OAAXZ,QAAW,IAAXA,OAAW,EAAXA,EAAaa,cAC1C,OACO,OAANf,QAAM,IAANA,GAAiB,QAAXO,EAANP,EAAQgB,iBAAS,IAAAT,OAAX,EAANA,EACGQ,cACDE,SAASH,EAAqBC,kBAC1B,OAANf,QAAM,IAANA,GAAa,QAAPQ,EAANR,EAAQkB,aAAK,IAAAV,OAAP,EAANA,EACGO,cACDE,SAASH,EAAqBC,kBAC1B,OAANf,QAAM,IAANA,GAAW,QAALS,EAANT,EAAQmB,WAAG,IAAAV,OAAL,EAANA,EACGM,cACDE,SAASH,EAAqBC,kBAC1B,OAANf,QAAM,IAANA,GAAmB,QAAbU,EAANV,EAAQoB,mBAAW,IAAAV,OAAb,EAANA,EACGK,cACDE,SAASH,EAAqBC,kBAC1B,OAANf,QAAM,IAANA,GAAc,QAARW,EAANX,EAAQC,cAAM,IAAAU,OAAR,EAANA,EACGU,WACDJ,SAASH,EAAqBC,kBAC1B,OAANf,QAAM,IAANA,GAEmB,QAFbY,EAANZ,EAAQsB,SACNC,MAAM,KAAK,GACXC,QAAQ,IAAK,YAAI,IAAAZ,OAFb,EAANA,EAGGG,cACDE,SAASH,EAAqBC,kBAC1B,OAANf,QAAM,IAANA,GAAgB,QAAVa,EAANb,EAAQsB,gBAAQ,IAAAT,OAAV,EAANA,EACGE,cACDE,SAASH,EAAqBC,eAAc,IAG/CU,KAAI,CAACzB,EAAQM,KAAK,IAAWN,EAAQM,MAAOA,EAAQ,OAEhD,YAAEzB,IAAgBC,EAAAA,EAAAA,KACxB,OACCC,EAAAA,EAAAA,MAAA2C,EAAAA,SAAA,CAAA1C,SAAA,EACCD,EAAAA,EAAAA,MAAA,MAAAC,SAAA,CAAI,sBAAoBa,MACxBd,EAAAA,EAAAA,MAAA,OAAKE,UAAU,mBAAkBD,SAAA,EAChCjB,EAAAA,EAAAA,KAAC4D,EAAAA,EAAKC,MAAK,CAAA5C,UACVjB,EAAAA,EAAAA,KAAC4D,EAAAA,EAAKE,QAAO,CACZ5C,UAAS,GAAAC,OAAKL,GAAe,oBAAmB,yBAChDiD,KAAK,OACLC,YAAY,SACZC,SA5CiBtD,IACrByB,EAAezB,EAAEuD,OAAOC,MAAM,EA4C1BA,MAAOhC,EACPiC,UAAQ,OAGVpE,EAAAA,EAAAA,KAACqE,EAAAA,GAAS,CACTC,QA5FY,CACf,CACCC,KAAM,KACNC,SAAWC,GAAQA,EAAIlC,MACvBmC,SAAU,OACVC,SAAU,QAEX,CACCJ,KAAM,WACNC,SAAWC,GAAG,GAAAtD,OAAQsD,EAAItB,MAAK,KAAAhC,OAAIsD,EAAIxB,WACvC2B,UAAU,EACVC,MAAM,GAEP,CACCN,KAAM,MACNC,SAAWC,GAAQA,EAAIrB,IACvByB,MAAM,EACND,UAAU,GAEX,CACCL,KAAM,SACNC,SAAWC,GAAQA,EAAIvC,OACvB0C,UAAU,EACVC,MAAM,EACNH,SAAU,QACVC,SAAU,SAEX,CACCJ,KAAM,cACNC,SAAWC,GAAQA,EAAIpB,YACvBuB,UAAU,EACVC,MAAM,GAEP,CACCN,KAAM,OACNC,SAAWC,GAAQA,EAAIlB,SAASC,MAAM,KAAK,GAAGC,QAAQ,IAAK,KAC3DmB,UAAU,EACVC,MAAM,IAwDJnF,KAAM2C,EACNyC,YAAU,EACVC,kBAAmB,GACnBC,SAAUlE,EACVmE,OAAOC,EAAAA,EAAAA,MACPC,kBAAgB,EAChBC,UAAQ,SAKb,C,wHCjHO,MAAMC,EAAwB,CACpCC,OAAQA,CAACC,EAAUC,KAAK,IACpBD,EACHE,gBAAiBD,EAAME,WAAa,OAAS,QAC7CC,MAAOH,EAAME,WAAa,QAAU,WAGzBE,EAAsB,CAClCN,OAAQA,CAACO,EAAML,KAAK,IAChBH,EAAsBC,OAAOO,EAAML,GACtC,UAAW,CACVC,gBAAiB,YACjBE,MAAO,YAsBH,SAASG,EAAmBC,GAClC,IAAIC,EAASD,EAAYE,OAAO,GAO5BC,EACQ,MAAXF,GACW,MAAXA,GACW,SAAXA,GACW,SAAXA,GACW,SAAXA,EACD,MAXY,MAAXA,GACW,MAAXA,GACW,QAAXA,GACW,QAAXA,GACW,QAAXA,GAQOhG,EAAAA,EAAAA,KAAA,SAAOkB,UAAU,8BAA6BD,SAAC,QAC5CiF,GACHlG,EAAAA,EAAAA,KAAA,SAAOkB,UAAU,8BAA6BD,SAAC,SAGrD8E,IACC/F,EAAAA,EAAAA,KAAA,SAAOkB,UAAU,6BAA4BD,SAC3C8E,EAAYI,eAKlB,CAqBO,SAASC,EAAmBC,GAElC,OADAA,EAASC,WAAWD,GAAU,GAAGE,QAAQ,IAC5B,GAEXvF,EAAAA,EAAAA,MAAA,QAAME,UAAU,cAAaD,SAAA,CAC3B,IACAoF,EAAO,KAACrG,EAAAA,EAAAA,KAAA,KAAGkB,UAAU,0BAGH,IAAXmF,GAA2B,OAAXA,GAC1BA,EAAS,GACFrF,EAAAA,EAAAA,MAAA,QAAAC,SAAA,CAAM,IAAEoF,OAGfrF,EAAAA,EAAAA,MAAA,QAAME,UAAU,eAAcD,SAAA,CAC5B,IACAoF,EAAO,KAACrG,EAAAA,EAAAA,KAAA,KAAGkB,UAAU,uBAGzB,CAiBO,SAASsF,EAAeC,EAAQC,GACtC,MAAyB,MAArBD,EAAOR,OAAO,IAAmC,MAArBQ,EAAOR,OAAO,IAE5CjG,EAAAA,EAAAA,KAAA,SACCkB,UAAU,8BACV,cAAY,UACZ,iBAAe,MACfyF,MAAOD,EAAOzF,SACd,cAI6B,MAArBwF,EAAOR,OAAO,IAAmC,MAArBQ,EAAOR,OAAO,IAEnDjG,EAAAA,EAAAA,KAAA,SACCkB,UAAU,6BACV,cAAY,UACZ,iBAAe,MACfyF,MAAOD,EAAOzF,SACd,cAiBDjB,EAAAA,EAAAA,KAAA,SACCkB,UAAU,2BACV,cAAY,UACZ,iBAAe,MACfyF,MAAOD,EAAOzF,SAEbwF,GAIL,CAEO,SAASG,EAAmB1E,GAClC,OAAI2E,MAAM3E,GACF,MAEJA,EAAS,IACLA,EAAOoB,WAEXpB,GAAU,KAAQA,EAAS,KACtBA,EAAS,KAAMqE,QAAQ,GAAK,KAEjCrE,GAAU,KAAUA,EAAS,KACxBA,EAAS,KAAQqE,QAAQ,GAAK,KAEnCrE,GAAU,KACLA,EAAS,KAAUqE,QAAQ,GAAK,WADzC,CAGD,CAKO,MAoCMrB,EAAWA,IACqC,SAAxCzG,aAAaC,QAAQ,iBApCzCoI,EAAAA,EAAAA,IACC,YACA,CACCC,WAAY,CACXC,QAAS,eAEVC,OAAQ,CACPC,OAAQ,kBACRC,MAAO,kBACPC,SAAU,oBAGZ,QA4BO,eAtBRN,EAAAA,EAAAA,IACC,gBACA,CACCC,WAAY,CACXC,QAAS,QAEVC,OAAQ,CACPC,OAAQ,kBACRC,MAAO,kBACPC,SAAU,oBAGZ,SAaO,gB,gGCxOLC,EAAY,CAAC,WAAY,UAAW,YAAa,OAAQ,WAAY,KAAM,aAI3E7F,EAAuB8F,EAAAA,YAAiB,SAAUC,EAAMC,GAC1D,IAAIC,EAAWF,EAAKE,SAChBC,EAAUH,EAAKG,QACfjG,EAAY8F,EAAK9F,UACjBkG,EAAOJ,EAAKI,KACZ1G,EAAWsG,EAAKtG,SAChB2G,EAAUL,EAAKM,GACfC,OAAwB,IAAZF,EAAqB,MAAQA,EACzC1G,EAAYqG,EAAKrG,UACjB9C,GAAQ2J,EAAAA,EAAAA,GAA8BR,EAAMF,GAG5CW,GADJP,GAAWQ,EAAAA,EAAAA,IAAmBR,EAAU,YACP,IAAMhG,EACvC,OAAoB6F,EAAAA,cAAoBQ,GAAWI,EAAAA,EAAAA,GAAS,CAC1DV,IAAKA,GACJpJ,EAAO,CACR8C,UAAWiH,IAAWjH,EAAW8G,EAAiBL,GAAQK,EAAkB,IAAML,EAAMD,GAAW,QAAUA,KAC3GzG,EACN,IACAO,EAAQ4G,YAAc,UACtB,S", "sources": ["app/components/higher-order/withauth.jsx", "app/user-pages/transactions/Transactions.jsx", "app/user-pages/transactions/UserCreditLedgerTable.jsx", "app/user-pages/ui-helper.jsx", "../node_modules/react-bootstrap/esm/Spinner.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\r\nimport { useHistory } from \"react-router-dom\";\r\nimport { API_URL } from \"../../../Util/constant\";\r\nconst withAuth = (WrappedComponent) => {\r\n\tconst AuthWrapper = (props) => {\r\n\t\t// localStorage.setItem(\r\n\t\t// \t\"admin_access_token\",\r\n\t\t// \t\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************.2pJTNAozJZkRDCJcIqE4OaKIEfq2may8sSTswl637LE\"\r\n\t\t// );\r\n\t\tconst history = useHistory();\r\n\t\tuseEffect(() => {\r\n\t\t\tconst token = localStorage.getItem(\"admin_access_token\");\r\n\t\t\tconst { hostname } = window.location;\r\n\t\t\tconst login_url = \"https://\" + hostname + \"/admin/\";\r\n\t\t\tif (!token) {\r\n\t\t\t\twindow.location.href = login_url;\r\n\t\t\t} else {\r\n\t\t\t\tconst requestOptions = {\r\n\t\t\t\t\tmethod: \"POST\",\r\n\t\t\t\t\theaders: { \"Content-Type\": \"application/json\" },\r\n\t\t\t\t\tbody: JSON.stringify({ token }),\r\n\t\t\t\t};\r\n\t\t\t\tfetch(API_URL + \"/auth/verify\", requestOptions)\r\n\t\t\t\t\t.then((response) => response.json())\r\n\t\t\t\t\t.then((data) => {\r\n\t\t\t\t\t\tif (data.code === \"token_not_valid\") {\r\n\t\t\t\t\t\t\twindow.location.href = login_url;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((error) => {\r\n\t\t\t\t\t\tconsole.error(\"Error:\", error);\r\n\t\t\t\t\t\twindow.location.href = login_url;\r\n\t\t\t\t\t});\r\n\t\t\t}\r\n\t\t}, [history]);\r\n\r\n\t\treturn <WrappedComponent {...props} />;\r\n\t};\r\n\r\n\treturn AuthWrapper;\r\n};\r\n\r\nexport default withAuth;\r\n", "import React, { useState, useEffect } from \"react\";\r\nimport \"../userpages.css\";\r\nimport { Spinner } from \"react-bootstrap\";\r\nimport { GetLedgerData } from \"../../../services/backendServices\";\r\nimport withAuth from \"../../components/higher-order/withauth\";\r\nimport UserCreditLedgerTable from \"./UserCreditLedgerTable\";\r\nimport { useTheme } from \"../../context/ThemeContext\";\r\n\r\nfunction Transactions() {\r\n\tconst [isDataLoaded, setIsDataLoaded] = useState(false);\r\n\tconst [transactionData, setTransactionData] = useState(null);\r\n\tconst [isLoading, setIsLoading] = useState(false);\r\n\r\n\tconst fetchTransactionData = () => {\r\n\t\tsetIsLoading(true); // set loading state when API call is made\r\n\t\tGetLedgerData(-1)\r\n\t\t\t.then((res) => {\r\n\t\t\t\t// console.log(res.data);\r\n\t\t\t\tsetTransactionData(res.data);\r\n\t\t\t\tsetIsDataLoaded(true);\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t})\r\n\t\t\t.finally(() => {\r\n\t\t\t\tsetIsLoading(false); // clear loading state when API response is received\r\n\t\t\t});\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tfetchTransactionData();\r\n\t}, []);\r\n\r\n\tconst { isDarkTheme } = useTheme()\r\n\r\n\tif (!isDataLoaded) {\r\n\t\treturn (\r\n\t\t\t<div\r\n\t\t\t\tclassName=\"d-flex justify-content-center align-items-center\"\r\n\t\t\t\tstyle={{ height: \"100%\" }}\r\n\t\t\t>\r\n\t\t\t\t<Spinner animation=\"border\" style={{ width: \"5rem\", height: \"5rem\" }} />\r\n\t\t\t</div>\r\n\t\t);\r\n\t}\r\n\r\n\treturn (\r\n\t\t<div>\r\n\t\t\t<div className=\"page-header\">\r\n\t\t\t\t<h3 className={`page-title ${isDarkTheme && \"dark-page-title\"}`}>Transaction</h3>\r\n\t\t\t</div>\r\n\t\t\t<div className=\"row mt-3\">\r\n\t\t\t\t<div className=\"col-lg-12 grid-margin stretch-card\">\r\n\t\t\t\t\t<div className={`card ${isDarkTheme && \"dark-theme-card\"}`}>\r\n\t\t\t\t\t\t<div className=\"card-body\">\r\n\t\t\t\t\t\t\t{transactionData && (\r\n\t\t\t\t\t\t\t\t<UserCreditLedgerTable records={transactionData} />\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n}\r\n\r\nexport default withAuth(Transactions);\r\n", "import React, { useState } from \"react\";\r\nimport { Form } from \"react-bootstrap\";\r\nimport DataTable from \"react-data-table-component\";\r\nimport { useTheme } from \"../../context/ThemeContext\";\r\nimport { getTheme } from \"../ui-helper\";\r\n\r\nfunction UserCreditLedgerTable(props) {\r\n\tconst totalAmount = props?.records?.reduce(\r\n\t\t(acc, record) => acc + record.amount,\r\n\t\t0\r\n\t);\r\n\r\n\tconst columns = [\r\n\t\t{\r\n\t\t\tname: \"Id\",\r\n\t\t\tselector: (row) => row.index,\r\n\t\t\tmaxWidth: \"65px\",\r\n\t\t\tminWidth: \"65px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Username\",\r\n\t\t\tselector: (row) => `${row.email} ${row.mobile_no}`,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Tag\",\r\n\t\t\tselector: (row) => row.tag,\r\n\t\t\twrap: true,\r\n\t\t\tsortable: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Amount\",\r\n\t\t\tselector: (row) => row.amount,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t\tmaxWidth: \"120px\",\r\n\t\t\tminWidth: \"120px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Description\",\r\n\t\t\tselector: (row) => row.description,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Time\",\r\n\t\t\tselector: (row) => row.add_time.split(\".\")[0].replace(\"T\", \" \"),\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t];\r\n\r\n\tconst [searchValue, setSearchValue] = useState(\"\");\r\n\tconst handleSearch = (e) => {\r\n\t\tsetSearchValue(e.target.value);\r\n\t};\r\n\tconst filteredRecords = props?.records\r\n\t\t?.filter((record, index) => {\r\n\t\t\tconst searchValueLowerCase = searchValue?.toLowerCase();\r\n\t\t\treturn (\r\n\t\t\t\trecord?.mobile_no\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\trecord?.email\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\trecord?.tag\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\trecord?.description\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\trecord?.amount\r\n\t\t\t\t\t?.toString()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\trecord?.add_time\r\n\t\t\t\t\t.split(\".\")[0]\r\n\t\t\t\t\t.replace(\"T\", \" \")\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\trecord?.add_time\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase())\r\n\t\t\t);\r\n\t\t})\r\n\t\t.map((record, index) => ({ ...record, index: index + 1 }));\r\n\r\n\tconst { isDarkTheme } = useTheme()\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<h3>Pendding Credits : {totalAmount}</h3>\r\n\t\t\t<div className=\"table-responsive\">\r\n\t\t\t\t<Form.Group>\r\n\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"} mb-2 searchbox-style`}\r\n\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\tplaceholder=\"Search\"\r\n\t\t\t\t\t\tonChange={handleSearch}\r\n\t\t\t\t\t\tvalue={searchValue}\r\n\t\t\t\t\t\trequired\r\n\t\t\t\t\t/>\r\n\t\t\t\t</Form.Group>\r\n\t\t\t\t<DataTable\r\n\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\tdata={filteredRecords}\r\n\t\t\t\t\tpagination\r\n\t\t\t\t\tpaginationPerPage={10}\r\n\t\t\t\t\tstriped={!isDarkTheme}\r\n\t\t\t\t\ttheme={getTheme()}\r\n\t\t\t\t\thighlightOnHover\r\n\t\t\t\t\tnoHeader\r\n\t\t\t\t/>\r\n\t\t\t</div>\r\n\t\t</>\r\n\t);\r\n}\r\n\r\nexport default UserCreditLedgerTable;\r\n", "import React from \"react\";\r\nimport { createTheme } from \"react-data-table-component\";\r\n\r\nexport const customStylesForSelect = {\r\n\toption: (provided, state) => ({\r\n\t\t...provided,\r\n\t\tbackgroundColor: state.isSelected ? \"blue\" : \"white\", // Change the background color as desired\r\n\t\tcolor: state.isSelected ? \"white\" : \"black\", // Change the text color as desired\r\n\t}),\r\n};\r\nexport const hoverEffectOnSelect = {\r\n\toption: (base, state) => ({\r\n\t\t...customStylesForSelect.option(base, state),\r\n\t\t\"&:hover\": {\r\n\t\t\tbackgroundColor: \"lightgray\", // Change the hover background color\r\n\t\t\tcolor: \"black\", // Change the hover text color\r\n\t\t},\r\n\t}),\r\n};\r\n\r\nexport const createDataTableTheme = () => {\r\n\tcreateTheme(\r\n\t\t\"solarized\",\r\n\t\t{\r\n\t\t\tbackground: {\r\n\t\t\t\tdefault: \"transparent\",\r\n\t\t\t},\r\n\t\t\taction: {\r\n\t\t\t\tbutton: \"rgba(0,0,0,.54)\",\r\n\t\t\t\thover: \"rgba(0,0,0,.08)\",\r\n\t\t\t\tdisabled: \"rgba(0,0,0,.12)\",\r\n\t\t\t},\r\n\t\t},\r\n\t\t\"dark\"\r\n\t);\r\n};\r\n\r\nexport function getTransactionType(transaction) {\r\n\tlet a_type = transaction.charAt(0);\r\n\tlet is_buy =\r\n\t\ta_type === \"B\" ||\r\n\t\ta_type === \"b\" ||\r\n\t\ta_type === \"buy\" ||\r\n\t\ta_type === \"BUY\" ||\r\n\t\ta_type === \"Buy\";\r\n\tlet is_sell =\r\n\t\ta_type === \"S\" ||\r\n\t\ta_type === \"s\" ||\r\n\t\ta_type === \"sell\" ||\r\n\t\ta_type === \"SELL\" ||\r\n\t\ta_type === \"Sell\";\r\n\tif (is_buy) {\r\n\t\treturn <label className=\"badge badge-outline-warning\">BUY</label>;\r\n\t} else if (is_sell) {\r\n\t\treturn <label className=\"badge badge-outline-primary\">SELL</label>;\r\n\t} else {\r\n\t\treturn (\r\n\t\t\ttransaction && (\r\n\t\t\t\t<label className=\"badge badge-outline-danger\">\r\n\t\t\t\t\t{transaction.toUpperCase()}\r\n\t\t\t\t</label>\r\n\t\t\t)\r\n\t\t);\r\n\t}\r\n}\r\n\r\nexport function getPnlComponent(pnlVal) {\r\n\tpnlVal = parseFloat(pnlVal || 0).toFixed(2);\r\n\tif (pnlVal < 0) {\r\n\t\treturn (\r\n\t\t\t'<span class=\"text-danger\"> ' +\r\n\t\t\tpnlVal +\r\n\t\t\t' <i class=\"mdi mdi-arrow-down\"></i></span>'\r\n\t\t);\r\n\t} else if (pnlVal === 0 || pnlVal === null) {\r\n\t\tpnlVal = 0;\r\n\t\treturn \"<span> \" + pnlVal + \"</span>\";\r\n\t}\r\n\treturn (\r\n\t\t'<span class=\"text-success\"> ' +\r\n\t\tpnlVal +\r\n\t\t' <i class=\"mdi mdi-arrow-up\"></i></span>'\r\n\t);\r\n}\r\n\r\nexport function getSubPnlComponent(pnlVal) {\r\n\tpnlVal = parseFloat(pnlVal || 0).toFixed(2);\r\n\tif (pnlVal < 0) {\r\n\t\treturn (\r\n\t\t\t<span className=\"text-danger\">\r\n\t\t\t\t{\" \"}\r\n\t\t\t\t{pnlVal} <i className=\"mdi mdi-arrow-down\"></i>\r\n\t\t\t</span>\r\n\t\t);\r\n\t} else if (pnlVal === 0 || pnlVal === null) {\r\n\t\tpnlVal = 0;\r\n\t\treturn <span> {pnlVal}</span>;\r\n\t}\r\n\treturn (\r\n\t\t<span className=\"text-success\">\r\n\t\t\t{\" \"}\r\n\t\t\t{pnlVal} <i className=\"mdi mdi-arrow-up\"></i>\r\n\t\t</span>\r\n\t);\r\n}\r\n\r\nexport function getSubTransactionType(transaction) {\r\n\tlet a_type = transaction.charAt(0);\r\n\tlet is_buy =\r\n\t\ta_type === \"B\" ||\r\n\t\ta_type === \"b\" ||\r\n\t\ta_type === \"buy\" ||\r\n\t\ta_type === \"BUY\" ||\r\n\t\ta_type === \"Buy\";\r\n\tif (is_buy) {\r\n\t\treturn '<label className=\"badge badge-outline-warning\">BUY</label>';\r\n\t} else {\r\n\t\treturn '<label className=\"badge badge-outline-primary\">SELL</label>';\r\n\t}\r\n}\r\n\r\nexport function getOrderStatus(status, reason) {\r\n\tif (status.charAt(0) === \"C\" || status.charAt(0) === \"c\") {\r\n\t\treturn (\r\n\t\t\t<label\r\n\t\t\t\tclassName=\"badge badge-outline-success\"\r\n\t\t\t\tdata-toggle=\"tooltip\"\r\n\t\t\t\tdata-placement=\"top\"\r\n\t\t\t\ttitle={reason}\r\n\t\t\t>\r\n\t\t\t\tCOMPLETED\r\n\t\t\t</label>\r\n\t\t);\r\n\t} else if (status.charAt(0) === \"R\" || status.charAt(0) === \"r\") {\r\n\t\treturn (\r\n\t\t\t<label\r\n\t\t\t\tclassName=\"badge badge-outline-danger\"\r\n\t\t\t\tdata-toggle=\"tooltip\"\r\n\t\t\t\tdata-placement=\"top\"\r\n\t\t\t\ttitle={reason}\r\n\t\t\t>\r\n\t\t\t\tREJECTED\r\n\t\t\t</label>\r\n\t\t);\r\n\t\t//} else if (status.charAt(0) === \"O\" || status.charAt(0) === \"o\") {\r\n\t\t//   return (\r\n\t\t//     <label\r\n\t\t//       className=\"badge badge-outline-secondary\"\r\n\t\t//       data-toggle=\"tooltip\"\r\n\t\t//       data-placement=\"top\"\r\n\t\t//       title={reason}\r\n\t\t//     >\r\n\t\t//       OPEN\r\n\t\t//     </label>\r\n\t\t//   );\r\n\t} else {\r\n\t\treturn (\r\n\t\t\t<label\r\n\t\t\t\tclassName=\"badge badge-outline-info\"\r\n\t\t\t\tdata-toggle=\"tooltip\"\r\n\t\t\t\tdata-placement=\"top\"\r\n\t\t\t\ttitle={reason}\r\n\t\t\t>\r\n\t\t\t\t{status}\r\n\t\t\t</label>\r\n\t\t);\r\n\t}\r\n}\r\n\r\nexport function getConvertedAmount(amount) {\r\n\tif (isNaN(amount)) {\r\n\t\treturn \"...\";\r\n\t}\r\n\tif (amount < 1000) {\r\n\t\treturn amount.toString();\r\n\t}\r\n\tif (amount >= 1000 && amount < 100000) {\r\n\t\treturn (amount / 1000).toFixed(2) + \" K\";\r\n\t}\r\n\tif (amount >= 100000 && amount < 10000000) {\r\n\t\treturn (amount / 100000).toFixed(2) + \" L\";\r\n\t}\r\n\tif (amount >= 10000000) {\r\n\t\treturn (amount / 10000000).toFixed(2) + \" Cr\";\r\n\t}\r\n}\r\n\r\n\r\n\r\n// Function to create dark theme for DataTable\r\nexport const createDataTableDarkTheme = () => {\r\n\tcreateTheme(\r\n\t\t\"solarized\",\r\n\t\t{\r\n\t\t\tbackground: {\r\n\t\t\t\tdefault: \"transparent\",\r\n\t\t\t},\r\n\t\t\taction: {\r\n\t\t\t\tbutton: \"rgba(0,0,0,.54)\",\r\n\t\t\t\thover: \"rgba(0,0,0,.08)\",\r\n\t\t\t\tdisabled: \"rgba(0,0,0,.12)\",\r\n\t\t\t},\r\n\t\t},\r\n\t\t\"dark\"\r\n\t);\r\n}\r\n\r\n// Function to reset DataTable theme to default values\r\nexport const resetDataTableTheme = () => {\r\n\tcreateTheme(\r\n\t\t\"resesolarized\",\r\n\t\t{\r\n\t\t\tbackground: {\r\n\t\t\t\tdefault: \"#fff\",\r\n\t\t\t},\r\n\t\t\taction: {\r\n\t\t\t\tbutton: \"rgba(0,0,0,.54)\",\r\n\t\t\t\thover: \"rgba(0,0,0,.08)\",\r\n\t\t\t\tdisabled: \"rgba(0,0,0,.12)\",\r\n\t\t\t},\r\n\t\t},\r\n\t\t\"light\"\r\n\t);\r\n}\r\n\r\n// theme color for DATATABLE package\r\nexport const getTheme = () => {\r\n\tconst isDarkTheme = localStorage.getItem(\"isDarkTheme\") === \"true\";\r\n\r\n\tif (isDarkTheme) {\r\n\t\tcreateDataTableDarkTheme();\r\n\t\treturn \"solarized\";\r\n\t} else {\r\n\t\tresetDataTableTheme(); // Reset the theme when switching to light theme\r\n\t\treturn \"resesolarized\";\r\n\t}\r\n};\r\n\r\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nvar _excluded = [\"bsPrefix\", \"variant\", \"animation\", \"size\", \"children\", \"as\", \"className\"];\nimport classNames from 'classnames';\nimport React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nvar Spinner = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var bsPrefix = _ref.bsPrefix,\n      variant = _ref.variant,\n      animation = _ref.animation,\n      size = _ref.size,\n      children = _ref.children,\n      _ref$as = _ref.as,\n      Component = _ref$as === void 0 ? 'div' : _ref$as,\n      className = _ref.className,\n      props = _objectWithoutPropertiesLoose(_ref, _excluded);\n\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'spinner');\n  var bsSpinnerPrefix = bsPrefix + \"-\" + animation;\n  return /*#__PURE__*/React.createElement(Component, _extends({\n    ref: ref\n  }, props, {\n    className: classNames(className, bsSpinnerPrefix, size && bsSpinnerPrefix + \"-\" + size, variant && \"text-\" + variant)\n  }), children);\n});\nSpinner.displayName = 'Spinner';\nexport default Spinner;"], "names": ["WrappedComponent", "props", "history", "useHistory", "useEffect", "token", "localStorage", "getItem", "hostname", "window", "location", "login_url", "requestOptions", "method", "headers", "body", "JSON", "stringify", "fetch", "API_URL", "then", "response", "json", "data", "code", "href", "catch", "error", "console", "_jsx", "Transactions", "isDataLoaded", "setIsDataLoaded", "useState", "transactionData", "setTransactionData", "isLoading", "setIsLoading", "GetLedgerData", "res", "e", "log", "finally", "isDarkTheme", "useTheme", "_jsxs", "children", "className", "concat", "UserCreditLedgerTable", "records", "style", "height", "Spinner", "animation", "width", "<PERSON><PERSON><PERSON>", "_props$records", "_props$records2", "totalAmount", "reduce", "acc", "record", "amount", "searchValue", "setSearchValue", "filteredRecords", "filter", "index", "_record$mobile_no", "_record$email", "_record$tag", "_record$description", "_record$amount", "_record$add_time$spli", "_record$add_time", "searchValueLowerCase", "toLowerCase", "mobile_no", "includes", "email", "tag", "description", "toString", "add_time", "split", "replace", "map", "_Fragment", "Form", "Group", "Control", "type", "placeholder", "onChange", "target", "value", "required", "DataTable", "columns", "name", "selector", "row", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "sortable", "wrap", "pagination", "paginationPerPage", "striped", "theme", "getTheme", "highlightOnHover", "<PERSON><PERSON><PERSON><PERSON>", "customStylesForSelect", "option", "provided", "state", "backgroundColor", "isSelected", "color", "hoverEffectOnSelect", "base", "getTransactionType", "transaction", "a_type", "char<PERSON>t", "is_sell", "toUpperCase", "getSubPnlComponent", "pnlVal", "parseFloat", "toFixed", "getOrderStatus", "status", "reason", "title", "getConvertedAmount", "isNaN", "createTheme", "background", "default", "action", "button", "hover", "disabled", "_excluded", "React", "_ref", "ref", "bsPrefix", "variant", "size", "_ref$as", "as", "Component", "_objectWithoutPropertiesLoose", "bsSpinnerPrefix", "useBootstrapPrefix", "_extends", "classNames", "displayName"], "sourceRoot": ""}
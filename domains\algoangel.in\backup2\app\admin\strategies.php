<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin'])) {
    header('Location: ../login.php');
    exit();
}

// Handle strategy actions (delete, status change)
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'delete':
                if (isset($_POST['strategy_id'])) {
                    $stmt = $conn->prepare("DELETE FROM strategies WHERE id = ?");
                    $stmt->bind_param("i", $_POST['strategy_id']);
                    if ($stmt->execute()) {
                        header('Location: strategies.php');
                        exit();
                    }
                }
                break;
            case 'toggle_status':
                if (isset($_POST['strategy_id'])) {
                    $stmt = $conn->prepare("UPDATE strategies SET status = CASE WHEN status = 'active' THEN 'inactive' ELSE 'active' END WHERE id = ?");
                    $stmt->bind_param("i", $_POST['strategy_id']);
                    if ($stmt->execute()) {
                        header('Location: strategies.php');
                        exit();
                    }
                }
                break;
        }
    }
}

// Get all strategies
$strategies = $conn->query("SELECT s.*, a.fullname as created_by_name 
                           FROM strategies s 
                           LEFT JOIN admin_users a ON s.created_by = a.id 
                           ORDER BY s.created_at DESC");

include '../templates/header.php';
?>

<div class="admin-container">
    <div class="admin-header">
        <h1>Strategy Management</h1>
        <a href="add_strategy.php" class="btn btn-primary">Add New Strategy</a>
    </div>

    <div class="table-responsive">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Description</th>
                    <th>Webhook URL</th>
                    <th>Status</th>
                    <th>Created By</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($strategy = $strategies->fetch_assoc()): ?>
                <tr>
                    <td><?php echo htmlspecialchars($strategy['id']); ?></td>
                    <td><?php echo htmlspecialchars($strategy['name']); ?></td>
                    <td><?php echo htmlspecialchars($strategy['description']); ?></td>
                    <td><?php echo htmlspecialchars($strategy['webhook_url']); ?></td>
                    <td>
                        <span class="status-badge <?php echo $strategy['status']; ?>">
                            <?php echo ucfirst($strategy['status']); ?>
                        </span>
                    </td>
                    <td><?php echo htmlspecialchars($strategy['created_by_name']); ?></td>
                    <td><?php echo date('Y-m-d', strtotime($strategy['created_at'])); ?></td>
                    <td class="actions">
                        <a href="edit_strategy.php?id=<?php echo $strategy['id']; ?>" class="btn btn-small">Edit</a>
                        <a href="assign_strategy.php?id=<?php echo $strategy['id']; ?>" class="btn btn-small btn-info">Assign Users</a>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="toggle_status">
                            <input type="hidden" name="strategy_id" value="<?php echo $strategy['id']; ?>">
                            <button type="submit" class="btn btn-small <?php echo $strategy['status'] == 'active' ? 'btn-warning' : 'btn-success'; ?>">
                                <?php echo $strategy['status'] == 'active' ? 'Deactivate' : 'Activate'; ?>
                            </button>
                        </form>
                        <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this strategy?');">
                            <input type="hidden" name="action" value="delete">
                            <input type="hidden" name="strategy_id" value="<?php echo $strategy['id']; ?>">
                            <button type="submit" class="btn btn-small btn-danger">Delete</button>
                        </form>
                    </td>
                </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
    </div>
</div>

<?php include '../templates/footer.php'; ?> 
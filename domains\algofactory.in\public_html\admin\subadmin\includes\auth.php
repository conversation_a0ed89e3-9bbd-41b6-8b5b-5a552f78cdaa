<?php
class Auth {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        if (session_status() == PHP_SESSION_NONE) {
            session_name(SESSION_NAME);
            session_start();
        }
    }
    
    public function login($username, $password) {
        $stmt = $this->pdo->prepare("
            SELECT id, username, email, password_hash, role, is_active 
            FROM admin_users 
            WHERE (username = ? OR email = ?) AND is_active = 1
        ");
        $stmt->execute([$username, $username]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password_hash'])) {
            $this->createSession($user);
            $this->updateLastLogin($user['id']);
            logActivity($user['id'], 'login', 'User logged in successfully');
            return true;
        }
        
        // Log failed login attempt
        logActivity(null, 'login_failed', "Failed login attempt for: $username");
        return false;
    }
    
    private function createSession($user) {
        $sessionToken = generateRandomToken(32);
        $expiresAt = date('Y-m-d H:i:s', time() + SESSION_TIMEOUT);
        
        // Clean old sessions for this user
        $this->cleanOldSessions($user['id']);
        
        // Store in database
        $stmt = $this->pdo->prepare("
            INSERT INTO admin_sessions (user_id, session_token, expires_at) 
            VALUES (?, ?, ?)
        ");
        $stmt->execute([$user['id'], $sessionToken, $expiresAt]);
        
        // Store in PHP session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['session_token'] = $sessionToken;
        $_SESSION['login_time'] = time();
        
        // Set cookie
        setcookie(
            'admin_session', 
            $sessionToken, 
            time() + SESSION_TIMEOUT, 
            '/', 
            COOKIE_DOMAIN, 
            isset($_SERVER['HTTPS']), 
            true
        );
    }
    
    private function cleanOldSessions($userId) {
        // Remove expired sessions
        $this->pdo->exec("DELETE FROM admin_sessions WHERE expires_at < NOW()");
        
        // Remove old sessions for this user (keep only latest 3)
        $stmt = $this->pdo->prepare("
            DELETE FROM admin_sessions 
            WHERE user_id = ? AND id NOT IN (
                SELECT id FROM (
                    SELECT id FROM admin_sessions 
                    WHERE user_id = ? 
                    ORDER BY created_at DESC 
                    LIMIT 3
                ) AS keep_sessions
            )
        ");
        $stmt->execute([$userId, $userId]);
    }
    
    public function isLoggedIn() {
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['session_token'])) {
            return false;
        }
        
        // Check session timeout
        if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > SESSION_TIMEOUT) {
            $this->logout();
            return false;
        }
        
        // Verify session in database
        $stmt = $this->pdo->prepare("
            SELECT user_id FROM admin_sessions 
            WHERE session_token = ? AND expires_at > NOW()
        ");
        $stmt->execute([$_SESSION['session_token']]);
        
        if ($stmt->fetch()) {
            // Update last activity
            $_SESSION['last_activity'] = time();
            return true;
        }
        
        $this->logout();
        return false;
    }
    
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) return null;
        
        $stmt = $this->pdo->prepare("
            SELECT id, username, email, role, created_at, last_login 
            FROM admin_users 
            WHERE id = ? AND is_active = 1
        ");
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch();
    }
    
    public function logout() {
        if (isset($_SESSION['session_token'])) {
            // Log logout activity
            if (isset($_SESSION['user_id'])) {
                logActivity($_SESSION['user_id'], 'logout', 'User logged out');
            }
            
            // Remove from database
            $stmt = $this->pdo->prepare("DELETE FROM admin_sessions WHERE session_token = ?");
            $stmt->execute([$_SESSION['session_token']]);
        }
        
        // Clear session
        session_destroy();
        setcookie('admin_session', '', time() - 3600, '/', COOKIE_DOMAIN);
        
        // Clear all session variables
        $_SESSION = [];
    }
    
    public function createSubAdmin($username, $email, $password, $permissions) {
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);
        
        try {
            $this->pdo->beginTransaction();
            
            // Create user
            $stmt = $this->pdo->prepare("
                INSERT INTO admin_users (username, email, password_hash, role, created_by) 
                VALUES (?, ?, ?, 'sub_admin', ?)
            ");
            $stmt->execute([$username, $email, $passwordHash, $_SESSION['user_id']]);
            $newUserId = $this->pdo->lastInsertId();
            
            // Add permissions
            $this->addPermissions($newUserId, $permissions);
            
            // Log activity
            logActivity($_SESSION['user_id'], 'create_sub_admin', "Created sub admin: $username (ID: $newUserId)");
            
            $this->pdo->commit();
            return $newUserId;
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            logActivity($_SESSION['user_id'], 'create_sub_admin_failed', "Failed to create sub admin: $username - " . $e->getMessage());
            throw $e;
        }
    }
    
    private function addPermissions($userId, $permissions) {
        if (empty($permissions)) return;
        
        $stmt = $this->pdo->prepare("
            INSERT INTO user_permissions (user_id, resource_type, resource_id, permission_type, granted_by) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        foreach ($permissions as $permission) {
            $stmt->execute([
                $userId,
                $permission['resource_type'],
                $permission['resource_id'],
                $permission['permission_type'],
                $_SESSION['user_id']
            ]);
        }
    }
    
    public function updatePermissions($userId, $permissions) {
        try {
            $this->pdo->beginTransaction();
            
            // Remove existing permissions
            $stmt = $this->pdo->prepare("DELETE FROM user_permissions WHERE user_id = ?");
            $stmt->execute([$userId]);
            
            // Add new permissions
            $this->addPermissions($userId, $permissions);
            
            logActivity($_SESSION['user_id'], 'update_permissions', "Updated permissions for user ID: $userId");
            
            $this->pdo->commit();
            return true;
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            logActivity($_SESSION['user_id'], 'update_permissions_failed', "Failed to update permissions for user ID: $userId - " . $e->getMessage());
            throw $e;
        }
    }
    
    public function deleteSubAdmin($userId) {
        try {
            $this->pdo->beginTransaction();
            
            // Get user info for logging
            $stmt = $this->pdo->prepare("SELECT username FROM admin_users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            // Soft delete (deactivate) instead of hard delete
            $stmt = $this->pdo->prepare("UPDATE admin_users SET is_active = 0 WHERE id = ? AND role != 'super_admin'");
            $stmt->execute([$userId]);
            
            // Remove sessions
            $stmt = $this->pdo->prepare("DELETE FROM admin_sessions WHERE user_id = ?");
            $stmt->execute([$userId]);
            
            logActivity($_SESSION['user_id'], 'delete_sub_admin', "Deactivated sub admin: " . ($user['username'] ?? "ID: $userId"));
            
            $this->pdo->commit();
            return true;
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            logActivity($_SESSION['user_id'], 'delete_sub_admin_failed', "Failed to delete sub admin ID: $userId - " . $e->getMessage());
            throw $e;
        }
    }
    
    public function getAllSubAdmins() {
        $stmt = $this->pdo->prepare("
            SELECT 
                u.id, u.username, u.email, u.created_at, u.last_login, u.is_active,
                creator.username as created_by_username,
                COUNT(p.id) as permission_count
            FROM admin_users u
            LEFT JOIN admin_users creator ON u.created_by = creator.id
            LEFT JOIN user_permissions p ON u.id = p.user_id
            WHERE u.role = 'sub_admin'
            GROUP BY u.id
            ORDER BY u.created_at DESC
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getSubAdminPermissions($userId) {
        $stmt = $this->pdo->prepare("
            SELECT resource_type, resource_id, permission_type 
            FROM user_permissions 
            WHERE user_id = ?
            ORDER BY resource_type, resource_id, permission_type
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    }
    
    private function updateLastLogin($userId) {
        $stmt = $this->pdo->prepare("UPDATE admin_users SET last_login = NOW() WHERE id = ?");
        $stmt->execute([$userId]);
    }
    
    public function changePassword($userId, $oldPassword, $newPassword) {
        // Verify old password
        $stmt = $this->pdo->prepare("SELECT password_hash FROM admin_users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        
        if (!$user || !password_verify($oldPassword, $user['password_hash'])) {
            return false;
        }
        
        // Update password
        $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);
        $stmt = $this->pdo->prepare("UPDATE admin_users SET password_hash = ? WHERE id = ?");
        $stmt->execute([$newPasswordHash, $userId]);
        
        logActivity($userId, 'password_change', 'Password changed successfully');
        return true;
    }
    
    public function getActivityLogs($userId = null, $limit = 50) {
        $sql = "
            SELECT 
                al.*, 
                u.username 
            FROM activity_logs al
            LEFT JOIN admin_users u ON al.user_id = u.id
        ";
        
        $params = [];
        if ($userId) {
            $sql .= " WHERE al.user_id = ?";
            $params[] = $userId;
        }
        
        $sql .= " ORDER BY al.created_at DESC LIMIT ?";
        $params[] = $limit;
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
}
?>

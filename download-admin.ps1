# PowerShell script to download admin folder from Hostinger
param(
    [Parameter(Mandatory=$true)]
    [string]$Host,
    
    [Parameter(Mandatory=$true)]
    [string]$Username,
    
    [string]$Port = "22",
    [string]$RemotePath = "/public_html/admin",
    [string]$LocalPath = "./admin"
)

Write-Host "Starting download from Hostinger..." -ForegroundColor Green

# Check if SCP is available
try {
    scp --help | Out-Null
} catch {
    Write-Host "Error: SCP not found. Please install OpenSSH client or Git Bash." -ForegroundColor Red
    Write-Host "You can install it via: winget install Microsoft.OpenSSH.Beta" -ForegroundColor Yellow
    exit 1
}

# Create local directory if it doesn't exist
if (!(Test-Path $LocalPath)) {
    New-Item -ItemType Directory -Path $LocalPath -Force
    Write-Host "Created local directory: $LocalPath" -ForegroundColor Yellow
}

# Download files using SCP
Write-Host "Downloading files from $Host:$RemotePath to $LocalPath..." -ForegroundColor Blue

$scpCommand = "scp -r -P $Port ${Username}@${Host}:${RemotePath}/* $LocalPath/"

Write-Host "Executing: $scpCommand" -ForegroundColor Gray

try {
    Invoke-Expression $scpCommand
    Write-Host "Download completed successfully!" -ForegroundColor Green
    
    # List downloaded files
    Write-Host "`nDownloaded files:" -ForegroundColor Cyan
    Get-ChildItem $LocalPath -Recurse | Select-Object Name, Length, LastWriteTime | Format-Table
    
} catch {
    Write-Host "Error during download: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please check your SSH credentials and connection." -ForegroundColor Yellow
}

Write-Host "`nNext steps:" -ForegroundColor Magenta
Write-Host "1. Update sync-config.json with your server details"
Write-Host "2. Run: npm install (to install sync dependencies)"
Write-Host "3. Run: node sync-watcher.js (to start auto-sync)"

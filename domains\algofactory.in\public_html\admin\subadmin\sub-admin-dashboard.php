<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/permissions.php';
require_once 'includes/api-client.php';

$auth = new Auth($pdo);

// Check authentication
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$currentUser = $auth->getCurrentUser();

// Only sub-admins can access this page
if ($currentUser['role'] !== 'sub_admin') {
    header('Location: super-admin-dashboard.php');
    exit;
}

$permissions = new PermissionManager($pdo, $currentUser['id']);
$apiClient = new AlgoFactoryAPI();

// Get user's permission summary
$permissionSummary = $permissions->getPermissionSummary();

// Get accessible data based on permissions
$data = [];
try {
    // Get strategies if user has permission
    if ($permissions->hasAnyPermission('strategy')) {
        $strategiesResponse = $apiClient->getMockStrategies(); // Using mock data for demo
        if ($strategiesResponse && isset($strategiesResponse['data'])) {
            $data['strategies'] = $permissions->filterStrategies($strategiesResponse['data']);
        }
    }
    
    // Get users if user has permission
    if ($permissions->hasAnyPermission('user')) {
        $usersResponse = $apiClient->getMockUsers(); // Using mock data for demo
        if ($usersResponse && isset($usersResponse['data'])) {
            $data['users'] = $permissions->filterUsers($usersResponse['data']);
        }
    }
    
    // Get orders if user has permission
    if ($permissions->canView('order')) {
        $ordersResponse = $apiClient->getMockOrders(); // Using mock data for demo
        if ($ordersResponse && isset($ordersResponse['data'])) {
            $data['orders'] = $permissions->filterOrders($ordersResponse['data']);
        }
    }
    
} catch (Exception $e) {
    $error = "Error loading data: " . $e->getMessage();
}

// Get accessible menu items
$menuItems = $permissions->getAccessibleMenuItems();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sub Admin Dashboard - <?php echo APP_NAME; ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header h1 {
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-info span {
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            font-size: 0.9rem;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            text-decoration: none;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .welcome-banner {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .welcome-banner h2 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .welcome-banner p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .permissions-overview {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .permissions-overview h3 {
            color: #28a745;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .permission-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .permission-item {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .permission-item h4 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .permission-list {
            list-style: none;
            font-size: 0.8rem;
        }
        
        .permission-list li {
            color: #666;
            margin-bottom: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .card h3 {
            color: #28a745;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .data-list {
            list-style: none;
            max-height: 250px;
            overflow-y: auto;
        }
        
        .data-list li {
            padding: 0.75rem 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .data-list li:last-child {
            border-bottom: none;
        }
        
        .item-name {
            font-weight: 500;
            color: #333;
        }
        
        .item-details {
            font-size: 0.8rem;
            color: #666;
        }
        
        .item-status {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 1rem;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .action-btn:hover {
            transform: translateY(-3px);
            color: white;
            text-decoration: none;
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
        }
        
        .action-btn.disabled {
            background: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
        }
        
        .action-btn.disabled:hover {
            transform: none;
            box-shadow: none;
        }
        
        .empty-state {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
        
        .empty-state .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .alert-error {
            background: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }
        
        .alert-info {
            background: #e7f3ff;
            color: #0c5460;
            border: 1px solid #b8daff;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
            
            .header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .permission-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-actions {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>👤 Sub Admin Dashboard</h1>
        <div class="user-info">
            <span>🎯 <?php echo htmlspecialchars($currentUser['username']); ?></span>
            <a href="logout.php" class="logout-btn">🚪 Logout</a>
        </div>
    </div>
    
    <div class="container">
        <div class="welcome-banner">
            <h2>🎉 Welcome, <?php echo htmlspecialchars($currentUser['username']); ?>!</h2>
            <p>Access your assigned resources and manage your permitted data</p>
        </div>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-error">
                ❌ <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <div class="permissions-overview">
            <h3>🔐 Your Permissions</h3>
            <div class="permission-grid">
                <div class="permission-item">
                    <h4>🎯 Strategies</h4>
                    <ul class="permission-list">
                        <?php if (!empty($permissionSummary['strategies']['view'])): ?>
                            <li>👁️ View: <?php echo count($permissionSummary['strategies']['view']); ?> strategies</li>
                        <?php endif; ?>
                        <?php if (!empty($permissionSummary['strategies']['edit'])): ?>
                            <li>✏️ Edit: <?php echo count($permissionSummary['strategies']['edit']); ?> strategies</li>
                        <?php endif; ?>
                        <?php if (empty($permissionSummary['strategies']['view']) && empty($permissionSummary['strategies']['edit'])): ?>
                            <li>❌ No strategy access</li>
                        <?php endif; ?>
                    </ul>
                </div>
                
                <div class="permission-item">
                    <h4>👥 Users</h4>
                    <ul class="permission-list">
                        <?php if (!empty($permissionSummary['users']['view'])): ?>
                            <li>👁️ View: <?php echo count($permissionSummary['users']['view']); ?> users</li>
                        <?php endif; ?>
                        <?php if (!empty($permissionSummary['users']['edit'])): ?>
                            <li>✏️ Edit: <?php echo count($permissionSummary['users']['edit']); ?> users</li>
                        <?php endif; ?>
                        <?php if (empty($permissionSummary['users']['view']) && empty($permissionSummary['users']['edit'])): ?>
                            <li>❌ No user access</li>
                        <?php endif; ?>
                    </ul>
                </div>
                
                <div class="permission-item">
                    <h4>📋 Orders</h4>
                    <ul class="permission-list">
                        <?php if ($permissionSummary['orders']['view']): ?>
                            <li>👁️ View orders</li>
                        <?php endif; ?>
                        <?php if ($permissionSummary['orders']['cancel']): ?>
                            <li>❌ Cancel orders</li>
                        <?php endif; ?>
                        <?php if (!$permissionSummary['orders']['view']): ?>
                            <li>❌ No order access</li>
                        <?php endif; ?>
                    </ul>
                </div>
                
                <div class="permission-item">
                    <h4>📊 Positions</h4>
                    <ul class="permission-list">
                        <?php if ($permissionSummary['positions']['view']): ?>
                            <li>👁️ View positions</li>
                        <?php endif; ?>
                        <?php if ($permissionSummary['positions']['close']): ?>
                            <li>🔒 Close positions</li>
                        <?php endif; ?>
                        <?php if (!$permissionSummary['positions']['view']): ?>
                            <li>❌ No position access</li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="dashboard-grid">
            <?php if (isset($data['strategies'])): ?>
            <div class="card">
                <h3>🎯 My Strategies</h3>
                <div class="stat-number"><?php echo count($data['strategies']); ?></div>
                <div class="stat-label">Accessible Strategies</div>
                
                <?php if (!empty($data['strategies'])): ?>
                    <ul class="data-list" style="margin-top: 1rem;">
                        <?php foreach ($data['strategies'] as $strategy): ?>
                            <li>
                                <div>
                                    <div class="item-name"><?php echo htmlspecialchars($strategy['strategy_name']); ?></div>
                                    <div class="item-details">ID: <?php echo $strategy['strategy_id']; ?></div>
                                </div>
                                <div>
                                    <span class="item-status <?php echo $strategy['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                        <?php echo $strategy['is_active'] ? 'Active' : 'Inactive'; ?>
                                    </span>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            
            <?php if (isset($data['users'])): ?>
            <div class="card">
                <h3>👥 My Users</h3>
                <div class="stat-number"><?php echo count($data['users']); ?></div>
                <div class="stat-label">Accessible Users</div>
                
                <?php if (!empty($data['users'])): ?>
                    <ul class="data-list" style="margin-top: 1rem;">
                        <?php foreach ($data['users'] as $user): ?>
                            <li>
                                <div>
                                    <div class="item-name"><?php echo htmlspecialchars($user['name']); ?></div>
                                    <div class="item-details"><?php echo htmlspecialchars($user['email']); ?></div>
                                </div>
                                <div>
                                    <span class="item-status <?php echo $user['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                        <?php echo $user['is_active'] ? 'Active' : 'Inactive'; ?>
                                    </span>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            
            <?php if (isset($data['orders'])): ?>
            <div class="card">
                <h3>📋 Recent Orders</h3>
                <div class="stat-number"><?php echo count($data['orders']); ?></div>
                <div class="stat-label">Accessible Orders</div>
                
                <?php if (!empty($data['orders'])): ?>
                    <ul class="data-list" style="margin-top: 1rem;">
                        <?php foreach ($data['orders'] as $order): ?>
                            <li>
                                <div>
                                    <div class="item-name"><?php echo htmlspecialchars($order['symbol']); ?></div>
                                    <div class="item-details">
                                        <?php echo $order['side']; ?> <?php echo $order['quantity']; ?> @ ₹<?php echo $order['price']; ?>
                                    </div>
                                </div>
                                <div>
                                    <span class="item-status <?php 
                                        echo $order['status'] === 'COMPLETED' ? 'status-active' : 
                                             ($order['status'] === 'PENDING' ? 'status-pending' : 'status-inactive'); 
                                    ?>">
                                        <?php echo $order['status']; ?>
                                    </span>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
        
        <div class="quick-actions">
            <?php foreach ($menuItems as $item): ?>
                <?php if ($item['name'] !== 'Dashboard'): ?>
                    <a href="<?php echo $item['url']; ?>" class="action-btn">
                        <?php echo $item['icon']; ?> <?php echo $item['name']; ?>
                    </a>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
        
        <?php if (empty($data)): ?>
            <div class="alert alert-info">
                ℹ️ You don't have access to any resources yet. Please contact your administrator to assign permissions.
            </div>
        <?php endif; ?>
    </div>
    
    <script>
        // Auto-refresh data every 60 seconds
        let refreshInterval;
        
        function startAutoRefresh() {
            refreshInterval = setInterval(function() {
                // You can add AJAX call here to refresh data without page reload
                console.log('Auto-refresh triggered');
            }, 60000);
        }
        
        // Start auto-refresh when page loads
        document.addEventListener('DOMContentLoaded', startAutoRefresh);
        
        // Stop auto-refresh when page is hidden
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                clearInterval(refreshInterval);
            } else {
                startAutoRefresh();
            }
        });
        
        // Add click tracking
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                console.log('Action clicked:', this.textContent.trim());
            });
        });
    </script>
</body>
</html>

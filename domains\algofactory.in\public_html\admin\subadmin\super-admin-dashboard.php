<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/permissions.php';

$auth = new Auth($pdo);

// Check authentication
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$currentUser = $auth->getCurrentUser();

// Only super admin can access this page
if ($currentUser['role'] !== 'super_admin') {
    header('Location: sub-admin-dashboard.php');
    exit;
}

$permissions = new PermissionManager($pdo, $currentUser['id']);

// Get statistics
$stats = [];
try {
    // Count sub-admins
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM admin_users WHERE role = 'sub_admin' AND is_active = 1");
    $stmt->execute();
    $stats['sub_admins'] = $stmt->fetch()['count'];
    
    // Count total permissions
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM user_permissions");
    $stmt->execute();
    $stats['total_permissions'] = $stmt->fetch()['count'];
    
    // Recent sub-admins
    $stmt = $pdo->prepare("
        SELECT username, email, created_at 
        FROM admin_users 
        WHERE role = 'sub_admin' 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $stats['recent_sub_admins'] = $stmt->fetchAll();
    
    // Recent activity
    $stats['recent_activity'] = $auth->getActivityLogs(null, 10);
    
} catch (Exception $e) {
    $stats['error'] = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Admin Dashboard - <?php echo APP_NAME; ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header h1 {
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-info span {
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            font-size: 0.9rem;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            text-decoration: none;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .welcome-banner {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .welcome-banner h2 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .welcome-banner p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .card h3 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.2rem;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .action-btn:hover {
            transform: translateY(-3px);
            color: white;
            text-decoration: none;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .recent-list {
            list-style: none;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .recent-list li {
            padding: 0.75rem 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .recent-list li:last-child {
            border-bottom: none;
        }
        
        .user-details {
            flex: 1;
        }
        
        .user-name {
            font-weight: 500;
            color: #333;
        }
        
        .user-email {
            font-size: 0.8rem;
            color: #666;
        }
        
        .user-date {
            font-size: 0.8rem;
            color: #999;
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            background: #f8f9fa;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-action {
            font-weight: 500;
            color: #333;
        }
        
        .activity-time {
            font-size: 0.8rem;
            color: #666;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .alert-error {
            background: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }
        
        .empty-state {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
        
        .empty-state .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
            
            .header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>👑 Super Admin Dashboard</h1>
        <div class="user-info">
            <span>👤 <?php echo htmlspecialchars($currentUser['username']); ?></span>
            <a href="logout.php" class="logout-btn">🚪 Logout</a>
        </div>
    </div>
    
    <div class="container">
        <div class="welcome-banner">
            <h2>🎉 Welcome back, <?php echo htmlspecialchars($currentUser['username']); ?>!</h2>
            <p>Manage your sub-admins and control access to your AlgoFactory system</p>
        </div>
        
        <?php if (isset($stats['error'])): ?>
            <div class="alert alert-error">
                ❌ Error loading statistics: <?php echo htmlspecialchars($stats['error']); ?>
            </div>
        <?php endif; ?>
        
        <div class="dashboard-grid">
            <div class="card">
                <h3>👥 Sub Admins</h3>
                <div class="stat-number"><?php echo $stats['sub_admins'] ?? 0; ?></div>
                <div class="stat-label">Active Sub Administrators</div>
            </div>
            
            <div class="card">
                <h3>🔐 Permissions</h3>
                <div class="stat-number"><?php echo $stats['total_permissions'] ?? 0; ?></div>
                <div class="stat-label">Total Permissions Granted</div>
            </div>
            
            <div class="card">
                <h3>⚡ System Status</h3>
                <div style="display: flex; justify-content: space-between; margin-top: 1rem;">
                    <div style="text-align: center;">
                        <div style="font-size: 1.5rem; font-weight: bold; color: #28a745;">✅</div>
                        <div style="font-size: 0.8rem; color: #666;">Database</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 1.5rem; font-weight: bold; color: #667eea;">🎯</div>
                        <div style="font-size: 0.8rem; color: #666;">API Ready</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 1.5rem; font-weight: bold; color: #ffc107;">⚡</div>
                        <div style="font-size: 0.8rem; color: #666;">Active</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="action-buttons">
            <a href="create-sub-admin.php" class="action-btn">
                ➕ Create New Sub Admin
            </a>
            <a href="manage-sub-admins.php" class="action-btn">
                👥 Manage Sub Admins
            </a>
            <a href="manage-permissions.php" class="action-btn">
                🔐 Manage Permissions
            </a>
            <a href="activity-logs.php" class="action-btn">
                📝 View Activity Logs
            </a>
            <a href="api-test.php" class="action-btn">
                🔗 Test API Connection
            </a>
            <a href="settings.php" class="action-btn">
                ⚙️ System Settings
            </a>
        </div>
        
        <div class="dashboard-grid">
            <div class="card">
                <h3>👥 Recent Sub Admins</h3>
                <?php if (!empty($stats['recent_sub_admins'])): ?>
                    <ul class="recent-list">
                        <?php foreach ($stats['recent_sub_admins'] as $subAdmin): ?>
                            <li>
                                <div class="user-details">
                                    <div class="user-name"><?php echo htmlspecialchars($subAdmin['username']); ?></div>
                                    <div class="user-email"><?php echo htmlspecialchars($subAdmin['email']); ?></div>
                                </div>
                                <div class="user-date">
                                    <?php echo date('M j, Y', strtotime($subAdmin['created_at'])); ?>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php else: ?>
                    <div class="empty-state">
                        <div class="icon">👥</div>
                        <p>No sub admins created yet.</p>
                        <a href="create-sub-admin.php" style="color: #667eea;">Create your first sub admin</a>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="card">
                <h3>📝 Recent Activity</h3>
                <?php if (!empty($stats['recent_activity'])): ?>
                    <div style="max-height: 300px; overflow-y: auto;">
                        <?php foreach ($stats['recent_activity'] as $activity): ?>
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <?php
                                    $icons = [
                                        'login' => '🔑',
                                        'logout' => '🚪',
                                        'create_sub_admin' => '➕',
                                        'update_permissions' => '🔐',
                                        'delete_sub_admin' => '🗑️'
                                    ];
                                    echo $icons[$activity['action']] ?? '📝';
                                    ?>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-action">
                                        <?php echo htmlspecialchars($activity['username'] ?? 'System'); ?> 
                                        <?php echo htmlspecialchars($activity['action']); ?>
                                    </div>
                                    <div class="activity-time">
                                        <?php echo date('M j, Y H:i', strtotime($activity['created_at'])); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <div class="icon">📝</div>
                        <p>No recent activity.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script>
        // Auto-refresh stats every 30 seconds
        let refreshInterval;
        
        function startAutoRefresh() {
            refreshInterval = setInterval(function() {
                // You can add AJAX call here to refresh stats without page reload
                console.log('Auto-refresh triggered');
            }, 30000);
        }
        
        // Start auto-refresh when page loads
        document.addEventListener('DOMContentLoaded', startAutoRefresh);
        
        // Stop auto-refresh when page is hidden
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                clearInterval(refreshInterval);
            } else {
                startAutoRefresh();
            }
        });
        
        // Add click tracking for analytics
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                console.log('Action clicked:', this.textContent.trim());
            });
        });
        
        // Add smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>

# Setup script for Hostinger Admin Sync
Write-Host "=== Hostinger Admin Sync Setup ===" -ForegroundColor Cyan

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "✓ Node.js found: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Node.js not found. Please install Node.js from https://nodejs.org/" -ForegroundColor Red
    exit 1
}

# Check if npm is available
try {
    $npmVersion = npm --version
    Write-Host "✓ npm found: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ npm not found. Please install npm." -ForegroundColor Red
    exit 1
}

# Install dependencies
Write-Host "`nInstalling dependencies..." -ForegroundColor Blue
npm install

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Dependencies installed successfully!" -ForegroundColor Green
} else {
    Write-Host "✗ Failed to install dependencies." -ForegroundColor Red
    exit 1
}

# Check for SSH client
try {
    ssh -V 2>&1 | Out-Null
    Write-Host "✓ SSH client found" -ForegroundColor Green
} catch {
    Write-Host "⚠ SSH client not found. Installing OpenSSH..." -ForegroundColor Yellow
    try {
        winget install Microsoft.OpenSSH.Beta
        Write-Host "✓ OpenSSH installed" -ForegroundColor Green
    } catch {
        Write-Host "✗ Failed to install OpenSSH. Please install manually." -ForegroundColor Red
    }
}

Write-Host "`n=== Setup Complete ===" -ForegroundColor Cyan
Write-Host "`nNext steps:" -ForegroundColor Magenta
Write-Host "1. Setup your domains:"
Write-Host "   npm run setup"
Write-Host ""
Write-Host "2. Download all domains:"
Write-Host "   npm run download"
Write-Host ""
Write-Host "3. Start auto-sync:"
Write-Host "   npm start"
Write-Host ""
Write-Host "Available commands:" -ForegroundColor Yellow
Write-Host "   npm run setup       - Discover and configure domains"
Write-Host "   npm run list         - List all domains"
Write-Host "   npm run download     - Download all domains"
Write-Host "   npm run sync         - Start file synchronization"
Write-Host "   npm run status       - Show sync status"
Write-Host "   npm run enable       - Enable domain for sync"
Write-Host "   npm run disable      - Disable domain from sync"
Write-Host ""
Write-Host "Your Hostinger SSH details are already configured:" -ForegroundColor Green
Write-Host "   Host: ***************"
Write-Host "   Port: 65002"
Write-Host "   Username: u352667016"

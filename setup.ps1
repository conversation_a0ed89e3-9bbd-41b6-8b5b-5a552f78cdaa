# Setup script for Hostinger Admin Sync
Write-Host "=== Hostinger Admin Sync Setup ===" -ForegroundColor Cyan

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "✓ Node.js found: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Node.js not found. Please install Node.js from https://nodejs.org/" -ForegroundColor Red
    exit 1
}

# Check if npm is available
try {
    $npmVersion = npm --version
    Write-Host "✓ npm found: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ npm not found. Please install npm." -ForegroundColor Red
    exit 1
}

# Install dependencies
Write-Host "`nInstalling dependencies..." -ForegroundColor Blue
npm install

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Dependencies installed successfully!" -ForegroundColor Green
} else {
    Write-Host "✗ Failed to install dependencies." -ForegroundColor Red
    exit 1
}

# Check for SSH client
try {
    ssh -V 2>&1 | Out-Null
    Write-Host "✓ SSH client found" -ForegroundColor Green
} catch {
    Write-Host "⚠ SSH client not found. Installing OpenSSH..." -ForegroundColor Yellow
    try {
        winget install Microsoft.OpenSSH.Beta
        Write-Host "✓ OpenSSH installed" -ForegroundColor Green
    } catch {
        Write-Host "✗ Failed to install OpenSSH. Please install manually." -ForegroundColor Red
    }
}

Write-Host "`n=== Setup Complete ===" -ForegroundColor Cyan
Write-Host "`nNext steps:" -ForegroundColor Magenta
Write-Host "1. Edit sync-config.json with your Hostinger details:"
Write-Host "   - host: your-domain.com"
Write-Host "   - username: your SSH username"
Write-Host "   - remotePath: path to your admin folder on server"
Write-Host ""
Write-Host "2. Download your admin folder:"
Write-Host "   .\download-admin.ps1 -Host your-domain.com -Username your-username"
Write-Host ""
Write-Host "3. Start auto-sync:"
Write-Host "   npm start"
Write-Host ""
Write-Host "Example config update:" -ForegroundColor Yellow
Write-Host '{
  "hostinger": {
    "host": "yourdomain.com",
    "username": "your_ssh_user",
    "port": 22,
    "remotePath": "/public_html/admin",
    "localPath": "./admin"
  }
}' -ForegroundColor Gray

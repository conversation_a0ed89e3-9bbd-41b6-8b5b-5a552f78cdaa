"use strict";(self.webpackChunkadminpanel=self.webpackChunkadminpanel||[]).push([[283],{7455:(e,a,t)=>{t.a(e,(async(e,s)=>{try{t.d(a,{Z:()=>i});var r=t(2791),o=t(4880),n=t(7971),l=t(184),c=e([n]);n=(c.then?(await c)():c)[0];const i=e=>a=>{const t=(0,o.k6)();return(0,r.useEffect)((()=>{const e=localStorage.getItem("admin_access_token"),{hostname:a}=window.location,t="https://"+a+"/admin/";if(e){const a={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:e})};fetch(n.T5+"/auth/verify",a).then((e=>e.json())).then((e=>{"token_not_valid"===e.code&&(window.location.href=t)})).catch((e=>{console.error("Error:",e),window.location.href=t}))}else window.location.href=t}),[t]),(0,l.jsx)(e,{...a})};s()}catch(i){s(i)}}))},8671:(e,a,t)=>{t.a(e,(async(e,s)=>{try{t.d(a,{Z:()=>N});var r=t(2791),o=t(7691),n=t(3513),l=t(1933),c=t(3360),i=t(4912),d=t(1662),m=t(1025),h=t(6960),u=t(8573),x=t.n(u),j=t(4536),p=t(4970),b=t(184),v=e([o]);function N(e){const[a,t]=(0,r.useState)({show:!1,res:{}}),[s,u]=(0,r.useState)({status:!1,obj:{}}),[v,g]=(0,r.useState)({status:!1,obj:{}}),[N,f]=(0,r.useState)([]),Z=(0,r.useMemo)((()=>new Map),[]),[w,y]=(0,r.useState)(""),[C,k]=(0,r.useState)(""),[S,E]=(0,r.useState)(1),[L,U]=(0,r.useState)(""),[_,F]=(0,r.useState)(""),[D,P]=(0,r.useState)(""),[G,T]=(0,r.useState)(""),[M,q]=(0,r.useState)("");function A(e){e.map(((e,a)=>{const t=e.contact_info.split("/").map((e=>e.trim())),s=t[0],r=t[1],o=e.create_time.slice(0,10),n=e.update_time.slice(0,8);e.search_val=(null===s||void 0===s?void 0:s.toLowerCase())+" "+r+" "+n.toLowerCase()+" "+o.toLowerCase()+" "+e.event.toLowerCase()+" "+e.note.toLowerCase()+" "+e.create_time.slice(0,10).toLowerCase()+" "+e.update_time.slice(0,8).toLowerCase()+e.priority,Z.set(e.id,e)}))}function O(e){var a=[];e.map(((e,t)=>{a.push(z(t,e))})),f(a)}(0,r.useEffect)((()=>{if(e.data.data){var a;const t=Object.values(null===(a=e.data)||void 0===a?void 0:a.data);O(t),A(t)}}),[e.data.data]);const W=e=>{console.log("update ",e),u({status:!0,obj:e}),y(e.note),k(e.event),E(e.priority)};function z(e,a){const t=a.contact_info.split("/").map((e=>e.trim())),s=t[0],r=t[1];return console.log("ddd",a),{index:e+1,email:s,username:(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{children:s}),(0,b.jsx)("div",{children:r})]}),mobile:r,priority:a.priority,event:a.event,note:a.note,create_time:a.create_time.slice(0,10),update_time:a.update_time.slice(0,8),demat:(0,b.jsx)(b.Fragment,{children:(0,b.jsxs)("div",{className:"d-flex align-item-center",children:[(0,b.jsx)(c.Z,{variant:"primary",className:"p-2 ml-2",onClick:()=>W(a),children:(0,b.jsx)("i",{className:"mdi mdi-border-color m-0"})}),(0,b.jsx)(c.Z,{variant:"danger",className:"p-2 ml-2",onClick:()=>g({status:!0,obj:a}),children:(0,b.jsx)("i",{className:"mdi mdi-delete-forever m-0 "})})]})}),obj:a}}const B=()=>{(0,o.Vl)().then((e=>{if(e.status){const a=Object.values(null===e||void 0===e?void 0:e.data);return O(a),A(a),e}return!1})).catch((e=>{console.log("error : ",e)}))},I=x()((e=>{const a=[];for(let t of Z.values())t.search_val.includes(e.toLowerCase())&&a.push(t);f(a.map(((e,a)=>z(a,e))))}),300),H=()=>{u({status:!1,obj:{}}),y("")},R=(0,l.useMutation)("UpdateUserFollowUp",(e=>(0,o.n8)(e).then((e=>e.status?(h.Z.success("".concat(e.msg)),B(),H(),e):(t({show:!0,res:e}),!1))).catch((e=>{console.log("error : ",e),t({show:!0,res:e})})))),Y=(0,l.useMutation)("CreateFolloeUp",(e=>(0,o.yC)(e).then((e=>e.status?(h.Z.success("".concat(e.msg)),B(),J(),e):(t({show:!0,res:e}),!1))).catch((e=>{console.log("error : ",e),t({show:!0,res:e})})))),J=()=>{e.setCreateModel(!1),U(""),P(""),F(""),q(""),T("")},V=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),$=e=>/^\d{10}$/.test(e),{isDarkTheme:K}=(0,j.F)();return(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)("div",{className:"table-responsive",children:[(0,b.jsx)(i.Z.Group,{children:(0,b.jsx)(i.Z.Control,{type:"text",placeholder:"Search",className:"".concat(K&&"dark-form-control"," mb-2 searchbox-style"),onChange:e=>{const a=e.target.value;I(a)},required:!0})}),(0,b.jsx)(n.ZP,{columns:[{name:"Id",selector:e=>e.index,sortable:!0,maxWidth:"60px",minWidth:"60px"},{name:"Contact Info",selector:e=>e.username,sortable:!0,maxWidth:"200px",minWidth:"200px",wrap:!0},{name:"Date",selector:e=>e.create_time,sortable:!0,maxWidth:"110px",minWidth:"110px",wrap:!0},{name:"Updated Time",selector:e=>e.update_time,sortable:!0,maxWidth:"110px",minWidth:"110px",wrap:!0},{name:"Priority",selector:e=>e.priority,sortable:!0,wrap:!0},{name:"Event",selector:e=>e.event,sortable:!0,wrap:!0},{name:"Notes",selector:e=>e.note,sortable:!1,wrap:!0},{name:"Action",selector:e=>e.demat,sortable:!1},{name:"obj",selector:e=>e.obj,omit:!0}],data:N,pagination:!1,paginationPerPage:10,highlightOnHover:!0,noHeader:!0,theme:(0,p.gh)()}),a.show&&(0,b.jsx)(m.Z,{res:a.res,setApiResponseModal:t,msg:a.res.msg})]}),s.status&&(0,b.jsxs)(d.Z,{show:!0,animation:!0,size:"md",className:"".concat(K&&"dark-modal"," mt-5"),children:[(0,b.jsx)(d.Z.Header,{className:"text-center py-3",children:(0,b.jsx)(d.Z.Title,{className:"text-center",children:(0,b.jsx)("h5",{className:"mb-0",children:"Create Notes"})})}),(0,b.jsxs)(i.Z,{className:"mt-2",children:[(0,b.jsx)(d.Z.Body,{children:(0,b.jsxs)("div",{className:"row scroll",children:[(0,b.jsx)("div",{className:"col-md-12",children:(0,b.jsxs)(i.Z.Group,{children:[(0,b.jsx)(i.Z.Label,{className:"".concat(K&&"dark-text"," mb-0"),children:"Priority :"}),(0,b.jsx)(i.Z.Control,{className:"".concat(K&&"dark-form-control"),type:"number",value:S,onChange:e=>E(e.target.value),required:!0})]})}),(0,b.jsx)("div",{className:"col-md-12",children:(0,b.jsxs)(i.Z.Group,{children:[(0,b.jsx)(i.Z.Label,{className:"".concat(K&&"dark-text"," mb-0"),children:"User Event :"}),(0,b.jsx)(i.Z.Control,{className:"".concat(K&&"dark-form-control"),as:"textarea",placeholder:"Enter event",value:C,onChange:e=>k(e.target.value),rows:2,required:!0})]})}),(0,b.jsx)("div",{className:"col-md-12",children:(0,b.jsxs)(i.Z.Group,{children:[(0,b.jsx)(i.Z.Label,{className:"".concat(K&&"dark-text"," mb-0"),children:"User Notes :"}),(0,b.jsx)(i.Z.Control,{className:"".concat(K&&"dark-form-control"),as:"textarea",placeholder:"Enter User Notes",value:w,onChange:e=>y(e.target.value),rows:4,required:!0})]})})]})}),(0,b.jsx)(d.Z.Footer,{className:"py-1 d-flex justify-content-center d-inline",children:(0,b.jsx)("div",{className:"row scroll",children:(0,b.jsxs)("div",{className:"col-md-4 pt-2 d-flex",children:[(0,b.jsx)(c.Z,{type:"button",className:"btn btn-md btn-danger mr-3",onClick:H,children:"Cancel"}),(0,b.jsx)(c.Z,{className:"btn btn-md",onClick:e=>{e.preventDefault();const a={id:s.obj.id,note:w,priority:Number(S),event:C};console.log("custom value",a),R.mutate(a)},children:"Update"})]})})})]})]}),v.status&&(0,b.jsxs)(d.Z,{show:!0,animation:!0,size:"md",className:"".concat(K&&"dark-modal"," mt-5"),children:[(0,b.jsx)(d.Z.Header,{className:"text-center py-3",children:(0,b.jsx)(d.Z.Title,{className:"text-center",children:(0,b.jsx)("h5",{className:"mb-0",children:"Delete Record"})})}),(0,b.jsx)(d.Z.Body,{className:"p-10",children:"Do you want to remove this user record ?"}),(0,b.jsxs)(d.Z.Footer,{className:"py-1 d-flex justify-content-center",children:[(0,b.jsx)("div",{children:(0,b.jsx)(c.Z,{variant:"outline-danger",onClick:()=>g({status:!1,obj:{}}),children:"No"})}),(0,b.jsx)("div",{children:(0,b.jsx)(c.Z,{variant:"outline-primary",className:"mx-2 px-3",onClick:async e=>{e.preventDefault();try{const e=await(0,o.FO)({id:v.obj.id});g({status:!1,obj:{}}),e.status?(h.Z.success("".concat(e.msg)),B()):t({show:!0,res:e})}catch(a){t({show:!0,res:a})}},children:"Yes"})})]})]}),e.showcreateModel&&(0,b.jsxs)(d.Z,{show:!0,animation:!0,size:"md",className:"".concat(K&&"dark-modal"," mt-5"),children:[(0,b.jsx)(d.Z.Header,{className:"text-center py-3",children:(0,b.jsx)(d.Z.Title,{className:"text-center",children:(0,b.jsx)("h5",{className:"mb-0",children:"User Notes"})})}),(0,b.jsxs)(i.Z,{className:"mt-2",children:[(0,b.jsx)(d.Z.Body,{children:(0,b.jsxs)("div",{className:"row scroll",children:[(0,b.jsx)("div",{className:"col-md-12",children:(0,b.jsxs)(i.Z.Group,{children:[(0,b.jsx)(i.Z.Label,{className:"".concat(K&&"dark-text"," mb-0"),children:"User Email ID :"}),(0,b.jsx)(i.Z.Control,{className:"".concat(K&&"dark-form-control"),type:"text",placeholder:"Enter user Email ID",value:L,onChange:e=>U(e.target.value),required:!0})]})}),(0,b.jsx)("div",{className:"col-md-6",children:(0,b.jsxs)(i.Z.Group,{children:[(0,b.jsx)(i.Z.Label,{className:"".concat(K&&"dark-text"," mb-0"),children:"Phone Number :"}),(0,b.jsx)(i.Z.Control,{className:"".concat(K&&"dark-form-control"),type:"number",placeholder:"Enter phone number ID",value:_,onChange:e=>F(e.target.value),required:!0})]})}),(0,b.jsx)("div",{className:"col-md-6",children:(0,b.jsxs)(i.Z.Group,{children:[(0,b.jsx)(i.Z.Label,{className:"".concat(K&&"dark-text"," mb-0"),children:"Priority :"}),(0,b.jsx)(i.Z.Control,{className:"".concat(K&&"dark-form-control"),type:"number",placeholder:"Enter priority",value:D,onChange:e=>P(e.target.value),required:!0})]})}),(0,b.jsx)("div",{className:"col-md-12",children:(0,b.jsxs)(i.Z.Group,{children:[(0,b.jsx)(i.Z.Label,{className:"".concat(K&&"dark-text"," mb-0"),children:"User Event :"}),(0,b.jsx)(i.Z.Control,{className:"".concat(K&&"dark-form-control"),as:"textarea",placeholder:"Enter event",value:M,onChange:e=>q(e.target.value),rows:2,required:!0})]})}),(0,b.jsx)("div",{className:"col-md-12",children:(0,b.jsxs)(i.Z.Group,{children:[(0,b.jsx)(i.Z.Label,{className:"".concat(K&&"dark-text"," mb-0"),children:"User Notes :"}),(0,b.jsx)(i.Z.Control,{className:"".concat(K&&"dark-form-control"),as:"textarea",placeholder:"Enter User Notes",value:G,onChange:e=>T(e.target.value),rows:4,required:!0})]})})]})}),(0,b.jsx)(d.Z.Footer,{className:"py-1 d-flex justify-content-center d-inline",children:(0,b.jsx)("div",{className:"row scroll",children:(0,b.jsxs)("div",{className:"col-md-4 pt-2 d-flex",children:[(0,b.jsx)(c.Z,{type:"button",className:"btn btn-md btn-danger mr-3",onClick:J,children:"Cancel"}),(0,b.jsx)(c.Z,{className:"btn btn-md",onClick:e=>{if(e.preventDefault(),!L||!V(L))return void h.Z.error("Please enter a valid email address.");if(!_||!$(_))return void h.Z.error("Please enter a valid mobile number.");if(!D||isNaN(Number(D)))return void h.Z.error("Please enter a valid priority number.");if(!M)return void h.Z.error("Please enter a event.");if(!G)return void h.Z.error("Please enter a note.");const a={contact_info:L+"/"+_,note:G,priority:Number(D),event:M};console.log("custom value",a),Y.mutate(a)},children:"Create"})]})})})]})]})]})}o=(v.then?(await v)():v)[0],s()}catch(g){s(g)}}))},9283:(e,a,t)=>{t.a(e,(async(e,s)=>{try{t.r(a),t.d(a,{default:()=>j});var r=t(2791),o=t(4849),n=t(1025),l=(t(4129),t(7455)),c=t(7691),i=t(8671),d=(t(1933),t(6960),t(4536)),m=t(184),h=e([l,c,i]);function x(){const[e,a]=(0,r.useState)({show:!1,res:{}}),[t,s]=(0,r.useState)(null),[l,h]=(0,r.useState)(!1),[u,x]=(0,r.useState)(!1),j=()=>{h(!0),(0,c.Vl)().then((e=>e.status?(s(e),h(!1),e):(h(!1),!1))).catch((e=>{h(!1),console.log("error : ",e)}))};(0,r.useEffect)((()=>{j()}),[]);const{isDarkTheme:p}=(0,d.F)();return(0,m.jsxs)("div",{children:[(0,m.jsxs)("div",{className:"d-flex align-items-center justify-content-start mb-4",children:[(0,m.jsx)("h3",{className:"page-title ".concat(p&&"dark-page-title"),children:"Follow Up"}),(0,m.jsxs)("button",{className:"btn btn-primary btn-rounded ml-3",onClick:()=>x(!0),children:[" ","Create Notes"]})]}),(0,m.jsx)("div",{className:"row",children:(0,m.jsx)("div",{className:"col-lg-12 grid-margin stretch-card",children:(0,m.jsx)("div",{className:"card ".concat(p&&"dark-theme-card"),children:(0,m.jsx)("div",{className:"card-body",children:l?(0,m.jsx)("div",{className:"d-flex justify-content-center align-items-center",style:{height:"100%"},children:(0,m.jsx)(o.Z,{animation:"border",style:{width:"5rem",height:"5rem"}})}):t&&(0,m.jsx)(i.Z,{data:t,fetchUserData:j,setCreateModel:x,showcreateModel:u})})})})}),e.show&&(0,m.jsx)(n.Z,{res:e.res,setApiResponseModal:a,msg:e.res.msg})]})}[l,c,i]=h.then?(await h)():h;const j=(0,l.Z)(x);s()}catch(u){s(u)}}))},4970:(e,a,t)=>{t.d(a,{Gr:()=>d,SX:()=>i,UO:()=>o,b4:()=>n,gh:()=>m,le:()=>l,nZ:()=>c});t(2791);var s=t(3513),r=t(184);const o={option:(e,a)=>({...e,backgroundColor:a.isSelected?"blue":"white",color:a.isSelected?"white":"black"})},n={option:(e,a)=>({...o.option(e,a),"&:hover":{backgroundColor:"lightgray",color:"black"}})};function l(e){let a=e.charAt(0),t="S"===a||"s"===a||"sell"===a||"SELL"===a||"Sell"===a;return"B"===a||"b"===a||"buy"===a||"BUY"===a||"Buy"===a?(0,r.jsx)("label",{className:"badge badge-outline-warning",children:"BUY"}):t?(0,r.jsx)("label",{className:"badge badge-outline-primary",children:"SELL"}):e&&(0,r.jsx)("label",{className:"badge badge-outline-danger",children:e.toUpperCase()})}function c(e){return(e=parseFloat(e||0).toFixed(2))<0?(0,r.jsxs)("span",{className:"text-danger",children:[" ",e," ",(0,r.jsx)("i",{className:"mdi mdi-arrow-down"})]}):0===e||null===e?(e=0,(0,r.jsxs)("span",{children:[" ",e]})):(0,r.jsxs)("span",{className:"text-success",children:[" ",e," ",(0,r.jsx)("i",{className:"mdi mdi-arrow-up"})]})}function i(e,a){return"C"===e.charAt(0)||"c"===e.charAt(0)?(0,r.jsx)("label",{className:"badge badge-outline-success","data-toggle":"tooltip","data-placement":"top",title:a,children:"COMPLETED"}):"R"===e.charAt(0)||"r"===e.charAt(0)?(0,r.jsx)("label",{className:"badge badge-outline-danger","data-toggle":"tooltip","data-placement":"top",title:a,children:"REJECTED"}):(0,r.jsx)("label",{className:"badge badge-outline-info","data-toggle":"tooltip","data-placement":"top",title:a,children:e})}function d(e){return isNaN(e)?"...":e<1e3?e.toString():e>=1e3&&e<1e5?(e/1e3).toFixed(2)+" K":e>=1e5&&e<1e7?(e/1e5).toFixed(2)+" L":e>=1e7?(e/1e7).toFixed(2)+" Cr":void 0}const m=()=>"true"===localStorage.getItem("isDarkTheme")?((0,s.jG)("solarized",{background:{default:"transparent"},action:{button:"rgba(0,0,0,.54)",hover:"rgba(0,0,0,.08)",disabled:"rgba(0,0,0,.12)"}},"dark"),"solarized"):((0,s.jG)("resesolarized",{background:{default:"#fff"},action:{button:"rgba(0,0,0,.54)",hover:"rgba(0,0,0,.08)",disabled:"rgba(0,0,0,.12)"}},"light"),"resesolarized")},4129:()=>{}}]);
//# sourceMappingURL=283.182794e2.chunk.js.map
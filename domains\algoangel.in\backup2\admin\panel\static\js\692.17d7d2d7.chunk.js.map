{"version": 3, "file": "static/js/692.17d7d2d7.chunk.js", "mappings": "wHAAO,MAAMA,EAA4BC,IACrC,MAAMC,EAAY,IAAIC,KAAKF,GAG3B,GAAKG,MAAMF,EAAUG,WAWjB,OAAOJ,EAXsB,CAC7B,MAAMK,EAAOJ,EAAUK,cACjBC,GAASN,EAAUO,WAAa,GAAGC,WAAWC,SAAS,EAAG,KAC1DC,EAAMV,EAAUW,UAAUH,WAAWC,SAAS,EAAG,KACjDG,EAAQZ,EAAUa,WAAWL,WAAWC,SAAS,EAAG,KACpDK,EAAUd,EAAUe,aAAaP,WAAWC,SAAS,EAAG,KACxDO,EAAUhB,EAAUiB,aAAaT,WAAWC,SAAS,EAAG,KAE9D,MAAM,GAANS,OAAUd,EAAI,KAAAc,OAAIZ,EAAK,KAAAY,OAAIR,EAAG,KAAAQ,OAAIN,EAAK,KAAAM,OAAIJ,EAAO,KAAAI,OAAIF,EAC1D,CAGA,C,0ICbJ,MAuCA,EAvCkBG,GACIC,IAKpB,MAAMC,GAAUC,EAAAA,EAAAA,MA2BhB,OA1BAC,EAAAA,EAAAA,YAAU,KACT,MAAMC,EAAQC,aAAaC,QAAQ,uBAC7B,SAAEC,GAAaC,OAAOC,SACtBC,EAAY,WAAaH,EAAW,UAC1C,GAAKH,EAEE,CACN,MAAMO,EAAiB,CACtBC,OAAQ,OACRC,QAAS,CAAE,eAAgB,oBAC3BC,KAAMC,KAAKC,UAAU,CAAEZ,WAExBa,MAAMC,EAAAA,GAAU,eAAgBP,GAC9BQ,MAAMC,GAAaA,EAASC,SAC5BF,MAAMG,IACY,oBAAdA,EAAKC,OACRf,OAAOC,SAASe,KAAOd,EACxB,IAEAe,OAAOC,IACPC,QAAQD,MAAM,SAAUA,GACxBlB,OAAOC,SAASe,KAAOd,CAAS,GAEnC,MAlBCF,OAAOC,SAASe,KAAOd,CAkBxB,GACE,CAACT,KAEG2B,EAAAA,EAAAA,KAAC7B,EAAgB,IAAKC,GAAS,E,0PCpBzB,SAAS6B,EAAgB7B,GACvC,MAAO8B,EAAWC,IAAgBC,EAAAA,EAAAA,WAAS,GAG3C,SAASC,IACRjC,EAAMkC,mBAAmB,CAAEC,MAAM,IACjCJ,GAAa,EACd,CACA,MAAOK,EAAkBC,IAAuBL,EAAAA,EAAAA,UAAS,CACxDG,MAAM,EACNG,IAAK,CAAC,KAGAC,EAAKC,IAAUR,EAAAA,EAAAA,UAAS,IACxBS,EAAgBC,IAAqBV,EAAAA,EAAAA,UAAS,CACpDW,MAAO,MACPC,MAAO,SAEDC,EAAoBC,IAAyBd,EAAAA,EAAAA,UAAS,CAC5DW,MAAO,KACPC,MAAO,QAGDG,EAAoBC,IAAyBhB,EAAAA,EAAAA,UAAS,CAC5DW,MAAO,SACPC,MAAO,YAGDK,EAAuBC,IAA4BlB,EAAAA,EAAAA,UAAS,CAClEW,MAAO,MACPC,MAAO,SAqDDO,EAAQC,IAAapB,EAAAA,EAAAA,aACrBqB,EAAcC,IAAmBtB,EAAAA,EAAAA,UAAS,IAAInD,OAC9C0E,EAAYC,IAAiBxB,EAAAA,EAAAA,aAC7ByB,EAAcC,IAAmB1B,EAAAA,EAAAA,YAElC2B,GAAoBC,EAAAA,EAAAA,aAAY,iBAAkBC,IAChDC,EAAAA,EAAAA,IAAcD,GACnB1C,MAAMmB,IAEFA,EAAIyB,QACP9B,IACA+B,EAAAA,EAAUC,QAAQ,GAADnE,OAAIwC,EAAI4B,IAAG,UAG5BR,GAAgB,GAChBrB,EAAoB,CAAEF,MAAM,EAAMG,IAAKA,IACxC,IAEAb,OAAO0C,IACPxC,QAAQyC,IAAID,GACZ9B,EAAoB,CAAEF,MAAM,EAAMG,IAAK6B,GAAI,MAoG9C,OACCE,EAAAA,EAAAA,MAACC,EAAAA,EAAK,CACLnC,KAAML,EACNyC,WAAW,EACXC,KAAK,KACLC,UAAU,qBAAoBC,SAAA,EAE9B9C,EAAAA,EAAAA,KAAC0C,EAAAA,EAAMK,OAAM,CAACF,UAAU,mBAAkBC,UACzC9C,EAAAA,EAAAA,KAAC0C,EAAAA,EAAMM,MAAK,CAACH,UAAU,cAAaC,UACnC9C,EAAAA,EAAAA,KAAA,MAAI6C,UAAU,OAAMC,SAAC,qBAGvB9C,EAAAA,EAAAA,KAAC0C,EAAAA,EAAMO,KAAI,CAACJ,UAAU,QAAOC,UAC5BL,EAAAA,EAAAA,MAAA,OAAAK,SAAA,EACC9C,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,MAAKC,UACnB9C,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,0CAAyCC,UACvD9C,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,iBAAgBC,UAC9BL,EAAAA,EAAAA,MAACS,EAAAA,EAAI,CAAAJ,SAAA,EACJL,EAAAA,EAAAA,MAAA,OAAKI,UAAU,aAAYC,SAAA,EAC1B9C,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,WAAUC,UACxBL,EAAAA,EAAAA,MAACS,EAAAA,EAAKC,MAAK,CAAAL,SAAA,EACV9C,EAAAA,EAAAA,KAACkD,EAAAA,EAAKE,MAAK,CAACP,UAAU,aAAYC,SAAC,UACnC9C,EAAAA,EAAAA,KAACqD,EAAAA,GAAM,CACNC,QAjMO,CAClB,CACCvC,MAAO,SACPC,MAAO,UAER,CACCD,MAAO,SACPC,MAAO,UAER,CACCD,MAAO,SACPC,MAAO,WAuLGD,MAAOI,EACPoC,SAAW1C,GACVO,EAAsBP,GAEvB2C,QAAQC,EAAAA,EAAAA,IACPC,EAAAA,GACAC,EAAAA,YAKJ3D,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,WAAUC,UACxBL,EAAAA,EAAAA,MAACS,EAAAA,EAAKC,MAAK,CAAAL,SAAA,EACV9C,EAAAA,EAAAA,KAACkD,EAAAA,EAAKE,MAAK,CAACP,UAAU,aAAYC,SAAC,YACnC9C,EAAAA,EAAAA,KAACkD,EAAAA,EAAKU,QAAO,CACZC,KAAK,OACLC,YAAY,SACZ/C,MAAOQ,EACPgC,SAAWhB,IAAC,IAAAwB,EAAA,OACXvC,EAAwB,QAAfuC,EAACxB,EAAEyB,OAAOjD,aAAK,IAAAgD,OAAA,EAAdA,EAAgBE,cAAc,EAEzCC,UAAQ,UAIoB,WAA7B/C,EAAmBJ,OACS,WAA7BI,EAAmBJ,SACnBf,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,WAAUC,UACxBL,EAAAA,EAAAA,MAACS,EAAAA,EAAKC,MAAK,CAAAL,SAAA,EACV9C,EAAAA,EAAAA,KAACkD,EAAAA,EAAKE,MAAK,CAACP,UAAU,aAAYC,SAAC,UACnC9C,EAAAA,EAAAA,KAACmE,IAAU,CACVC,SAAU3C,EACV8B,SA9Dac,IACzB3C,EAAgB2C,EAAK,EA8DTxB,UAAU,eACVyB,WAAW,qBAKftE,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,WAAUC,UACxBL,EAAAA,EAAAA,MAACS,EAAAA,EAAKC,MAAK,CAAAL,SAAA,EACV9C,EAAAA,EAAAA,KAACkD,EAAAA,EAAKE,MAAK,CAACP,UAAU,aAAYC,SACH,WAA7B3B,EAAmBJ,MACjB,MACA,SAEJf,EAAAA,EAAAA,KAACkD,EAAAA,EAAKU,QAAO,CACZC,KAAK,SACLC,YAAY,MACZ/C,MAAOJ,EACP4C,SAAWhB,GAAM3B,EAAO2B,EAAEyB,OAAOjD,OACjCmD,UAAQ,SAImB,WAA7B/C,EAAmBJ,QACnB0B,EAAAA,EAAAA,MAAA8B,EAAAA,SAAA,CAAAzB,SAAA,EACC9C,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,WAAUC,UACxBL,EAAAA,EAAAA,MAACS,EAAAA,EAAKC,MAAK,CAAAL,SAAA,EACV9C,EAAAA,EAAAA,KAACkD,EAAAA,EAAKE,MAAK,CAACP,UAAU,aAAYC,SAAC,kBAGnC9C,EAAAA,EAAAA,KAACkD,EAAAA,EAAKU,QAAO,CACZC,KAAK,SACLC,YAAY,eACZ/C,MAAOY,EACP4B,SAAWhB,GAAMX,EAAcW,EAAEyB,OAAOjD,OACxCmD,UAAQ,UAIXlE,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,WAAUC,UACxBL,EAAAA,EAAAA,MAACS,EAAAA,EAAKC,MAAK,CAAAL,SAAA,EACV9C,EAAAA,EAAAA,KAACkD,EAAAA,EAAKE,MAAK,CAACP,UAAU,aAAYC,SAAC,iBAGnC9C,EAAAA,EAAAA,KAACqD,EAAAA,GAAM,CACNC,QAxOK,CAClB,CACCvC,MAAO,KACPC,MAAO,MAER,CACCD,MAAO,KACPC,MAAO,OAkOKD,MAAOE,EACPsC,SAAW1C,GACVK,EAAsBL,GAEvB2C,QAAQC,EAAAA,EAAAA,IACPC,EAAAA,GACAC,EAAAA,eAQN3D,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,WAAUC,UACxBL,EAAAA,EAAAA,MAACS,EAAAA,EAAKC,MAAK,CAAAL,SAAA,EACV9C,EAAAA,EAAAA,KAACkD,EAAAA,EAAKE,MAAK,CAACP,UAAU,aAAYC,SAAC,YACnC9C,EAAAA,EAAAA,KAACqD,EAAAA,GAAM,CACNC,QArQI,CACf,CACCvC,MAAO,MACPC,MAAO,OAER,CACCD,MAAO,OACPC,MAAO,SA+PGD,MAAOF,EACP0C,SAAW1C,GACVC,EAAkBD,GAEnBiD,YAAY,wBACZN,QAAQC,EAAAA,EAAAA,IACPC,EAAAA,GACAC,EAAAA,YAKJ3D,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,WAAUC,UACxBL,EAAAA,EAAAA,MAACS,EAAAA,EAAKC,MAAK,CAAAL,SAAA,EACV9C,EAAAA,EAAAA,KAACkD,EAAAA,EAAKE,MAAK,CAACP,UAAU,aAAYC,SAAC,aACnC9C,EAAAA,EAAAA,KAACqD,EAAAA,GAAM,CACNC,QArSW,CACtB,CACCvC,MAAO,MACPC,MAAO,OAER,CACCD,MAAO,OACPC,MAAO,QAER,CACCD,MAAO,MACPC,MAAO,QA2RGD,MAAOM,EACPkC,SAAW1C,GACVS,EAAyBT,GAE1B2C,QAAQC,EAAAA,EAAAA,IACPC,EAAAA,GACAC,EAAAA,eAML3D,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,aAAYC,UAC1BL,EAAAA,EAAAA,MAAA,OAAKI,UAAU,gBAAeC,SAAA,EAC7B9C,EAAAA,EAAAA,KAACwE,EAAAA,EAAM,CACNX,KAAK,SACLhB,UAAU,6BACV4B,QAtLeC,KACzB5D,EAAkB,CACjBC,MAAO,MACPC,MAAO,QAERE,EAAsB,CACrBH,MAAO,KACPC,MAAO,OAERI,EAAsB,CACrBL,MAAO,SACPC,MAAO,WAERM,EAAyB,CACxBP,MAAO,MACPC,MAAO,QAERJ,EAAO,IACPgB,EAAc,MACdF,EAAgB,IAAIzE,KAAO,EAmKU6F,SAC3B,WAGD9C,EAAAA,EAAAA,KAACwE,EAAAA,EAAM,CACN3B,UAAU,kBACV4B,QAjQelC,IACzBA,EAAEoC,iBAG4B,WAA7BxD,EAAmBJ,OACnBJ,GACc,OAAdE,QAAc,IAAdA,GAAAA,EAAgBE,OAChBQ,GAI6B,WAA7BJ,EAAmBJ,OACnBJ,GACc,OAAdE,QAAc,IAAdA,GAAAA,EAAgBE,OAChBQ,GAI6B,WAA7BJ,EAAmBJ,OACnBJ,GACc,OAAdE,QAAc,IAAdA,GAAAA,EAAgBE,OAChBQ,GACAI,GACkB,OAAlBV,QAAkB,IAAlBA,GAAAA,EAAoBF,MAdpBe,GAAgB,GAkBhBM,EAAAA,EAAUtC,MAAM,yBACjB,EAqOoCgD,SAC1B,6BAUNjB,IACAY,EAAAA,EAAAA,MAACC,EAAAA,EAAK,CACLnC,KAAMsB,EACNc,WAAW,EACXC,KAAK,KACLC,UAAU,OAAMC,SAAA,EAEhB9C,EAAAA,EAAAA,KAAC0C,EAAAA,EAAMK,OAAM,CAACF,UAAU,qCAAoCC,UAC3D9C,EAAAA,EAAAA,KAAC0C,EAAAA,EAAMM,MAAK,CAAAF,UACX9C,EAAAA,EAAAA,KAAA,OAAA8C,UACC9C,EAAAA,EAAAA,KAAA,MAAI6C,UAAU,OAAMC,SAAC,mBAIxB9C,EAAAA,EAAAA,KAAC0C,EAAAA,EAAMO,KAAI,CAACJ,UAAU,OAAO+B,MAAO,CAAEC,gBAAiB,SAAU/B,UAChE9C,EAAAA,EAAAA,KAAA,SACC4E,MAAO,CACNE,MAAO,MACPC,OAAQ,SACRC,eAAgB,YACflC,UAEFL,EAAAA,EAAAA,MAAA,SAAAK,SAAA,EACCL,EAAAA,EAAAA,MAAA,MAAAK,SAAA,EACC9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,SAAC,gBAC/B9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,SAAC,QAC/B9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,UAC7B9C,EAAAA,EAAAA,KAAA,KAAA8C,SAAI3B,EAAmBJ,cAGzB0B,EAAAA,EAAAA,MAAA,MAAAK,SAAA,EACC9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,SAAC,YAC/B9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,SAAC,QAC/B9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,UAC7B9C,EAAAA,EAAAA,KAAA,KAAA8C,SAAIvB,SAGwB,WAA7BJ,EAAmBJ,QACnBf,EAAAA,EAAAA,KAAA,MAAA8C,SACErB,IACAgB,EAAAA,EAAAA,MAAA8B,EAAAA,SAAA,CAAAzB,SAAA,EACC9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,SAAC,UAC/B9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,SAAC,QAC/B9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,UAC5BoC,EAAAA,EAAAA,SAAOzD,EAAc,sBAM3BgB,EAAAA,EAAAA,MAAA,MAAAK,SAAA,EACC9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,SACC,WAA7B3B,EAAmBJ,MAAqB,MAAQ,SAElDf,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,SAAC,QAC/B9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,UAC7B9C,EAAAA,EAAAA,KAAA,KAAA8C,SAAInC,SAGwB,WAA7BQ,EAAmBJ,QACnB0B,EAAAA,EAAAA,MAAA8B,EAAAA,SAAA,CAAAzB,SAAA,EACCL,EAAAA,EAAAA,MAAA,MAAAK,SAAA,EACC9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,SAAC,kBAC/B9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,SAAC,QAC/B9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,UAC7B9C,EAAAA,EAAAA,KAAA,KAAA8C,SAAInB,UAGNc,EAAAA,EAAAA,MAAA,MAAAK,SAAA,EACC9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,SAAC,iBAC/B9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,SAAC,QAC/B9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,UAC7B9C,EAAAA,EAAAA,KAAA,KAAA8C,SAAI7B,EAAmBF,iBAK3B0B,EAAAA,EAAAA,MAAA,MAAAK,SAAA,EACC9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,SAAC,YAC/B9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,SAAC,QAC/B9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,UAC7B9C,EAAAA,EAAAA,KAAA,KAAA8C,SAAIjC,EAAeE,cAGrB0B,EAAAA,EAAAA,MAAA,MAAAK,SAAA,EACC9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,SAAC,aAC/B9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,SAAC,QAC/B9C,EAAAA,EAAAA,KAAA,MAAI4E,MAAO,CAAEK,MAAO,SAAUnC,UAC7B9C,EAAAA,EAAAA,KAAA,KAAA8C,SAAIzB,EAAsBN,qBAO/B0B,EAAAA,EAAAA,MAACC,EAAAA,EAAMyC,OAAM,CAACtC,UAAU,qCAAoCC,SAAA,EAC3D9C,EAAAA,EAAAA,KAAA,OAAA8C,UACC9C,EAAAA,EAAAA,KAACwE,EAAAA,EAAM,CAACY,QAAQ,kBAAkBX,QA9UpBlC,IAErB,IAAI8C,EAEHA,EADgC,WAA7BlE,EAAmBJ,MACb,CACRuE,WAAY,KACZC,YAAahE,EACbiE,YAAaC,OAAOrH,EAAMsH,IAC1BC,SAAUF,OAAO9E,GACjBiF,YAAa/E,EAAeE,MAC5B8E,QAASxE,EAAsBN,OAEO,WAA7BI,EAAmBJ,MACpB,CACRuE,WAAY,SACZC,YAAahE,EACbuE,aAAaZ,EAAAA,EAAAA,SAAOzD,EAAc,cAClCsE,aAAcN,OAAO9D,GACrBqE,YAAa/E,EAAmBF,MAChCyE,YAAaC,OAAOrH,EAAMsH,IAC1BC,SAAUF,OAAO9E,GACjBiF,YAAa/E,EAAeE,MAC5B8E,QAASxE,EAAsBN,OAGvB,CACRuE,WAAY,SACZC,YAAahE,EACbuE,aAAaZ,EAAAA,EAAAA,SAAOzD,EAAc,cAClC+D,YAAaC,OAAOrH,EAAMsH,IAC1BC,SAAUF,OAAO9E,GACjBiF,YAAa/E,EAAeE,MAC5B8E,QAASxE,EAAsBN,OAGjCsE,GAAUtD,EAAkBkE,OAAOZ,EAAO,EA2SqBvC,SAAC,eAI1D9C,EAAAA,EAAAA,KAAA,OAAA8C,UACC9C,EAAAA,EAAAA,KAACwE,EAAAA,EAAM,CACNY,QAAQ,iBACRX,QAASA,IAAM3C,GAAgB,GAAOgB,SACtC,mBAQJtC,EAAiBD,OACjBP,EAAAA,EAAAA,KAACkG,EAAAA,EAAa,CACbxF,IAAKF,EAAiBE,IACtBD,oBAAqBA,EACrB6B,IAAK9B,EAAiBE,IAAI4B,YAK9BtC,EAAAA,EAAAA,KAAC0C,EAAAA,EAAMyC,OAAM,CAACtC,UAAU,qCAAoCC,UAC3D9C,EAAAA,EAAAA,KAAA,OAAA8C,UACC9C,EAAAA,EAAAA,KAACwE,EAAAA,EAAM,CAACY,QAAQ,iBAAiBX,QAASpE,EAAWyC,SAAC,iBAO3D,C,2QCzfO,MAAMqD,EAAmB/H,IAC/B,MAAM,QAAEgI,IAAYC,EAAAA,EAAAA,OACbC,EAAoBC,IAAyBnG,EAAAA,EAAAA,UAAS,IACvDoG,GAAaC,EAAAA,EAAAA,UAAQ,IAAM,IAAIC,KAAO,KACrCC,EAAiBC,IAAsBxG,EAAAA,EAAAA,UAAS,CACtD+B,QAAQ,EACR0E,IAAK,KACLC,IAAK,QAECC,EAAiBC,IAAsB5G,EAAAA,EAAAA,UAAS,CACtD+B,QAAQ,EACR0E,IAAK,KACLC,IAAK,QAGCtG,EAAkBC,IAAuBL,EAAAA,EAAAA,UAAS,CACxDG,MAAM,EACNG,IAAK,CAAC,IAmBP,SAASuG,EAA2BH,EAAKD,GACxC,MAAO,CACNK,MAAOJ,EAAM,EACbK,OAAQN,EAAIM,OACZvB,aAAawB,EAAAA,EAAAA,IAAmBP,EAAIjB,aACpCC,QAASgB,EAAIhB,QACbwB,UAAWR,EAAIQ,UACf1B,SAAUkB,EAAIlB,SACd2B,MAAOT,EAAIS,MACXC,MAAMzK,EAAAA,EAAAA,GAAyB+J,EAAIU,MACnCC,QAASX,EAAIW,QACbrF,OAAQ0E,EAAIY,SACXhF,EAAAA,EAAAA,MAAA,OAAKI,UAAU,2BAA0BC,SAAA,EACxC9C,EAAAA,EAAAA,KAACwE,EAAAA,EAAM,CACNY,QAAQ,UACRvC,UAAU,sBACV6C,GAAImB,EAAInB,GACRjB,QAASA,IACRuC,EAAmB,CAAE7E,QAAQ,EAAM0E,IAAKA,EAAKC,IAAKA,IAClDhE,UAED9C,EAAAA,EAAAA,KAAA,KACC6C,UAAU,mEAIZ7C,EAAAA,EAAAA,KAACwE,EAAAA,EAAM,CACNY,QAAQ,SACRvC,UAAU,2BACV6C,GAAImB,EAAInB,GACRjB,QAASA,IACRmC,EAAmB,CAAEzE,QAAQ,EAAM0E,IAAKA,EAAKC,IAAKA,IAClDhE,UAED9C,EAAAA,EAAAA,KAAA,KACC6C,UAAU,+DAOb6E,EAAAA,EAAAA,IAAkB,OAAHb,QAAG,IAAHA,OAAG,EAAHA,EAAK1E,OAAW,OAAH0E,QAAG,IAAHA,OAAG,EAAHA,EAAKc,OAAQd,GAG1CA,IAAKA,EAEP,EA/DAtI,EAAAA,EAAAA,YAAU,KAAO,IAADqJ,GAQhB,SAAsBC,GACrB,IAAIC,EAAe,GACnBD,EAAUE,KAAI,CAAClB,EAAKK,IACZY,EAAaE,KAAKf,EAA2BC,EAAOL,MAE5DN,EAAsBuB,EACvB,CAbCG,CAAuB,QAAXL,EAACxJ,EAAMsB,YAAI,IAAAkI,OAAA,EAAVA,EAAYM,QAEzB9J,EAAMsB,KAAKwI,OAAOH,KAAI,CAAClB,EAAKK,IACpBV,EAAW2B,IAAItB,EAAIW,QAASX,IAClC,GACA,CAACzI,EAAMsB,OA2DV,MAcM0I,EAAU,CACf,CACCC,KAAM,KACNC,SAAWC,GAAQA,EAAIrB,MACvBsB,UAAU,EACVC,SAAU,OACVC,SAAU,QAEX,CACCL,KAAM,SACNC,SAAWC,GAAQA,EAAIpB,OACvBwB,MAAM,EACNH,UAAU,EACVG,MAAM,GAEP,CACCN,KAAM,QACNC,SAAWC,GAAQA,EAAI3C,YACvB4C,UAAU,EACVI,aAjC6BC,CAACC,EAAGC,IAEX,MAAtBD,EAAEjC,IAAIjB,aACgB,MAAtBkD,EAAEjC,IAAIjB,aACgB,QAAtBkD,EAAEjC,IAAIjB,aACgB,QAAtBkD,EAAEjC,IAAIjB,aACgB,QAAtBkD,EAAEjC,IAAIjB,YAEC,GAEC,EAwBR6C,SAAU,OACVC,SAAU,QAEX,CACCL,KAAM,UACNC,SAAWC,GAAQA,EAAI1C,QACvB2C,UAAU,EACVC,SAAU,QACVC,SAAU,SAEX,CACCL,KAAM,OACNC,SAAWC,GAAQA,EAAIlB,UACvBmB,UAAU,EACVC,SAAU,QACVC,SAAU,SAEX,CACCL,KAAM,MACNC,SAAWC,GAAQA,EAAI5C,SACvB6C,UAAU,EACVC,SAAU,OACVC,SAAU,QAEX,CACCL,KAAM,QACNC,SAAWC,GAAQA,EAAIjB,MACvBkB,UAAU,EACVC,SAAU,QACVC,SAAU,SAEX,CACCL,KAAM,OACNC,SAAWC,GAAQA,EAAIhB,KACvBiB,UAAU,EACVC,SAAU,QACVC,SAAU,QACVC,MAAM,GAEP,CACCN,KAAM,WACNC,SAAWC,GAAQA,EAAIf,QACvBmB,MAAM,EACNH,UAAU,GAEX,CAAEH,KAAM,SAAUC,SAAWC,GAAQA,EAAIpG,OAAQqG,UAAU,GAC3D,CAAEH,KAAM,MAAOC,SAAWC,GAAQA,EAAI1B,IAAKmC,MAAM,IAuD5CC,GAAqBjH,EAAAA,EAAAA,aAAY,kBAAkB,KACxDkH,EAAAA,EAAAA,IAAe9C,EAASO,EAAgBE,IAAIW,SAC1CjI,MAAMmB,IAiCT,IAA0BwG,EAhCnBxG,EAAIyB,QACPqE,EAAW2C,OAAOxC,EAAgBE,IAAInB,IACtCjF,EAAoB,CAAEF,MAAM,EAAMG,IAAKA,IA8BjBwG,EA7BLP,EAAgBG,IA8BpCP,GAAuB6C,IACtB,MAAMC,EAAc,IAAID,GAExB,OADAC,EAAYC,OAAOpC,EAAO,GACnBmC,CAAW,KA/BhB5I,EAAoB,CAAEF,MAAM,EAAMG,IAAKA,IAExCkG,EAAmB,CAAEzE,QAAQ,EAAO0E,IAAK,KAAMC,IAAK,MAAO,IAE3DjH,OAAO0C,IACP9B,EAAoB,CAAEF,MAAM,EAAMG,IAAK6B,IAChCgH,QAAQC,OAAOjH,QAKnBkH,GAAyBzH,EAAAA,EAAAA,aAAY,mBAAmB,KAC7D0H,EAAAA,EAAAA,IAAgBtD,EAASW,EAAgBF,IAAIW,SAC3CjI,MAAMmB,IACFA,EAAIyB,OACP1B,EAAoB,CAAEF,MAAM,EAAMG,IAAKA,IAIxCsG,EAAmB,CAAE7E,QAAQ,EAAO0E,IAAK,KAAMC,IAAK,MAAO,IAE3DjH,OAAO0C,IACP9B,EAAoB,CAAEF,MAAM,EAAMG,IAAK6B,IAChCgH,QAAQC,OAAOjH,QAWzB,OACCE,EAAAA,EAAAA,MAAA8B,EAAAA,SAAA,CAAAzB,SAAA,EACCL,EAAAA,EAAAA,MAAA,OAAKI,UAAU,wBAAuBC,SAAA,EACrC9C,EAAAA,EAAAA,KAACkD,EAAAA,EAAKC,MAAK,CAAAL,UACV9C,EAAAA,EAAAA,KAACkD,EAAAA,EAAKU,QAAO,CACZC,KAAK,OACLC,YAAY,SACZjB,UAAU,uBACVU,SAtGiBhB,IACrB,IAAIoH,EAAapH,EAAEyB,OAAOjD,MACtB6I,EAAiC,OAAVD,QAAU,IAAVA,OAAU,EAAVA,EAAYE,cACnCC,EAAe,GACnB,IAAK,IAAI/I,KAASyF,EAAWnB,SAAU,CAAC,IAAD0E,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACtC,MAAMC,GAAU7N,EAAAA,EAAAA,GAA8B,OAALiE,QAAK,IAALA,OAAK,EAALA,EAAOwG,MAC1CqD,GAAYlD,EAAAA,EAAAA,IAAoB,OAAL3G,QAAK,IAALA,OAAK,EAALA,EAAOoB,OAAa,OAALpB,QAAK,IAALA,OAAK,EAALA,EAAO4G,OAAQ5G,GACzD8J,EAA6B,OAATD,QAAS,IAATA,GAAgB,QAAPb,EAATa,EAAWxM,aAAK,IAAA2L,OAAP,EAATA,EAAkBjH,UAE/B,QAAZkH,EAAAjJ,EAAMoG,cAAM,IAAA6C,GAAZA,EACGH,cACDiB,SAASlB,EAAqBC,gBACf,QAD6BI,EAC9ClJ,EAAM6E,mBAAW,IAAAqE,GAAjBA,EACGJ,cACDiB,SAASlB,EAAqBC,gBACnB,QADiCK,EAC9CnJ,EAAM8E,eAAO,IAAAqE,GAAbA,EACGL,cACDiB,SAASlB,EAAqBC,gBACjB,QAD+BM,EAC9CpJ,EAAMsG,iBAAS,IAAA8C,GAAfA,EACGN,cACDiB,SAASlB,EAAqBC,gBAClB,QADgCO,EAC9CrJ,EAAM4E,gBAAQ,IAAAyE,GACD,QADCC,EAAdD,EACG5M,kBAAU,IAAA6M,GADbA,EAEGR,cACDiB,SAASlB,EAAqBC,gBACrB,QADmCS,EAC9CvJ,EAAMuG,aAAK,IAAAgD,GACE,QADFC,EAAXD,EACG9M,kBAAU,IAAA+M,GADbA,EAEGV,cACDiB,SAASlB,EAAqBC,gBACnB,QADiCW,EAC9CzJ,EAAMyG,eAAO,IAAAgD,GACA,QADAC,EAAbD,EACGhN,kBAAU,IAAAiN,GADbA,EAEGZ,cACDiB,SAASlB,EAAqBC,gBACtB,QADoCa,EAC9C3J,EAAMwG,YAAI,IAAAmD,GAAVA,EACGb,cACDiB,SAASlB,EAAqBC,gBACzB,OAAPc,QAAO,IAAPA,GAAAA,EAASd,cAAciB,SAASlB,EAAqBC,gBACpC,OAAjBgB,QAAiB,IAAjBA,GAAAA,EACGhB,cACDiB,SAASlB,EAAqBC,iBAEhCC,EAAa9B,KAAKjH,EAEpB,CACAwF,EACCuD,EAAa/B,KAAI,CAAClB,EAAKK,IAAUD,EAA2BC,EAAOL,KACnE,EAyDG3C,UAAQ,OAGVlE,EAAAA,EAAAA,KAAC+K,EAAAA,GAAS,CACT3C,QAASA,EACT1I,KAAM4G,EACN0E,YAAY,EACZC,kBAAmB,GACnBC,kBAAgB,EAChBC,MAAM,iBAGPxE,EAAgBxE,SAChBM,EAAAA,EAAAA,MAACC,EAAAA,EAAK,CAACnC,MAAM,EAAMoC,WAAW,EAAMC,KAAK,KAAKC,UAAU,OAAMC,SAAA,EAC7D9C,EAAAA,EAAAA,KAAC0C,EAAAA,EAAMK,OAAM,CAACF,UAAU,mBAAkBC,UACzC9C,EAAAA,EAAAA,KAAC0C,EAAAA,EAAMM,MAAK,CAACH,UAAU,cAAaC,UACnC9C,EAAAA,EAAAA,KAAA,MAAI6C,UAAU,OAAMC,SAAC,sBAGvB9C,EAAAA,EAAAA,KAAC0C,EAAAA,EAAMO,KAAI,CAACJ,UAAU,OAAMC,SAAC,gCAC7BL,EAAAA,EAAAA,MAACC,EAAAA,EAAMyC,OAAM,CAACtC,UAAU,qCAAoCC,SAAA,EAC3D9C,EAAAA,EAAAA,KAAA,OAAA8C,UACC9C,EAAAA,EAAAA,KAACwE,EAAAA,EAAM,CACNY,QAAQ,iBACRX,QAASA,IAAMmC,EAAmB,CAAEzE,QAAQ,EAAOuD,GAAI,KAAM5C,SAC7D,UAIF9C,EAAAA,EAAAA,KAAA,OAAA8C,UACC9C,EAAAA,EAAAA,KAACwE,EAAAA,EAAM,CACNY,QAAQ,kBACRvC,UAAU,YACV4B,QAASA,IAAMwE,EAAmBhD,SAASnD,SAC3C,gBAQJiE,EAAgB5E,SAChBM,EAAAA,EAAAA,MAACC,EAAAA,EAAK,CAACnC,MAAM,EAAMoC,WAAW,EAAMC,KAAK,KAAKC,UAAU,OAAMC,SAAA,EAC7D9C,EAAAA,EAAAA,KAAC0C,EAAAA,EAAMK,OAAM,CAACF,UAAU,mBAAkBC,UACzC9C,EAAAA,EAAAA,KAAC0C,EAAAA,EAAMM,MAAK,CAACH,UAAU,cAAaC,UACnC9C,EAAAA,EAAAA,KAAA,MAAI6C,UAAU,OAAMC,SAAC,2BAGvB9C,EAAAA,EAAAA,KAAC0C,EAAAA,EAAMO,KAAI,CAACJ,UAAU,OAAMC,SAAC,qCAG7BL,EAAAA,EAAAA,MAACC,EAAAA,EAAMyC,OAAM,CAACtC,UAAU,qCAAoCC,SAAA,EAC3D9C,EAAAA,EAAAA,KAAA,OAAA8C,UACC9C,EAAAA,EAAAA,KAACwE,EAAAA,EAAM,CACNY,QAAQ,iBACRX,QAASA,IAAMuC,EAAmB,CAAE7E,QAAQ,EAAOuD,GAAI,KAAM5C,SAC7D,UAIF9C,EAAAA,EAAAA,KAAA,OAAA8C,UACC9C,EAAAA,EAAAA,KAACwE,EAAAA,EAAM,CACNY,QAAQ,kBACRvC,UAAU,YACV4B,QAASA,IAAMgF,EAAuBxD,SAASnD,SAC/C,gBAOJtC,EAAiBD,OACjBP,EAAAA,EAAAA,KAACkG,EAAAA,EAAa,CACbxF,IAAKF,EAAiBE,IACtBD,oBAAqBA,EACrB6B,IAAK9B,EAAiBE,IAAI4B,QAG1B,E,2NCrWE,MAAM8I,EAAsBhN,IAClC,MAAOoC,EAAkBC,IAAuBL,EAAAA,EAAAA,UAAS,CACxDG,MAAM,EACNG,IAAK,CAAC,KAEA2K,EAAoBC,IAAyBlL,EAAAA,EAAAA,UAAS,CAC5D+B,QAAQ,EACRkD,OAAQ,CAAC,KAEHiB,EAAoBC,IAAyBnG,EAAAA,EAAAA,UAAS,IACvDoG,GAAaC,EAAAA,EAAAA,UAAQ,IAAM,IAAIC,KAAO,IAuB5C,MAAM6E,GAAoBvJ,EAAAA,EAAAA,aAAY,aAAcqD,IACnDmG,EAAAA,EAAAA,IAAUnG,EAAOe,QAASf,EAAO8B,QAC/B5H,MAAMmB,IACFA,EAAIyB,SACP1B,EAAoB,CAAEF,MAAM,EAAMG,IAAKA,IACvCtC,EAAMqN,mBAEPH,EAAsB,CAAEnJ,QAAQ,EAAOkD,OAAQ,CAAC,GAAI,IAEpDxF,OAAO0C,IACPxC,QAAQyC,IAAI,WAAYD,GACxB9B,EAAoB,CAAEF,MAAM,EAAMG,IAAK6B,GAAI,MAyB9C,SAAS0E,EAA2BH,EAAKD,GACxC,MAAO,CACNK,MAAOJ,EAAM,EACb4E,cAAe7E,EAAI6E,cACnBC,YAAa9E,EAAI8E,YACjBhG,SAAUkB,EAAIlB,SACdiG,KAAKC,EAAAA,EAAAA,IAAmBhF,EAAI+E,KAC5BE,IAAKjF,EAAIiF,IACTC,SAAUlF,EAAIkF,SACdC,QAAQ5E,EAAAA,EAAAA,IAAmBP,EAAImF,QAC/BC,WAnEuBtL,EAoEtBkG,EAAIlB,SApEuBuG,EAqE3BrF,EAAI6E,cArEsCtF,EAsE1ChI,EAAMgI,SApEP3D,EAAAA,EAAAA,MAAA,UACCI,UACS,IAARlC,EAAY,yBAA2B,8BAExCwL,SAAkB,IAARxL,EACV8D,QAASA,IACR6G,EAAsB,CACrBnJ,QAAQ,EACRkD,OAAQ,CAAEe,UAASe,OAAQ+E,EAAeE,QAAQ,KAEnDtJ,SAAA,CAEA,IAAI,iBA0DN+D,IAAKA,GAxEP,IAAyBlG,EAAKuL,EAAe9F,CA0E7C,EAlCA7H,EAAAA,EAAAA,YAAU,KAAO,IAADqJ,GAShB,SAAsBC,GACrB,IAAIC,EAAe,GACnBD,EAAUE,KAAI,CAAClB,EAAKK,IACZY,EAAaE,KAAKf,EAA2BC,EAAOL,MAE5DN,EAAsBuB,EACvB,CAbCG,CAAuB,QAAXL,EAACxJ,EAAMsB,YAAI,IAAAkI,OAAA,EAAVA,EAAYyE,WAEzBjO,EAAMsB,KAAK2M,UAAUtE,KAAI,CAAClB,EAAKK,IACvBV,EAAW2B,IAAItB,EAAI6E,cAAe7E,IACxC,GACA,CAACzI,EAAMsB,OA6BV,MAkBM0I,EAAU,CACf,CACCC,KAAM,KACNC,SAAWC,GAAQA,EAAIrB,MACvBsB,UAAU,EACVC,SAAU,OACVC,SAAU,QAEX,CACCL,KAAM,SACNC,SAAWC,GAAQA,EAAImD,cACvB/C,MAAM,EACNH,UAAU,EACVG,MAAM,GAEP,CACCN,KAAM,OACNC,SAAWC,GAAQA,EAAIoD,YACvBnD,UAAU,EACVG,MAAM,GAEP,CACCN,KAAM,MACNC,SAAWC,GAAQA,EAAI5C,SACvB6C,UAAU,EACVC,SAAU,QACVC,SAAU,SAEX,CACCL,KAAM,MACNC,SAAWC,GAAQA,EAAIqD,IACvBpD,UAAU,EACVI,aAlDsB0D,CAACxD,EAAGC,IACpBD,EAAEjC,IAAI+E,IAAM7C,EAAElC,IAAI+E,KAAO,EAAI,GAmDpC,CAAEvD,KAAM,MAAOC,SAAWC,GAAQA,EAAIuD,IAAKtD,UAAU,GACrD,CAAEH,KAAM,YAAaC,SAAWC,GAAQA,EAAIwD,SAAUvD,UAAU,GAChE,CACCH,KAAM,QACNC,SAAWC,GAAQA,EAAIyD,OACvBxD,UAAU,EACVI,aAtD6BC,CAACC,EAAGC,IAEhB,MAAjBD,EAAEjC,IAAImF,QACW,MAAjBlD,EAAEjC,IAAImF,QACW,QAAjBlD,EAAEjC,IAAImF,QACW,QAAjBlD,EAAEjC,IAAImF,QACW,QAAjBlD,EAAEjC,IAAImF,OAEC,GAEC,GA8CT,CAAE3D,KAAM,aAAcC,SAAWC,GAAQA,EAAI0D,WAC7C,CAAE5D,KAAM,MAAOC,SAAWC,GAAQA,EAAI1B,IAAKmC,MAAM,IAyClD,OACChJ,EAAAA,EAAAA,KAAAuE,EAAAA,SAAA,CAAAzB,UACCL,EAAAA,EAAAA,MAAA,OAAKI,UAAU,mBAAkBC,SAAA,EAChC9C,EAAAA,EAAAA,KAACkD,EAAAA,EAAKC,MAAK,CAAAL,UACV9C,EAAAA,EAAAA,KAACkD,EAAAA,EAAKU,QAAO,CACZC,KAAK,OACLC,YAAY,SACZjB,UAAU,uBACVU,SA9CiBhB,IACrB,IAAIoH,EAAapH,EAAEyB,OAAOjD,MACtB6I,EAAiC,OAAVD,QAAU,IAAVA,OAAU,EAAVA,EAAYE,cACnCC,EAAe,GACnB,IAAK,IAAI/I,KAASyF,EAAWnB,SAAU,CAAC,IAADkH,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GAErC7L,EAAM2K,cACJ7B,cACAiB,SAASlB,EAAqBC,gBAChC9I,EAAM4K,YACJ9B,cACAiB,SAASlB,EAAqBC,gBAChC9I,EAAM4E,SACJnI,WACAsN,SAASlB,EAAqBC,gBACvB,QADqC0C,EAC9CxL,EAAM6K,WAAG,IAAAW,GACI,QADJC,EAATD,EACG/O,kBAAU,IAAAgP,GADbA,EAEG3C,cACDiB,SAASlB,EAAqBC,gBACvB,QADqC4C,EAC9C1L,EAAM+K,WAAG,IAAAW,GACI,QADJC,EAATD,EACGjP,kBAAU,IAAAkP,GADbA,EAEG7C,cACDiB,SAASlB,EAAqBC,gBAClB,QADgC8C,EAC9C5L,EAAMgL,gBAAQ,IAAAY,GACD,QADCC,EAAdD,EACGnP,kBAAU,IAAAoP,GADbA,EAEG/C,cACDiB,SAASlB,EAAqBC,gBAChC9I,EAAMiL,OAAOnC,cAAciB,SAASlB,EAAqBC,iBAEzDC,EAAa9B,KAAKjH,EAEpB,CACAwF,EACCuD,EAAa/B,KAAI,CAAClB,EAAKK,IAAUD,EAA2BC,EAAOL,KACnE,EAaG3C,UAAQ,OAGVlE,EAAAA,EAAAA,KAAC+K,EAAAA,GAAS,CACT3C,QAASA,EACT1I,KAAM4G,EACN0E,YAAY,EACZC,kBAAmB,GACnBC,kBAAgB,EAChBC,MAAM,cAEN3K,EAAiBD,OACjBP,EAAAA,EAAAA,KAACkG,EAAAA,EAAa,CACbxF,IAAKF,EAAiBE,IACtBD,oBAAqBA,EACrB6B,IAAK9B,EAAiBE,IAAI4B,MAG3B+I,EAAmBlJ,SACnBM,EAAAA,EAAAA,MAACC,EAAAA,EAAK,CAACnC,MAAM,EAAMoC,WAAW,EAAMC,KAAK,KAAKC,UAAU,OAAMC,SAAA,EAC7D9C,EAAAA,EAAAA,KAAC0C,EAAAA,EAAMK,OAAM,CAACF,UAAU,mBAAkBC,UACzC9C,EAAAA,EAAAA,KAAC0C,EAAAA,EAAMM,MAAK,CAACH,UAAU,cAAaC,UACnC9C,EAAAA,EAAAA,KAAA,MAAI6C,UAAU,OAAMC,SAAC,oBAGvB9C,EAAAA,EAAAA,KAAC0C,EAAAA,EAAMO,KAAI,CAACJ,UAAU,OAAMC,SAAC,iCAG7BL,EAAAA,EAAAA,MAACC,EAAAA,EAAMyC,OAAM,CAACtC,UAAU,qCAAoCC,SAAA,EAC3D9C,EAAAA,EAAAA,KAAA,OAAA8C,UACC9C,EAAAA,EAAAA,KAACwE,EAAAA,EAAM,CACNY,QAAQ,iBACRX,QAASA,IACR6G,EAAsB,CAAEnJ,QAAQ,EAAOkD,OAAQ,CAAC,IAChDvC,SACD,UAIF9C,EAAAA,EAAAA,KAAA,OAAA8C,UACC9C,EAAAA,EAAAA,KAACwE,EAAAA,EAAM,CACNY,QAAQ,kBACRvC,UAAU,YACV4B,QAASA,KACRoI,OAnMsBxH,EAmMAgG,EAAmBhG,YAlMjDkG,EAAkBtF,OAAOZ,GAD1B,IAA+BA,CAoMtB,EAAAvC,SACD,oBAQJ,E,sHC/PE,MAAMgK,EAAoB1O,IAChC,MAAOkI,EAAoBC,IAAyBnG,EAAAA,EAAAA,UAAS,IACvDoG,GAAaC,EAAAA,EAAAA,UAAQ,IAAM,IAAIC,KAAO,IAkB5C,SAASO,EAA2BH,EAAKD,GACxC,MAAO,CACNK,MAAOJ,EAAM,EACbK,OAAQN,EAAIM,OACZK,QAASX,EAAIW,QACb3B,QAASgB,EAAIhB,QACbD,aAAawB,EAAAA,EAAAA,IAAmBP,EAAIjB,aACpC0B,MAAOT,EAAIS,MACXC,MAAMzK,EAAAA,EAAAA,GAAyB+J,EAAIU,MACnCV,IAAKA,EAEP,EA3BAtI,EAAAA,EAAAA,YAAU,KAAO,IAADqJ,GAQhB,SAAsBC,GACrB,IAAIC,EAAe,GACnBD,EAAUE,KAAI,CAAClB,EAAKK,IACZY,EAAaE,KAAKf,EAA2BC,EAAOL,MAE5DN,EAAsBuB,EACvB,CAbCG,CAAuB,QAAXL,EAACxJ,EAAMsB,YAAI,IAAAkI,OAAA,EAAVA,EAAYmF,QAEzB3O,EAAMsB,KAAKqN,OAAOhF,KAAI,CAAClB,EAAKK,IACpBV,EAAW2B,IAAItB,EAAIW,QAASX,IAClC,GACA,CAACzI,EAAMsB,OAuBV,MAcM0I,EAAU,CACf,CACCC,KAAM,KACNC,SAAWC,GAAQA,EAAIrB,MACvBsB,UAAU,EACVC,SAAU,OACVC,SAAU,OACVC,MAAM,GAEP,CACCN,KAAM,SACNC,SAAWC,GAAQA,EAAIpB,OACvBwB,MAAM,EACNH,UAAU,EACVC,SAAU,QACVC,SAAU,SAEX,CACCL,KAAM,WACNC,SAAWC,GAAQA,EAAIf,QACvBgB,UAAU,EACVG,MAAM,GAEP,CACCN,KAAM,UACNC,SAAWC,GAAQA,EAAI1C,QACvB2C,UAAU,EACVG,MAAM,GAEP,CACCN,KAAM,QACNC,SAAWC,GAAQA,EAAI3C,YACvB4C,UAAU,EACVI,aA/C6BC,CAACC,EAAGC,IAEX,MAAtBD,EAAEjC,IAAIjB,aACgB,MAAtBkD,EAAEjC,IAAIjB,aACgB,QAAtBkD,EAAEjC,IAAIjB,aACgB,QAAtBkD,EAAEjC,IAAIjB,aACgB,QAAtBkD,EAAEjC,IAAIjB,YAEC,GAEC,EAsCR+C,MAAM,GAEP,CAAEN,KAAM,QAASC,SAAWC,GAAQA,EAAIjB,MAAOkB,UAAU,EAAMG,MAAM,GACrE,CAAEN,KAAM,OAAQC,SAAWC,GAAQA,EAAIhB,KAAMiB,UAAU,EAAMG,MAAM,GACnE,CAAEN,KAAM,MAAOC,SAAWC,GAAQA,EAAI1B,IAAKmC,MAAM,IAuClD,OACChJ,EAAAA,EAAAA,KAAAuE,EAAAA,SAAA,CAAAzB,UACCL,EAAAA,EAAAA,MAAA,OAAKI,UAAU,wBAAuBC,SAAA,EACrC9C,EAAAA,EAAAA,KAACkD,EAAAA,EAAKC,MAAK,CAAAL,UACV9C,EAAAA,EAAAA,KAACkD,EAAAA,EAAKU,QAAO,CACZC,KAAK,OACLC,YAAY,SACZjB,UAAU,uBACVU,SA5CiBhB,IACrB,IAAIoH,EAAapH,EAAEyB,OAAOjD,MACtB6I,EAAiC,OAAVD,QAAU,IAAVA,OAAU,EAAVA,EAAYE,cACnCC,EAAe,GACnB,IAAK,IAAI/I,KAASyF,EAAWnB,SAAU,CAAC,IAAD2E,EAAAC,EAAAO,EAAAF,EAAAC,EAAAG,EACtC,MAAMC,GAAU7N,EAAAA,EAAAA,GAA8B,OAALiE,QAAK,IAALA,OAAK,EAALA,EAAOwG,OAEnC,QAAZyC,EAAAjJ,EAAMoG,cAAM,IAAA6C,GAAZA,EACGH,cACDiB,SAASlB,EAAqBC,gBACf,QAD6BI,EAC9ClJ,EAAM6E,mBAAW,IAAAqE,GAAjBA,EACGJ,cACDiB,SAASlB,EAAqBC,gBACnB,QADiCW,EAC9CzJ,EAAMyG,eAAO,IAAAgD,GAAbA,EACGX,cACDiB,SAASlB,EAAqBC,gBAChC9I,EAAM8E,QACJrI,WACAqM,cACAiB,SAASlB,EAAqBC,gBACrB,QADmCS,EAC9CvJ,EAAMuG,aAAK,IAAAgD,GACE,QADFC,EAAXD,EACG9M,kBAAU,IAAA+M,GADbA,EAEGV,cACDiB,SAASlB,EAAqBC,gBACzB,OAAPc,QAAO,IAAPA,GAAAA,EAASd,cAAciB,SAASlB,EAAqBC,gBAC3C,QADyDa,EACnE3J,EAAMwG,YAAI,IAAAmD,GAAVA,EAAYb,cAAciB,SAASlB,EAAqBC,iBAExDC,EAAa9B,KAAKjH,EAEpB,CACAwF,EACCuD,EAAa/B,KAAI,CAAClB,EAAKK,IAAUD,EAA2BC,EAAOL,KACnE,EAaG3C,UAAQ,OAGVlE,EAAAA,EAAAA,KAAC+K,EAAAA,GAAS,CACT3C,QAASA,EACT1I,KAAM4G,EACN0E,YAAY,EACZC,kBAAmB,GACnBC,kBAAgB,EAChBC,MAAM,kBAGN,C,oRC1IE,MAAM6B,UAAwBC,EAAAA,UACpCC,WAAAA,CAAY9O,GACX+O,MAAM/O,GAAO,KAWdgP,gBAAmBC,IAClBC,KAAKC,SAAS,CACbC,YAAaH,GACZ,EACD,KA8BF/M,mBAAqB,KACpBgN,KAAKC,SAAS,CAAEE,gBAAiB,CAAElN,MAAM,EAAOG,IAAK,CAAC,IAAM,EAC3D,KAEFgN,0BAA4B,KAC3BJ,KAAKC,SAAS,CACbE,gBAAiB,CAAElN,MAAM,IACxB,EAnDF+M,KAAKK,MAAQ,CACZH,YAAa,YACbI,cAAc,EACdC,WAAY,KACZC,WAAW,EACXL,gBAAiB,CAAElN,MAAM,EAAOG,IAAK,CAAC,IAEvC4M,KAAK7B,gBAAkB6B,KAAK7B,gBAAgBsC,KAAKT,KAClD,CAQA7B,eAAAA,CAAgBuC,EAAW5H,GAC1BkH,KAAKC,SAAS,CAAEO,WAAW,KAC3BG,EAAAA,EAAAA,IAAoBD,EAAW5H,GAC7B7G,MAAMmB,KACFA,EAAIyB,SAEPmL,KAAKC,SAAS,CAAEM,WAAYnN,EAAKkN,cAAc,IAExClN,KAKRb,OAAO0C,IACPxC,QAAQyC,IAAI,WAAYD,EAAE,IAE1B2L,SAAQ,KACRZ,KAAKC,SAAS,CAAEO,WAAW,GAAQ,GAEtC,CAEAK,iBAAAA,GACC,MAAMH,EAAYV,KAAKlP,MAAMgQ,MAAMnM,OAAOmE,QACpCA,EAAU3H,aAAaC,QAAQ,WACrC4O,KAAK7B,gBAAgBhG,OAAOuI,GAAYvI,OAAOW,GAEhD,CAYAiI,MAAAA,GAAU,IAADC,EAAAC,EAAAC,EACR,MAAM,WAAEX,EAAU,aAAED,EAAY,UAAEE,GAAcR,KAAKK,MAI/CvH,EAAUkH,KAAKlP,MAAMgQ,MAAMnM,OAAOmE,QACxC,OAAKwH,GAUJnL,EAAAA,EAAAA,MAAA,OAAAK,SAAA,EACC9C,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,YAAWC,UACzB9C,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,iBAAgBC,UAC9BL,EAAAA,EAAAA,MAAA,OAAKI,UAAU,iDAAgDC,SAAA,EAC9D9C,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,4BAA2BC,UACzCL,EAAAA,EAAAA,MAAA,UACCI,UAAU,sBACV4B,QAASA,IAAM7F,OAAO6P,QAAQ3L,SAAA,CAE7B,KACD9C,EAAAA,EAAAA,KAAA,KACC6C,UAAU,8BACV+B,MAAO,CAAE8J,SAAU,UACd,UAUR1O,EAAAA,EAAAA,KAAA,MAAI6C,UAAU,iCAAgCC,SAC7B,QAD6BwL,EAC5CT,EAAWnO,YAAI,IAAA4O,OAAA,EAAfA,EAAiBK,cAEnBlM,EAAAA,EAAAA,MAAA,MAAII,UAAU,iCAAgCC,SAAA,CAC7B,QAD6ByL,EAC5CV,EAAWnO,YAAI,IAAA6O,OAAA,EAAfA,EAAiBxJ,QAClB/E,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,aAAYC,SAAC,eAE7BL,EAAAA,EAAAA,MAAA,MAAII,UAAU,iCAAgCC,SAAA,EAC5C+I,EAAAA,EAAAA,IAAkC,QAAhB2C,EAACX,EAAWnO,YAAI,IAAA8O,OAAA,EAAfA,EAAiB5C,MACrC5L,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,aAAYC,SAAC,YAE7B9C,EAAAA,EAAAA,KAAA,UACC6C,UAAU,mBACV4B,QAASA,IAAM6I,KAAK7B,gBAAgBrF,GAAStD,SAE5CgL,GACA9N,EAAAA,EAAAA,KAAC4O,EAAAA,EAAO,CAACjM,UAAU,SAASC,KAAK,QAEjC5C,EAAAA,EAAAA,KAAA,KACC6C,UAAU,gCACV+B,MAAO,CAAE8J,SAAU,oBAOzB1O,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,gBAAeC,UAC7B9C,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,qCAAoCC,UAClD9C,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,OAAMC,UACpB9C,EAAAA,EAAAA,KAAA,OAAK6C,UAAU,YAAWC,UACzBL,EAAAA,EAAAA,MAACoM,EAAAA,EAAI,CACJC,iBAAiB,YACjBpJ,GAAG,mBACH7C,UAAU,OACVkM,MAAI,EACJC,SAAO,EACPC,UAAW3B,KAAKK,MAAMH,YACtB0B,SAAU5B,KAAKF,gBAAgBtK,SAAA,EAE/B9C,EAAAA,EAAAA,KAACmP,EAAAA,EAAG,CAAC9B,SAAS,YAAY+B,MAAM,YAAWtM,SACzC+K,IACA7N,EAAAA,EAAAA,KAACoL,EAAAA,EAAkB,CAClB1L,KAAMmO,EAAWnO,KACjB0G,QAASA,EACTqF,gBAAiB6B,KAAK7B,qBAIzBzL,EAAAA,EAAAA,KAACmP,EAAAA,EAAG,CAAC9B,SAAS,SAAS+B,MAAM,SAAQtM,SACnC+K,IAAc7N,EAAAA,EAAAA,KAACmG,EAAAA,EAAe,CAACzG,KAAMmO,EAAWnO,UAElDM,EAAAA,EAAAA,KAACmP,EAAAA,EAAG,CAAC9B,SAAS,SAAS+B,MAAM,SAAQtM,SACnC+K,IAAc7N,EAAAA,EAAAA,KAAC8M,EAAAA,EAAgB,CAACpN,KAAMmO,EAAWnO,oBAOvD4N,KAAKK,MAAMF,gBAAgBlN,OAC3BP,EAAAA,EAAAA,KAACC,EAAAA,EAAe,CACfK,mBAAoBgN,KAAKhN,mBACzBoF,GAAI4H,KAAKlP,MAAMgQ,MAAMnM,OAAOmE,cA9F9BpG,EAAAA,EAAAA,KAAA,MAAA8C,SAAI,8EAmGP,EAGD,SAAeuM,EAAAA,EAAAA,GAASrC,G,kICpLjB,MAAMrJ,EAAwB,CACpC2L,OAAQA,CAACC,EAAU5B,KAAK,IACpB4B,EACH1K,gBAAiB8I,EAAM6B,WAAa,OAAS,QAC7CvK,MAAO0I,EAAM6B,WAAa,QAAU,WAGzB9L,EAAsB,CAClC4L,OAAQA,CAACG,EAAM9B,KAAK,IAChBhK,EAAsB2L,OAAOG,EAAM9B,GACtC,UAAW,CACV9I,gBAAiB,YACjBI,MAAO,YAsBH,SAASmC,EAAmBxB,GAClC,IAAI8J,EAAS9J,EAAY+J,OAAO,GAO5BC,EACQ,MAAXF,GACW,MAAXA,GACW,SAAXA,GACW,SAAXA,GACW,SAAXA,EACD,MAXY,MAAXA,GACW,MAAXA,GACW,QAAXA,GACW,QAAXA,GACW,QAAXA,GAQO1P,EAAAA,EAAAA,KAAA,SAAO6C,UAAU,8BAA6BC,SAAC,QAC5C8M,GACH5P,EAAAA,EAAAA,KAAA,SAAO6C,UAAU,8BAA6BC,SAAC,SAGrD8C,IACC5F,EAAAA,EAAAA,KAAA,SAAO6C,UAAU,6BAA4BC,SAC3C8C,EAAY3B,eAKlB,CAqBO,SAAS4H,EAAmBgE,GAElC,OADAA,EAASC,WAAWD,GAAU,GAAGE,QAAQ,IAC5B,GAEXtN,EAAAA,EAAAA,MAAA,QAAMI,UAAU,cAAaC,SAAA,CAC3B,IACA+M,EAAO,KAAC7P,EAAAA,EAAAA,KAAA,KAAG6C,UAAU,0BAGH,IAAXgN,GAA2B,OAAXA,GAC1BA,EAAS,GACFpN,EAAAA,EAAAA,MAAA,QAAAK,SAAA,CAAM,IAAE+M,OAGfpN,EAAAA,EAAAA,MAAA,QAAMI,UAAU,eAAcC,SAAA,CAC5B,IACA+M,EAAO,KAAC7P,EAAAA,EAAAA,KAAA,KAAG6C,UAAU,uBAGzB,CAiBO,SAAS6E,EAAevF,EAAQwF,GACtC,MAAyB,MAArBxF,EAAOwN,OAAO,IAAmC,MAArBxN,EAAOwN,OAAO,IAE5C3P,EAAAA,EAAAA,KAAA,SACC6C,UAAU,8BACV,cAAY,UACZ,iBAAe,MACfuM,MAAOzH,EAAO7E,SACd,cAI6B,MAArBX,EAAOwN,OAAO,IAAmC,MAArBxN,EAAOwN,OAAO,IAEnD3P,EAAAA,EAAAA,KAAA,SACC6C,UAAU,6BACV,cAAY,UACZ,iBAAe,MACfuM,MAAOzH,EAAO7E,SACd,cAiBD9C,EAAAA,EAAAA,KAAA,SACC6C,UAAU,2BACV,cAAY,UACZ,iBAAe,MACfuM,MAAOzH,EAAO7E,SAEbX,GAIL,CAEO,SAAS6N,EAAmBC,GAClC,OAAI/S,MAAM+S,GACF,MAEJA,EAAS,IACLA,EAAOzS,WAEXyS,GAAU,KAAQA,EAAS,KACtBA,EAAS,KAAMF,QAAQ,GAAK,KAEjCE,GAAU,KAAUA,EAAS,KACxBA,EAAS,KAAQF,QAAQ,GAAK,KAEnCE,GAAU,KACLA,EAAS,KAAUF,QAAQ,GAAK,WADzC,CAGD,C,oECzLsJG,EAAS,WAAW,OAAOA,EAASC,OAAOC,QAAQ,SAASC,GAAG,IAAI,IAAIC,EAAE/N,EAAE,EAAEgO,EAAEC,UAAUC,OAAOlO,EAAEgO,EAAEhO,IAAI,IAAI,IAAImO,KAAKJ,EAAEE,UAAUjO,GAAG4N,OAAOQ,UAAUC,eAAeC,KAAKP,EAAEI,KAAKL,EAAEK,GAAGJ,EAAEI,IAAI,OAAOL,CAAC,GAAGS,MAAMxD,KAAKkD,UAAU,EAAsM,IAA8iGO,EAAM,CAAC1O,QAAziG,SAASgO,GAAG,OAAOW,EAAAA,cAAoB,MAAMd,EAAS,CAACe,QAAQ,sBAAsBnM,MAAM,GAAGoM,OAAO,IAAIb,GAAGW,EAAAA,cAAoB,OAAO,CAACG,EAAE,gPAAgPpC,KAAK,YAAY,EAA8qFqC,KAAvqF,SAASf,GAAG,OAAOW,EAAAA,cAAoB,MAAMd,EAAS,CAACe,QAAQ,sBAAsBnM,MAAM,GAAGoM,OAAO,IAAIb,GAAGW,EAAAA,cAAoB,OAAO,CAACG,EAAE,wuBAAwuBpC,KAAK,YAAY,EAA8yDsC,QAApyD,SAAShB,GAAG,OAAOW,EAAAA,cAAoB,MAAMd,EAAS,CAACrN,UAAU,mBAAmBwN,GAAG,EAA6tDiB,KAAttD,SAASjB,GAAG,OAAOW,EAAAA,cAAoB,MAAMd,EAAS,CAACe,QAAQ,oBAAoBnM,MAAM,GAAGoM,OAAO,IAAIb,GAAGW,EAAAA,cAAoB,OAAO,CAACG,EAAE,8/BAA8/BpC,KAAK,YAAY,EAAykBjP,MAAjkB,SAASuQ,GAAG,OAAOW,EAAAA,cAAoB,MAAMd,EAAS,CAACe,QAAQ,oBAAoBnM,MAAM,GAAGoM,OAAO,IAAIb,GAAGW,EAAAA,cAAoB,OAAO,CAACG,EAAE,2WAA2WpC,KAAK,YAAY,GAA0EwC,EAAO,CAAClP,QAAQ,UAAUiP,KAAK,UAAUF,KAAK,UAAUtR,MAAM,UAAUuR,QAAQ,WAAWG,EAAM,SAASnB,GAAG,IAAIC,EAAE/N,EAAEgO,EAAEG,EAAE5H,EAAE,WAAWuH,EAAEoB,UAAU,cAAc3G,SAAS,UAAU,SAAS,OAAO4G,EAAE,CAAC,WAAWrB,EAAE5L,QAAQ,qBAAqB,GAAG,YAAY4L,EAAExM,MAAM8N,KAAK,KAAKC,IAAI,QAAQrP,EAAE8N,EAAEwB,WAAM,IAAStP,OAAE,EAAOA,EAAEK,OAAO,OAAO,MAAM,QAAQ2N,EAAEF,EAAEwB,WAAM,IAAStB,OAAE,EAAOA,EAAE3L,QAAQ,SAAS,MAAM,QAAQ8L,EAAEL,EAAEwB,WAAM,IAASnB,OAAE,EAAOA,EAAEzL,QAAQsM,EAAOlB,EAAExM,OAAOiO,EAAEf,EAAMV,EAAExM,MAAMkO,GAAE3R,EAAAA,EAAAA,YAAWkQ,EAAE,CAAC0B,QAAQ,IAAIlJ,IAAI,GAAGwH,IAAIa,EAAEY,EAAE,GAAGE,EAAEF,EAAE,GAAGG,EAAEhC,EAAS,CAACiC,YAAY9B,EAAE+B,QAAQ,QAAG,EAAOC,UAAUhC,EAAE+B,QAAQ,QAAG,EAAOE,WAAWV,GAAGT,GAAGoB,EAAE,WAAW,IAAIjC,EAAE2B,IAAI3B,EAAE,CAAC0B,QAAQ,IAAIlJ,GAAG,QAAQwH,IAAIkC,YAAY,WAAWnC,EAAEoC,OAAOpC,EAAE3K,GAAG2K,EAAEoB,SAAS,GAAG,IAAI,GAAElT,EAAAA,EAAAA,YAAW,WAAW,IAAI+R,EAAE/N,EAAEiQ,YAAY,WAAW,IAAInC,EAAE4B,IAAI5B,EAAE,CAAC2B,QAAQ,IAAIlJ,GAAG,OAAOuH,GAAG,GAAG,IAAI,OAAO,IAAIA,EAAEqC,YAAYpC,EAAEkC,YAAY,WAAWD,GAAG,GAAG,IAAIlC,EAAEqC,YAAY,WAAWC,aAAapQ,GAAG+N,GAAGqC,aAAarC,EAAE,CAAC,GAAG,KAAI/R,EAAAA,EAAAA,YAAW,WAAW8R,EAAE9P,MAAMgS,GAAG,GAAG,CAAClC,EAAE9P,OAAO,IAAIqS,EAAE,CAACC,SAAS,EAAEpO,QAAQ4L,EAAE5L,QAAQqO,WAAW,SAASxC,GAAG,KAAKA,EAAEyC,SAAS1C,EAAE5L,QAAQ6L,EAAE,GAAG,OAAOU,EAAAA,cAAoB,MAAMd,EAAS,CAACrN,UAAU6O,EAAEsB,KAAK3C,EAAE2C,KAAK3C,EAAE2C,KAAK,SAASpO,MAAMsN,GAAG7B,EAAE5L,QAAQmO,EAAE,CAAC,GAAGvC,EAAE4C,WAAW5C,EAAE4C,aAAajC,EAAAA,cAAoBc,EAAE,MAAMd,EAAAA,cAAoB,MAAM,CAACnO,UAAUwN,EAAE+B,QAAQ,wBAAwB,iBAAiB/B,EAAE+B,SAASpB,EAAAA,cAAoB,KAAK,CAACnO,UAAU,cAAcwN,EAAE+B,SAASpB,EAAAA,cAAoB,MAAM,CAACnO,UAAU,WAAWwN,EAAE6C,OAAO,EAAE1B,EAAM2B,UAAU,CAACtP,KAAKuP,EAAAA,OAAOC,WAAWH,MAAKI,EAAAA,EAAAA,WAAU,CAACF,EAAAA,OAAOG,EAAAA,OAAOF,WAAW9S,KAAKiT,EAAAA,KAAKf,OAAOgB,EAAAA,KAAK/N,IAAG4N,EAAAA,EAAAA,WAAU,CAACF,EAAAA,OAAOM,EAAAA,SAAShB,UAAUgB,EAAAA,OAAOtB,QAAQgB,EAAAA,OAAO3B,SAAS2B,EAAAA,OAAOH,WAAWQ,EAAAA,KAAK5B,KAAI8B,EAAAA,EAAAA,OAAM,CAAC,GAAGlP,QAAQgP,EAAAA,KAAKT,KAAKI,EAAAA,QAAQ5B,EAAMoC,aAAa,CAAClO,QAAG,EAAOnF,MAAK,EAAGkS,YAAO,EAAOC,UAAU,EAAEN,aAAQ,EAAOX,SAAS,aAAawB,gBAAW,EAAOpB,IAAI,CAAC,EAAEpN,aAAQ,EAAOuO,KAAK,UAAU,IAAIa,EAAU,SAASxD,GAAG,OAAOA,EAAEyD,QAAQ,aAAa,SAASzD,GAAG,OAAOA,EAAE,GAAGpM,aAAa,GAAG,EAAE8P,EAAc,CAACC,QAAQ,GAAGC,UAAU,GAAGC,SAAS,GAAGC,WAAW,GAAGC,aAAa,GAAGC,YAAY,IAAIC,EAAe,SAASjE,GAAG,IAAIC,EAAED,EAAEkE,MAAMhS,EAAE8N,EAAEmE,SAASjE,GAAEnQ,EAAAA,EAAAA,UAAS2T,GAAerD,EAAEH,EAAE,GAAGzH,EAAEyH,EAAE,IAAGhS,EAAAA,EAAAA,YAAW,WAAW+R,GAAGxH,GAAG,SAASuH,GAAG,IAAI9N,EAAEgO,EAAEsD,EAAUvD,EAAEmB,UAAU,cAAc,OAAOvB,EAASA,EAAS,CAAC,EAAEG,KAAK9N,EAAE,CAAC,GAAGgO,GAA/hL,WAA0B,IAAI,IAAIF,EAAE,EAAEC,EAAE,EAAE/N,EAAEiO,UAAUC,OAAOH,EAAE/N,EAAE+N,IAAID,GAAGG,UAAUF,GAAGG,OAAO,IAAIF,EAAEkE,MAAMpE,GAAGK,EAAE,EAAE,IAAIJ,EAAE,EAAEA,EAAE/N,EAAE+N,IAAI,IAAI,IAAIxH,EAAE0H,UAAUF,GAAGoB,EAAE,EAAEE,EAAE9I,EAAE2H,OAAOiB,EAAEE,EAAEF,IAAIhB,IAAIH,EAAEG,GAAG5H,EAAE4I,GAAG,OAAOnB,CAAC,CAA+1KmE,CAAerE,EAAEE,GAAG,CAACD,IAAI/N,GAAG,GAAG,GAAG,CAAC+N,IAAI,IAAIoB,EAAE,SAASrB,EAAEC,GAAGxH,GAAG,SAASvG,GAAG,IAAIgO,EAAEG,EAAEmD,EAAUvD,GAAG,cAAc,OAAOJ,EAASA,EAAS,CAAC,EAAE3N,KAAKgO,EAAE,CAAC,GAAGG,GAAGnO,EAAEmO,GAAGiE,QAAQ,SAASrE,GAAG,OAAOA,EAAE5K,KAAK2K,CAAC,IAAIE,GAAG,GAAG,EAAEqB,EAAE,CAAC,OAAO,SAAS,SAAS,OAAOZ,EAAAA,cAAoBA,EAAAA,SAAe,KAAK,CAAC,MAAM,UAAUjJ,KAAK,SAASsI,GAAG,OAAOW,EAAAA,cAAoB,MAAM,CAAC4D,IAAI,OAAOvE,EAAExN,UAAU,UAAU+O,EAAE7J,KAAK,SAASuI,GAAG,IAAIC,EAAE,GAAGF,EAAEC,EAAExH,EAAE,CAAC,WAAW,WAAWuH,EAAE,iBAAiB,IAAIsB,KAAK,KAAK,OAAOX,EAAAA,cAAoB,MAAM,CAAC4D,IAAIrE,EAAE1N,UAAUiG,GAAG4H,EAAEH,GAAGxI,KAAK,SAASsI,GAAG,OAAOW,EAAAA,cAAoBQ,EAAMtB,EAAS,CAAC0E,IAAIrE,EAAE,IAAIF,EAAE3K,IAAI2K,EAAE,CAAC3K,GAAG2K,EAAE3K,GAAGwN,KAAK7C,EAAE6C,KAAKrP,KAAKwM,EAAExM,KAAKY,QAAQ4L,EAAE5L,QAAQiO,UAAUrC,EAAEqC,UAAUnS,KAAKgC,IAAI8N,EAAE3K,GAAG+M,OAAOf,IAAI,IAAI,IAAI,IAAI,EAA6W4C,EAAenB,UAAU,CAACoB,OAAMZ,EAAAA,EAAAA,OAAM,CAAC,GAAGa,SAASd,EAAAA,QAAQY,EAAeV,aAAa,CAACW,WAAM,EAAOC,cAAS,IAAzd,SAAqBnE,EAAEC,QAAG,IAASA,IAAIA,EAAE,CAAC,GAAG,IAAI/N,EAAE+N,EAAEuE,SAAS,GAAGxE,GAAG,oBAAoByE,SAAS,CAAC,IAAIvE,EAAEuE,SAASC,MAAMD,SAASE,qBAAqB,QAAQ,GAAGtE,EAAEoE,SAASG,cAAc,SAASvE,EAAE7M,KAAK,WAAW,QAAQtB,GAAGgO,EAAE2E,WAAW3E,EAAE4E,aAAazE,EAAEH,EAAE2E,YAAY3E,EAAE6E,YAAY1E,GAAGA,EAAE2E,WAAW3E,EAAE2E,WAAWC,QAAQjF,EAAEK,EAAE0E,YAAYN,SAASS,eAAelF,GAAG,CAAC,CAA6lEmF,CAA99D,+9DAA++D,IAAIC,EAAa,EAAErT,EAAU,SAASiO,EAAEC,GAAG,IAAI/N,EAAEgO,EAAEG,EAAEoE,SAASY,gBAAgB,QAAQnT,EAAE+N,SAAI,IAAS/N,OAAE,EAAOA,EAAEoT,mBAAmB,gBAAgBjF,KAAKA,EAAEoE,SAASG,cAAc,QAAQvP,GAAG,eAAeoP,SAAS5V,KAAKkW,YAAY1E,IAAI+E,GAAc,EAAE,IAAI3M,EAAE,UAAK,KAAU,QAAQyH,EAAED,SAAI,IAASC,OAAE,EAAOA,EAAEmC,WAAW,EAAEpC,EAAEoC,WAAWhB,EAAExB,EAAS,CAACxK,GAAG+P,EAAavC,KAAK7C,GAAGC,GAAGsF,EAAAA,OAAgB5E,EAAAA,cAAoBsD,EAAe,CAACC,MAAM7C,IAAIhB,GAAG,IAAIkB,EAAE,IAAIrI,SAAS,SAAS8G,GAAGmC,YAAY,WAAWnC,GAAG,GAAGvH,EAAE,IAAI,OAAO8I,EAAEiE,KAAK,WAAWD,EAAAA,OAAgB5E,EAAAA,cAAoBsD,EAAe,CAACE,SAAS9C,EAAEhM,KAAKgL,EAAE,EAAEkB,CAAC,EAAExP,EAAUC,QAAQ,SAASgO,EAAEC,GAAG,OAAOlO,EAAUiO,EAAEH,EAASA,EAAS,CAAC,EAAEI,GAAG,CAACzM,KAAK,YAAY,EAAEzB,EAAUgP,KAAK,SAASf,EAAEC,GAAG,OAAOlO,EAAUiO,EAAEH,EAASA,EAAS,CAAC,EAAEI,GAAG,CAACzM,KAAK,SAAS,EAAEzB,EAAUkP,KAAK,SAASjB,EAAEC,GAAG,OAAOlO,EAAUiO,EAAEH,EAASA,EAAS,CAAC,EAAEI,GAAG,CAACzM,KAAK,SAAS,EAAEzB,EAAUtC,MAAM,SAASuQ,EAAEC,GAAG,OAAOlO,EAAUiO,EAAEH,EAASA,EAAS,CAAC,EAAEI,GAAG,CAACzM,KAAK,UAAU,EAAEzB,EAAUiP,QAAQ,SAAShB,EAAEC,GAAG,OAAOlO,EAAUiO,EAAEH,EAASA,EAAS,CAAC,EAAEI,GAAG,CAACzM,KAAK,YAAY,EAAE,S,gGCEzlUiS,EAAY,CAAC,WAAY,UAAW,YAAa,OAAQ,WAAY,KAAM,aAI3ElH,EAAuBoC,EAAAA,YAAiB,SAAU+E,EAAMC,GAC1D,IAAIC,EAAWF,EAAKE,SAChB7Q,EAAU2Q,EAAK3Q,QACfzC,EAAYoT,EAAKpT,UACjBC,EAAOmT,EAAKnT,KACZE,EAAWiT,EAAKjT,SAChBoT,EAAUH,EAAKI,GACflJ,OAAwB,IAAZiJ,EAAqB,MAAQA,EACzCrT,EAAYkT,EAAKlT,UACjBzE,GAAQgY,EAAAA,EAAAA,GAA8BL,EAAMD,GAG5CO,GADJJ,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,YACP,IAAMtT,EACvC,OAAoBqO,EAAAA,cAAoB/D,GAAWsJ,EAAAA,EAAAA,GAAS,CAC1DP,IAAKA,GACJ5X,EAAO,CACRyE,UAAW2T,IAAW3T,EAAWwT,EAAiBzT,GAAQyT,EAAkB,IAAMzT,EAAMwC,GAAW,QAAUA,KAC3GtC,EACN,IACA8L,EAAQ6H,YAAc,UACtB,S", "sources": ["Util/helper_functions.js", "app/components/higher-order/withauth.jsx", "app/components/modal/PlaceOrderModel.jsx", "app/components/table/UsersOrderTable.jsx", "app/components/table/UsersPositionTable.jsx", "app/components/table/UsersTradesTable.jsx", "app/user-pages/master/MasterTradeBook.jsx", "app/user-pages/ui-helper.jsx", "../node_modules/cogo-toast/dist/index.es.js", "../node_modules/react-bootstrap/esm/Spinner.js"], "sourcesContent": ["export const formatStanderdTimeString = (inputTime) => {\r\n    const inputDate = new Date(inputTime);\r\n\r\n    // Check if inputDate is a valid date\r\n    if (!isNaN(inputDate.getTime())) {\r\n        const year = inputDate.getFullYear();\r\n        const month = (inputDate.getMonth() + 1).toString().padStart(2, \"0\");\r\n        const day = inputDate.getDate().toString().padStart(2, \"0\");\r\n        const hours = inputDate.getHours().toString().padStart(2, \"0\");\r\n        const minutes = inputDate.getMinutes().toString().padStart(2, \"0\");\r\n        const seconds = inputDate.getSeconds().toString().padStart(2, \"0\");\r\n\r\n        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    } else {\r\n        // If inputTime is not a valid date, return it as is (assumed to be in \"hh:mm:ss\" format)\r\n        return inputTime;\r\n    }\r\n};", "import React, { useEffect } from \"react\";\r\nimport { useHistory } from \"react-router-dom\";\r\nimport { API_URL } from \"../../../Util/constant\";\r\nconst withAuth = (WrappedComponent) => {\r\n\tconst AuthWrapper = (props) => {\r\n\t\t// localStorage.setItem(\r\n\t\t// \t\"admin_access_token\",\r\n\t\t// \t\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************.2pJTNAozJZkRDCJcIqE4OaKIEfq2may8sSTswl637LE\"\r\n\t\t// );\r\n\t\tconst history = useHistory();\r\n\t\tuseEffect(() => {\r\n\t\t\tconst token = localStorage.getItem(\"admin_access_token\");\r\n\t\t\tconst { hostname } = window.location;\r\n\t\t\tconst login_url = \"https://\" + hostname + \"/admin/\";\r\n\t\t\tif (!token) {\r\n\t\t\t\twindow.location.href = login_url;\r\n\t\t\t} else {\r\n\t\t\t\tconst requestOptions = {\r\n\t\t\t\t\tmethod: \"POST\",\r\n\t\t\t\t\theaders: { \"Content-Type\": \"application/json\" },\r\n\t\t\t\t\tbody: JSON.stringify({ token }),\r\n\t\t\t\t};\r\n\t\t\t\tfetch(API_URL + \"/auth/verify\", requestOptions)\r\n\t\t\t\t\t.then((response) => response.json())\r\n\t\t\t\t\t.then((data) => {\r\n\t\t\t\t\t\tif (data.code === \"token_not_valid\") {\r\n\t\t\t\t\t\t\twindow.location.href = login_url;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((error) => {\r\n\t\t\t\t\t\tconsole.error(\"Error:\", error);\r\n\t\t\t\t\t\twindow.location.href = login_url;\r\n\t\t\t\t\t});\r\n\t\t\t}\r\n\t\t}, [history]);\r\n\r\n\t\treturn <WrappedComponent {...props} />;\r\n\t};\r\n\r\n\treturn AuthWrapper;\r\n};\r\n\r\nexport default withAuth;\r\n", "import React, { useState } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON>dal, Form } from \"react-bootstrap\";\r\nimport { useMutation } from \"react-query\";\r\nimport ResponseModal from \"./ResponseModal\";\r\nimport Select from \"react-select\";\r\nimport cogoToast from \"cogo-toast\";\r\nimport { PlaceOrderApi } from \"../../../services/backendServices\";\r\nimport {\r\n\tcustomStylesForSelect,\r\n\thoverEffectOnSelect,\r\n} from \"../../user-pages/ui-helper\";\r\nimport { mergeStyles } from \"react-select/dist/react-select.cjs.prod\";\r\nimport DatePicker from \"react-datepicker\";\r\nimport \"react-datepicker/dist/react-datepicker.css\";\r\nimport { format } from \"date-fns\";\r\n\r\nexport default function PlaceOrderModal(props) {\r\n\tconst [showModal, setShowModal] = useState(true);\r\n\t// console.log(\"id \", props);\r\n\r\n\tfunction onBtnClose() {\r\n\t\tprops.setPlaceOrderModal({ show: false });\r\n\t\tsetShowModal(false);\r\n\t}\r\n\tconst [apiResponseModal, setApiResponseModal] = useState({\r\n\t\tshow: false,\r\n\t\tres: {},\r\n\t}); // Modal : on API Response\r\n\r\n\tconst [qty, setQty] = useState(1);\r\n\tconst [selectedOption, setSelectedOption] = useState({\r\n\t\tvalue: \"BUY\",\r\n\t\tlabel: \"BUY\",\r\n\t});\r\n\tconst [selectedOptionSide, setSelectedOptionSide] = useState({\r\n\t\tvalue: \"PE\",\r\n\t\tlabel: \"PE\",\r\n\t});\r\n\r\n\tconst [selectedTypeOption, setSelectedTypeOption] = useState({\r\n\t\tvalue: \"EQUITY\",\r\n\t\tlabel: \"EQUITY\",\r\n\t});\r\n\r\n\tconst [selectedProductOption, setSelectedProductOption] = useState({\r\n\t\tvalue: \"CNC\",\r\n\t\tlabel: \"CNC\",\r\n\t});\r\n\r\n\tconst optionType = [\r\n\t\t{\r\n\t\t\tvalue: \"EQUITY\",\r\n\t\t\tlabel: \"EQUITY\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tvalue: \"FUTURE\",\r\n\t\t\tlabel: \"FUTURE\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tvalue: \"OPTION\",\r\n\t\t\tlabel: \"OPTION\",\r\n\t\t},\r\n\t];\r\n\tconst productOptions = [\r\n\t\t{\r\n\t\t\tvalue: \"CNC\",\r\n\t\t\tlabel: \"CNC\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tvalue: \"NRML\",\r\n\t\t\tlabel: \"NRML\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tvalue: \"MIS\",\r\n\t\t\tlabel: \"MIS\",\r\n\t\t},\r\n\t];\r\n\r\n\tconst options = [\r\n\t\t{\r\n\t\t\tvalue: \"BUY\",\r\n\t\t\tlabel: \"BUY\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tvalue: \"SELL\",\r\n\t\t\tlabel: \"SELL\",\r\n\t\t},\r\n\t];\r\n\tconst optionSide = [\r\n\t\t{\r\n\t\t\tvalue: \"PE\",\r\n\t\t\tlabel: \"PE\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tvalue: \"CE\",\r\n\t\t\tlabel: \"CE\",\r\n\t\t},\r\n\t];\r\n\r\n\tconst [script, setScript] = useState();\r\n\tconst [selectedDate, setSelectedDate] = useState(new Date());\r\n\tconst [strikPrice, setStrikPrice] = useState();\r\n\tconst [confirmModel, setConfirmModel] = useState();\r\n\r\n\tconst placeOrderApiCall = useMutation(\"PlaceOrderApi\", (params) => {\r\n\t\treturn PlaceOrderApi(params)\r\n\t\t\t.then((res) => {\r\n\t\t\t\t// console.log(\"res\", res);\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tonBtnClose();\r\n\t\t\t\t\tcogoToast.success(`${res.msg} OK`);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// console.log(\"res.msg\", res.msg);\r\n\t\t\t\t\tsetConfirmModel(false);\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t});\r\n\t});\r\n\r\n\tconst handlePlaceOrder = (e) => {\r\n\t\te.preventDefault();\r\n\r\n\t\tif (\r\n\t\t\tselectedTypeOption.value === \"EQUITY\" &&\r\n\t\t\tqty &&\r\n\t\t\tselectedOption?.value &&\r\n\t\t\tscript\r\n\t\t) {\r\n\t\t\tsetConfirmModel(true);\r\n\t\t} else if (\r\n\t\t\tselectedTypeOption.value === \"FUTURE\" &&\r\n\t\t\tqty &&\r\n\t\t\tselectedOption?.value &&\r\n\t\t\tscript\r\n\t\t) {\r\n\t\t\tsetConfirmModel(true);\r\n\t\t} else if (\r\n\t\t\tselectedTypeOption.value === \"OPTION\" &&\r\n\t\t\tqty &&\r\n\t\t\tselectedOption?.value &&\r\n\t\t\tscript &&\r\n\t\t\tstrikPrice &&\r\n\t\t\tselectedOptionSide?.value\r\n\t\t) {\r\n\t\t\tsetConfirmModel(true);\r\n\t\t} else {\r\n\t\t\tcogoToast.error(\"Please fill all fields\");\r\n\t\t}\r\n\t};\r\n\r\n\tconst confirmOrder = (e) => {\r\n\t\t// console.log(\"order place with details\", props.id);\r\n\t\tlet values;\r\n\t\tif (selectedTypeOption.value === \"EQUITY\") {\r\n\t\t\tvalues = {\r\n\t\t\t\torder_type: \"eq\", // eq, future, option\r\n\t\t\t\tscript_name: script,\r\n\t\t\t\tstrategy_id: Number(props.id),\r\n\t\t\t\tquantity: Number(qty),\r\n\t\t\t\ttransaction: selectedOption.value,\r\n\t\t\t\tproduct: selectedProductOption.value, // \"NRML\", \"CNC\", \"MIS\"\r\n\t\t\t};\r\n\t\t} else if (selectedTypeOption.value === \"OPTION\") {\r\n\t\t\tvalues = {\r\n\t\t\t\torder_type: \"option\", // eq, future, option\r\n\t\t\t\tscript_name: script,\r\n\t\t\t\texpiry_date: format(selectedDate, \"MM-dd-yyyy\"),\r\n\t\t\t\tstrike_price: Number(strikPrice), // for option\r\n\t\t\t\toption_type: selectedOptionSide.value, // CE PE : for option\r\n\t\t\t\tstrategy_id: Number(props.id),\r\n\t\t\t\tquantity: Number(qty),\r\n\t\t\t\ttransaction: selectedOption.value,\r\n\t\t\t\tproduct: selectedProductOption.value, // \"NRML\", \"CNC\", \"MIS\"\r\n\t\t\t};\r\n\t\t} else {\r\n\t\t\tvalues = {\r\n\t\t\t\torder_type: \"future\", // eq, future, option\r\n\t\t\t\tscript_name: script,\r\n\t\t\t\texpiry_date: format(selectedDate, \"MM-dd-yyyy\"),\r\n\t\t\t\tstrategy_id: Number(props.id),\r\n\t\t\t\tquantity: Number(qty),\r\n\t\t\t\ttransaction: selectedOption.value,\r\n\t\t\t\tproduct: selectedProductOption.value, // \"NRML\", \"CNC\", \"MIS\"\r\n\t\t\t};\r\n\t\t}\r\n\t\tvalues && placeOrderApiCall.mutate(values);\r\n\t};\r\n\r\n\tconst handleClearButton = () => {\r\n\t\tsetSelectedOption({\r\n\t\t\tvalue: \"BUY\",\r\n\t\t\tlabel: \"BUY\",\r\n\t\t});\r\n\t\tsetSelectedOptionSide({\r\n\t\t\tvalue: \"PE\",\r\n\t\t\tlabel: \"PE\",\r\n\t\t});\r\n\t\tsetSelectedTypeOption({\r\n\t\t\tvalue: \"EQUITY\",\r\n\t\t\tlabel: \"EQUITY\",\r\n\t\t});\r\n\t\tsetSelectedProductOption({\r\n\t\t\tvalue: \"CNC\",\r\n\t\t\tlabel: \"CNC\",\r\n\t\t});\r\n\t\tsetQty(\"\");\r\n\t\tsetStrikPrice(null);\r\n\t\tsetSelectedDate(new Date());\r\n\t};\r\n\tconst handleDateChange = (date) => {\r\n\t\tsetSelectedDate(date);\r\n\t\t// const formattedDate = format(date, \"MM-dd-yyyy\");\r\n\t\t// console.log(\"Selected Date:\", formattedDate);\r\n\t};\r\n\r\n\treturn (\r\n\t\t<Modal\r\n\t\t\tshow={showModal}\r\n\t\t\tanimation={true}\r\n\t\t\tsize=\"lg\"\r\n\t\t\tclassName=\"mt-5 custom-modal \"\r\n\t\t>\r\n\t\t\t<Modal.Header className=\"text-center py-2\">\r\n\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t<h3 className=\"mb-0\">Place order</h3>\r\n\t\t\t\t</Modal.Title>\r\n\t\t\t</Modal.Header>\r\n\t\t\t<Modal.Body className=\"p-10 \">\r\n\t\t\t\t<div>\r\n\t\t\t\t\t<div className=\"row\">\r\n\t\t\t\t\t\t<div className=\"col-lg-12 grid-margin stretch-card mb-0\">\r\n\t\t\t\t\t\t\t<div className=\"card-body pb-1\">\r\n\t\t\t\t\t\t\t\t<Form>\r\n\t\t\t\t\t\t\t\t\t<div className=\"row scroll\">\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className=\"text-muted\">Type</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\t\t\t\toptions={optionType}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={selectedTypeOption}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(selectedOption) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetSelectedTypeOption(selectedOption)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyles={mergeStyles(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\thoverEffectOnSelect,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcustomStylesForSelect\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-4\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className=\"text-muted\">Script</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Script\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={script}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetScript(e.target.value?.toUpperCase())\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t{(selectedTypeOption.value === \"FUTURE\" ||\r\n\t\t\t\t\t\t\t\t\t\t\tselectedTypeOption.value === \"OPTION\") && (\r\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className=\"text-muted\">Date</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<DatePicker\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tselected={selectedDate}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleDateChange}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"form-control\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdateFormat=\"dd-MM-yyyy\" // Customize date format if needed\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-2\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className=\"text-muted\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedTypeOption.value === \"EQUITY\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"Qty\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"Lot\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Qty\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={qty}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => setQty(e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t{selectedTypeOption.value === \"OPTION\" && (\r\n\t\t\t\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className=\"text-muted\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tStrike Price\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Strike Price\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={strikPrice}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => setStrikPrice(e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className=\"text-muted\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tOption Side\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\toptions={optionSide}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={selectedOptionSide}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(selectedOption) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetSelectedOptionSide(selectedOption)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyles={mergeStyles(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thoverEffectOnSelect,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcustomStylesForSelect\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className=\"text-muted\">Action</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\t\t\t\toptions={options}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={selectedOption}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(selectedOption) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetSelectedOption(selectedOption)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Search for an item...\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyles={mergeStyles(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\thoverEffectOnSelect,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcustomStylesForSelect\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className=\"text-muted\">Product</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\t\t\t\toptions={productOptions}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={selectedProductOption}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(selectedOption) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetSelectedProductOption(selectedOption)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyles={mergeStyles(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\thoverEffectOnSelect,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcustomStylesForSelect\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"row scroll\">\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-6 mt-2\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"button\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"btn btn-md btn-danger mr-3\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={handleClearButton}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\tClear\r\n\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"btn btn-md mr-3\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={handlePlaceOrder}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\tPlaceOrder\r\n\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Form>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t{confirmModel && (\r\n\t\t\t\t\t\t<Modal\r\n\t\t\t\t\t\t\tshow={confirmModel}\r\n\t\t\t\t\t\t\tanimation={true}\r\n\t\t\t\t\t\t\tsize=\"md\"\r\n\t\t\t\t\t\t\tclassName=\"mt-5\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Modal.Header className=\"d-flex justify-content-center py-2\">\r\n\t\t\t\t\t\t\t\t<Modal.Title>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t<h3 className=\"mb-0\">Confirm</h3>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t\t\t<Modal.Body className=\"p-10\" style={{ backgroundColor: \"white\" }}>\r\n\t\t\t\t\t\t\t\t<table\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\twidth: \"60%\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"0 auto\",\r\n\t\t\t\t\t\t\t\t\t\tborderCollapse: \"collapse\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<tbody>\r\n\t\t\t\t\t\t\t\t\t\t<tr>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>Order Type</td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>: </td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<b>{selectedTypeOption.value}</b>\r\n\t\t\t\t\t\t\t\t\t\t\t</td>\r\n\t\t\t\t\t\t\t\t\t\t</tr>\r\n\t\t\t\t\t\t\t\t\t\t<tr>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>Script</td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>: </td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<b>{script}</b>\r\n\t\t\t\t\t\t\t\t\t\t\t</td>\r\n\t\t\t\t\t\t\t\t\t\t</tr>\r\n\t\t\t\t\t\t\t\t\t\t{selectedTypeOption.value === \"FUTURE\" && (\r\n\t\t\t\t\t\t\t\t\t\t\t<tr>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selectedDate && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>Date</td>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>: </td>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{format(selectedDate, \"dd-MM-yyyy\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</td>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t</tr>\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t<tr>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selectedTypeOption.value === \"EQUITY\" ? \"Qty\" : \"Lot\"}\r\n\t\t\t\t\t\t\t\t\t\t\t</td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>: </td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<b>{qty}</b>\r\n\t\t\t\t\t\t\t\t\t\t\t</td>\r\n\t\t\t\t\t\t\t\t\t\t</tr>\r\n\t\t\t\t\t\t\t\t\t\t{selectedTypeOption.value === \"OPTION\" && (\r\n\t\t\t\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<tr>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>Strike Price</td>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>: </td>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b>{strikPrice}</b>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</td>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</tr>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<tr>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>Option Side</td>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>: </td>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b>{selectedOptionSide.value}</b>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</td>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</tr>\r\n\t\t\t\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t<tr>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>Action</td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>: </td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<b>{selectedOption.value}</b>\r\n\t\t\t\t\t\t\t\t\t\t\t</td>\r\n\t\t\t\t\t\t\t\t\t\t</tr>\r\n\t\t\t\t\t\t\t\t\t\t<tr>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>Product</td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>: </td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<b>{selectedProductOption.value}</b>\r\n\t\t\t\t\t\t\t\t\t\t\t</td>\r\n\t\t\t\t\t\t\t\t\t\t</tr>\r\n\t\t\t\t\t\t\t\t\t</tbody>\r\n\t\t\t\t\t\t\t\t</table>\r\n\t\t\t\t\t\t\t</Modal.Body>\r\n\r\n\t\t\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<Button variant=\"outline-primary\" onClick={confirmOrder}>\r\n\t\t\t\t\t\t\t\t\t\tConfirm\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => setConfirmModel(false)}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\tCancel\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t\t\t</Modal>\r\n\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t{apiResponseModal.show && (\r\n\t\t\t\t\t\t<ResponseModal\r\n\t\t\t\t\t\t\tres={apiResponseModal.res}\r\n\t\t\t\t\t\t\tsetApiResponseModal={setApiResponseModal}\r\n\t\t\t\t\t\t\tmsg={apiResponseModal.res.msg}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</div>\r\n\t\t\t</Modal.Body>\r\n\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t<div>\r\n\t\t\t\t\t<Button variant=\"outline-danger\" onClick={onBtnClose}>\r\n\t\t\t\t\t\tCancel\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</div>\r\n\t\t\t</Modal.Footer>\r\n\t\t</Modal>\r\n\t);\r\n}\r\n", "import React, { useEffect, useMemo, useState } from \"react\";\r\nimport DataTable from \"react-data-table-component\";\r\nimport { But<PERSON>, Modal } from \"react-bootstrap\";\r\nimport {\r\n\tcreateDataTableTheme,\r\n\tgetOrderStatus,\r\n\tgetTransactionType,\r\n} from \"../../user-pages/ui-helper\";\r\nimport { Form } from \"react-bootstrap\";\r\nimport { formatStanderdTimeString } from \"../../../Util/helper_functions\";\r\nimport {\r\n\tCancelOrderApi,\r\n\tExecuteOrderNow,\r\n} from \"../../../services/backendServices\";\r\nimport { useMutation } from \"react-query\";\r\nimport ResponseModal from \"../modal/ResponseModal\";\r\nimport { useParams } from \"react-router-dom\";\r\nexport const UsersOrderTable = (props) => {\r\n\tconst { user_id } = useParams();\r\n\tconst [tableDataFormatted, setTableDataFormatted] = useState([]);\r\n\tconst rawUserMap = useMemo(() => new Map(), []);\r\n\tconst [showDeletePopup, setShowDeletePopup] = useState({\r\n\t\tstatus: false,\r\n\t\tobj: null,\r\n\t\tind: null,\r\n\t});\r\n\tconst [executenowPopup, setExecutenowPopup] = useState({\r\n\t\tstatus: false,\r\n\t\tobj: null,\r\n\t\tind: null,\r\n\t});\r\n\r\n\tconst [apiResponseModal, setApiResponseModal] = useState({\r\n\t\tshow: false,\r\n\t\tres: {},\r\n\t}); // Modal : on API Response\r\n\r\n\tuseEffect(() => {\r\n\t\tsetTableData(props.data?.orders);\r\n\t\t// Setting row data in rawUserMap with key value\r\n\t\tprops.data.orders.map((obj, index) => {\r\n\t\t\treturn rawUserMap.set(obj.orderid, obj);\r\n\t\t});\r\n\t}, [props.data]);\r\n\r\n\tfunction setTableData(tableData) {\r\n\t\tvar rowTableData = [];\r\n\t\ttableData.map((obj, index) => {\r\n\t\t\treturn rowTableData.push(getRowFormatForBrokerTable(index, obj));\r\n\t\t});\r\n\t\tsetTableDataFormatted(rowTableData);\r\n\t}\r\n\r\n\tfunction getRowFormatForBrokerTable(ind, obj) {\r\n\t\treturn {\r\n\t\t\tindex: ind + 1,\r\n\t\t\tsymbol: obj.symbol,\r\n\t\t\ttransaction: getTransactionType(obj.transaction),\r\n\t\t\tproduct: obj.product,\r\n\t\t\tordertype: obj.ordertype,\r\n\t\t\tquantity: obj.quantity,\r\n\t\t\tprice: obj.price,\r\n\t\t\ttime: formatStanderdTimeString(obj.time),\r\n\t\t\torderid: obj.orderid,\r\n\t\t\tstatus: obj.is_open ? (\r\n\t\t\t\t<div className=\"d-flex align-item-center\">\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"primary\"\r\n\t\t\t\t\t\tclassName=\"btnRemoveUserBroker\"\r\n\t\t\t\t\t\tid={obj.id}\r\n\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\tsetExecutenowPopup({ status: true, obj: obj, ind: ind })\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<i\r\n\t\t\t\t\t\t\tclassName=\"mdi mdi mdi-checkbox-marked-circle-outline\r\ncard-text m-0\"\r\n\t\t\t\t\t\t></i>\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"danger\"\r\n\t\t\t\t\t\tclassName=\"ml-3 btnRemoveUserBroker\"\r\n\t\t\t\t\t\tid={obj.id}\r\n\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\tsetShowDeletePopup({ status: true, obj: obj, ind: ind })\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<i\r\n\t\t\t\t\t\t\tclassName=\"mdi mdi mdi mdi-close-circle-outline\r\n\r\ncard-text m-0\"\r\n\t\t\t\t\t\t></i>\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</div>\r\n\t\t\t) : (\r\n\t\t\t\tgetOrderStatus(obj?.status, obj?.reason, obj)\r\n\t\t\t),\r\n\r\n\t\t\tobj: obj,\r\n\t\t};\r\n\t}\r\n\r\n\tconst actionTypeSortFunction = (a, b) => {\r\n\t\tif (\r\n\t\t\ta.obj.transaction === \"B\" ||\r\n\t\t\ta.obj.transaction === \"b\" ||\r\n\t\t\ta.obj.transaction === \"buy\" ||\r\n\t\t\ta.obj.transaction === \"BUY\" ||\r\n\t\t\ta.obj.transaction === \"Buy\"\r\n\t\t) {\r\n\t\t\treturn 1;\r\n\t\t} else {\r\n\t\t\treturn -1;\r\n\t\t}\r\n\t};\r\n\r\n\tconst columns = [\r\n\t\t{\r\n\t\t\tname: \"Id\",\r\n\t\t\tselector: (row) => row.index,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"65px\",\r\n\t\t\tminWidth: \"65px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Symbol\",\r\n\t\t\tselector: (row) => row.symbol,\r\n\t\t\twrap: true,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Trans\",\r\n\t\t\tselector: (row) => row.transaction,\r\n\t\t\tsortable: true,\r\n\t\t\tsortFunction: actionTypeSortFunction,\r\n\t\t\tmaxWidth: \"90px\",\r\n\t\t\tminWidth: \"90px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Product\",\r\n\t\t\tselector: (row) => row.product,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"120px\",\r\n\t\t\tminWidth: \"120px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Type\",\r\n\t\t\tselector: (row) => row.ordertype,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"120px\",\r\n\t\t\tminWidth: \"120px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Qty\",\r\n\t\t\tselector: (row) => row.quantity,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"80px\",\r\n\t\t\tminWidth: \"80px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Price\",\r\n\t\t\tselector: (row) => row.price,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"100px\",\r\n\t\t\tminWidth: \"100px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Time\",\r\n\t\t\tselector: (row) => row.time,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"100px\",\r\n\t\t\tminWidth: \"100px\",\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Order Id\",\r\n\t\t\tselector: (row) => row.orderid,\r\n\t\t\twrap: true,\r\n\t\t\tsortable: true,\r\n\t\t},\r\n\t\t{ name: \"Status\", selector: (row) => row.status, sortable: true },\r\n\t\t{ name: \"obj\", selector: (row) => row.obj, omit: true },\r\n\t];\r\n\r\n\tconst handleSearch = (e) => {\r\n\t\tvar search_val = e.target.value;\r\n\t\tvar searchValueLowerCase = search_val?.toLowerCase();\r\n\t\tvar filteredData = [];\r\n\t\tfor (let value of rawUserMap.values()) {\r\n\t\t\tconst timeVal = formatStanderdTimeString(value?.time);\r\n\t\t\tconst statusVal = getOrderStatus(value?.status, value?.reason, value);\r\n\t\t\tconst formatedStatusVal = statusVal?.props?.children;\r\n\t\t\tif (\r\n\t\t\t\tvalue.symbol\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tvalue.transaction\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tvalue.product\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tvalue.ordertype\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tvalue.quantity\r\n\t\t\t\t\t?.toString()\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tvalue.price\r\n\t\t\t\t\t?.toString()\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tvalue.orderid\r\n\t\t\t\t\t?.toString()\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tvalue.time\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\ttimeVal?.toLowerCase().includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tformatedStatusVal\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase())\r\n\t\t\t) {\r\n\t\t\t\tfilteredData.push(value);\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetTableDataFormatted(\r\n\t\t\tfilteredData.map((obj, index) => getRowFormatForBrokerTable(index, obj))\r\n\t\t);\r\n\t};\r\n\r\n\t//createDataTableTheme(); // This is for designing Datatable in dark theme\r\n\r\n\t// API CALL to Cancel Order\r\n\tconst CancelOrderApiCall = useMutation(\"CancelOrderApi\", () =>\r\n\t\tCancelOrderApi(user_id, showDeletePopup.obj.orderid)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\trawUserMap.delete(showDeletePopup.obj.id);\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\tremoveRowByIndex(showDeletePopup.ind);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t}\r\n\t\t\t\tsetShowDeletePopup({ status: false, obj: null, ind: null });\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t\treturn Promise.reject(e); // wrap in Promise\r\n\t\t\t})\r\n\t);\r\n\r\n\t// API CALL to Exicute Order\r\n\tconst ExecuteOrderNowApiCall = useMutation(\"ExecuteOrderNow\", () =>\r\n\t\tExecuteOrderNow(user_id, executenowPopup.obj.orderid)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t}\r\n\t\t\t\tsetExecutenowPopup({ status: false, obj: null, ind: null });\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t\treturn Promise.reject(e); // wrap in Promise\r\n\t\t\t})\r\n\t);\r\n\r\n\tfunction removeRowByIndex(index) {\r\n\t\tsetTableDataFormatted((prevData) => {\r\n\t\t\tconst updatedData = [...prevData];\r\n\t\t\tupdatedData.splice(index, 1);\r\n\t\t\treturn updatedData;\r\n\t\t});\r\n\t}\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<div className=\"table-responsive mt-3\">\r\n\t\t\t\t<Form.Group>\r\n\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\tplaceholder=\"Search\"\r\n\t\t\t\t\t\tclassName=\"mb-2 searchbox-style\"\r\n\t\t\t\t\t\tonChange={handleSearch}\r\n\t\t\t\t\t\trequired\r\n\t\t\t\t\t/>\r\n\t\t\t\t</Form.Group>\r\n\t\t\t\t<DataTable\r\n\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\tdata={tableDataFormatted}\r\n\t\t\t\t\tpagination={false}\r\n\t\t\t\t\tpaginationPerPage={10}\r\n\t\t\t\t\thighlightOnHover\r\n\t\t\t\t\ttheme=\"solarized\"\r\n\t\t\t\t/>\r\n\t\t\t</div>\r\n\t\t\t{showDeletePopup.status && (\r\n\t\t\t\t<Modal show={true} animation={true} size=\"md\" className=\"mt-5\">\r\n\t\t\t\t\t<Modal.Header className=\"text-center py-3\">\r\n\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t<h5 className=\"mb-0\">Cancel Order</h5>\r\n\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t<Modal.Body className=\"p-10\">Do you want cancel order ?</Modal.Body>\r\n\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\tonClick={() => setShowDeletePopup({ status: false, id: \"\" })}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tvariant=\"outline-primary\"\r\n\t\t\t\t\t\t\t\tclassName=\"mx-2 px-3\"\r\n\t\t\t\t\t\t\t\tonClick={() => CancelOrderApiCall.mutate()}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tYes\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t</Modal>\r\n\t\t\t)}\r\n\r\n\t\t\t{executenowPopup.status && (\r\n\t\t\t\t<Modal show={true} animation={true} size=\"md\" className=\"mt-5\">\r\n\t\t\t\t\t<Modal.Header className=\"text-center py-3\">\r\n\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t<h5 className=\"mb-0\">Execute Order Now</h5>\r\n\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t<Modal.Body className=\"p-10\">\r\n\t\t\t\t\t\tDo you want Execute Order Now ?\r\n\t\t\t\t\t</Modal.Body>\r\n\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\tonClick={() => setExecutenowPopup({ status: false, id: \"\" })}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tvariant=\"outline-primary\"\r\n\t\t\t\t\t\t\t\tclassName=\"mx-2 px-3\"\r\n\t\t\t\t\t\t\t\tonClick={() => ExecuteOrderNowApiCall.mutate()}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tYes\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t</Modal>\r\n\t\t\t)}\r\n\t\t\t{apiResponseModal.show && (\r\n\t\t\t\t<ResponseModal\r\n\t\t\t\t\tres={apiResponseModal.res}\r\n\t\t\t\t\tsetApiResponseModal={setApiResponseModal}\r\n\t\t\t\t\tmsg={apiResponseModal.res.msg}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t</>\r\n\t);\r\n};\r\n", "import React, { useEffect, useMemo, useState } from \"react\";\r\nimport DataTable from \"react-data-table-component\";\r\nimport { useMutation } from \"react-query\";\r\nimport { SquareOff } from \"../../../services/backendServices\";\r\nimport ResponseModal from \"../modal/ResponseModal\";\r\nimport { Button, Form, Modal } from \"react-bootstrap\";\r\nimport {\r\n\tgetSubPnlComponent,\r\n\tgetTransactionType,\r\n} from \"../../user-pages/ui-helper\";\r\n\r\nexport const UsersPositionTable = (props) => {\r\n\tconst [apiResponseModal, setApiResponseModal] = useState({\r\n\t\tshow: false,\r\n\t\tres: {},\r\n\t}); // Modal : on API Response\r\n\tconst [showSquareOffPopup, setShowSquareOffPopup] = useState({\r\n\t\tstatus: false,\r\n\t\tvalues: {},\r\n\t});\r\n\tconst [tableDataFormatted, setTableDataFormatted] = useState([]);\r\n\tconst rawUserMap = useMemo(() => new Map(), []);\r\n\r\n\tfunction setSquareOffBtn(qty, tradingSymbol, user_id) {\r\n\t\treturn (\r\n\t\t\t<button\r\n\t\t\t\tclassName={\r\n\t\t\t\t\tqty === 0 ? \"btn btn-outline-danger\" : \"btn btn-danger btnSquareOff\"\r\n\t\t\t\t}\r\n\t\t\t\tdisabled={qty === 0 ? true : false}\r\n\t\t\t\tonClick={() =>\r\n\t\t\t\t\tsetShowSquareOffPopup({\r\n\t\t\t\t\t\tstatus: true,\r\n\t\t\t\t\t\tvalues: { user_id, symbol: tradingSymbol, is_all: false },\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t>\r\n\t\t\t\t{\" \"}\r\n\t\t\t\tSquare OFF\r\n\t\t\t</button>\r\n\t\t);\r\n\t}\r\n\r\n\t// API CALL to Square Off Position\r\n\tconst squareOffMutation = useMutation(\"SquareOff\", (values) =>\r\n\t\tSquareOff(values.user_id, values.symbol)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\tprops.fetchBrokerData();\r\n\t\t\t\t}\r\n\t\t\t\tsetShowSquareOffPopup({ status: false, values: {} });\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t})\r\n\t);\r\n\t// Square OFF Position\r\n\tfunction SquareOffThisPosition(values) {\r\n\t\tsquareOffMutation.mutate(values);\r\n\t}\r\n\r\n\tuseEffect(() => {\r\n\t\t// console.log(props.data);\r\n\t\tsetTableData(props.data?.positions);\r\n\t\t// Setting row data in rawUserMap with key value\r\n\t\tprops.data.positions.map((obj, index) => {\r\n\t\t\treturn rawUserMap.set(obj.tradingsymbol, obj);\r\n\t\t});\r\n\t}, [props.data]);\r\n\r\n\tfunction setTableData(tableData) {\r\n\t\tvar rowTableData = [];\r\n\t\ttableData.map((obj, index) => {\r\n\t\t\treturn rowTableData.push(getRowFormatForBrokerTable(index, obj));\r\n\t\t});\r\n\t\tsetTableDataFormatted(rowTableData);\r\n\t}\r\n\r\n\tfunction getRowFormatForBrokerTable(ind, obj) {\r\n\t\treturn {\r\n\t\t\tindex: ind + 1,\r\n\t\t\ttradingsymbol: obj.tradingsymbol,\r\n\t\t\tproducttype: obj.producttype,\r\n\t\t\tquantity: obj.quantity,\r\n\t\t\tpnl: getSubPnlComponent(obj.pnl),\r\n\t\t\tltp: obj.ltp,\r\n\t\t\tavgprice: obj.avgprice,\r\n\t\t\taction: getTransactionType(obj.action),\r\n\t\t\tsquareoff: setSquareOffBtn(\r\n\t\t\t\tobj.quantity,\r\n\t\t\t\tobj.tradingsymbol,\r\n\t\t\t\tprops.user_id\r\n\t\t\t),\r\n\t\t\tobj: obj,\r\n\t\t};\r\n\t}\r\n\r\n\tconst pnlSortFunction = (a, b) => {\r\n\t\treturn a.obj.pnl > b.obj.pnl ? -1 : 1;\r\n\t};\r\n\r\n\tconst actionTypeSortFunction = (a, b) => {\r\n\t\tif (\r\n\t\t\ta.obj.action === \"B\" ||\r\n\t\t\ta.obj.action === \"b\" ||\r\n\t\t\ta.obj.action === \"buy\" ||\r\n\t\t\ta.obj.action === \"BUY\" ||\r\n\t\t\ta.obj.action === \"Buy\"\r\n\t\t) {\r\n\t\t\treturn 1;\r\n\t\t} else {\r\n\t\t\treturn -1;\r\n\t\t}\r\n\t};\r\n\r\n\tconst columns = [\r\n\t\t{\r\n\t\t\tname: \"Id\",\r\n\t\t\tselector: (row) => row.index,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"65px\",\r\n\t\t\tminWidth: \"65px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Symbol\",\r\n\t\t\tselector: (row) => row.tradingsymbol,\r\n\t\t\twrap: true,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Type\",\r\n\t\t\tselector: (row) => row.producttype,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Qty\",\r\n\t\t\tselector: (row) => row.quantity,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"100px\",\r\n\t\t\tminWidth: \"100px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"P&L\",\r\n\t\t\tselector: (row) => row.pnl,\r\n\t\t\tsortable: true,\r\n\t\t\tsortFunction: pnlSortFunction,\r\n\t\t},\r\n\t\t{ name: \"LTP\", selector: (row) => row.ltp, sortable: true },\r\n\t\t{ name: \"Avg Price\", selector: (row) => row.avgprice, sortable: true },\r\n\t\t{\r\n\t\t\tname: \"Trans\",\r\n\t\t\tselector: (row) => row.action,\r\n\t\t\tsortable: true,\r\n\t\t\tsortFunction: actionTypeSortFunction,\r\n\t\t},\r\n\t\t{ name: \"Square Off\", selector: (row) => row.squareoff },\r\n\t\t{ name: \"obj\", selector: (row) => row.obj, omit: true },\r\n\t];\r\n\r\n\tconst handleSearch = (e) => {\r\n\t\tvar search_val = e.target.value;\r\n\t\tvar searchValueLowerCase = search_val?.toLowerCase();\r\n\t\tvar filteredData = [];\r\n\t\tfor (let value of rawUserMap.values()) {\r\n\t\t\tif (\r\n\t\t\t\tvalue.tradingsymbol\r\n\t\t\t\t\t.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tvalue.producttype\r\n\t\t\t\t\t.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tvalue.quantity\r\n\t\t\t\t\t.toString()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tvalue.pnl\r\n\t\t\t\t\t?.toString()\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tvalue.ltp\r\n\t\t\t\t\t?.toString()\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tvalue.avgprice\r\n\t\t\t\t\t?.toString()\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tvalue.action.toLowerCase().includes(searchValueLowerCase.toLowerCase())\r\n\t\t\t) {\r\n\t\t\t\tfilteredData.push(value);\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetTableDataFormatted(\r\n\t\t\tfilteredData.map((obj, index) => getRowFormatForBrokerTable(index, obj))\r\n\t\t);\r\n\t};\r\n\r\n\t//createDataTableTheme(); // This is for designing Datatable in dark theme\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<div className=\"table-responsive\">\r\n\t\t\t\t<Form.Group>\r\n\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\tplaceholder=\"Search\"\r\n\t\t\t\t\t\tclassName=\"mb-2 searchbox-style\"\r\n\t\t\t\t\t\tonChange={handleSearch}\r\n\t\t\t\t\t\trequired\r\n\t\t\t\t\t/>\r\n\t\t\t\t</Form.Group>\r\n\t\t\t\t<DataTable\r\n\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\tdata={tableDataFormatted}\r\n\t\t\t\t\tpagination={false}\r\n\t\t\t\t\tpaginationPerPage={10}\r\n\t\t\t\t\thighlightOnHover\r\n\t\t\t\t\ttheme=\"solarized\"\r\n\t\t\t\t/>\r\n\t\t\t\t{apiResponseModal.show && (\r\n\t\t\t\t\t<ResponseModal\r\n\t\t\t\t\t\tres={apiResponseModal.res}\r\n\t\t\t\t\t\tsetApiResponseModal={setApiResponseModal}\r\n\t\t\t\t\t\tmsg={apiResponseModal.res.msg}\r\n\t\t\t\t\t/>\r\n\t\t\t\t)}\r\n\t\t\t\t{showSquareOffPopup.status && (\r\n\t\t\t\t\t<Modal show={true} animation={true} size=\"md\" className=\"mt-5\">\r\n\t\t\t\t\t\t<Modal.Header className=\"text-center py-3\">\r\n\t\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t\t<h5 className=\"mb-0\">Square Off</h5>\r\n\t\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t\t<Modal.Body className=\"p-10\">\r\n\t\t\t\t\t\t\tDo you want to Square Off ?\r\n\t\t\t\t\t\t</Modal.Body>\r\n\t\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\tsetShowSquareOffPopup({ status: false, values: {} })\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tvariant=\"outline-primary\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"mx-2 px-3\"\r\n\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\tSquareOffThisPosition(showSquareOffPopup.values)\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tYes\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t\t</Modal>\r\n\t\t\t\t)}\r\n\t\t\t</div>\r\n\t\t</>\r\n\t);\r\n};\r\n", "import React, { useEffect, useMemo, useState } from \"react\";\r\nimport DataTable from \"react-data-table-component\";\r\nimport {\r\n\tcreateDataTableTheme,\r\n\tgetTransactionType,\r\n} from \"../../user-pages/ui-helper\";\r\nimport { Form } from \"react-bootstrap\";\r\nimport { formatStanderdTimeString } from \"../../../Util/helper_functions\";\r\n\r\nexport const UsersTradesTable = (props) => {\r\n\tconst [tableDataFormatted, setTableDataFormatted] = useState([]);\r\n\tconst rawUserMap = useMemo(() => new Map(), []);\r\n\r\n\tuseEffect(() => {\r\n\t\tsetTableData(props.data?.trades);\r\n\t\t// Setting row data in rawUserMap with key value\r\n\t\tprops.data.trades.map((obj, index) => {\r\n\t\t\treturn rawUserMap.set(obj.orderid, obj);\r\n\t\t});\r\n\t}, [props.data]);\r\n\r\n\tfunction setTableData(tableData) {\r\n\t\tvar rowTableData = [];\r\n\t\ttableData.map((obj, index) => {\r\n\t\t\treturn rowTableData.push(getRowFormatForBrokerTable(index, obj));\r\n\t\t});\r\n\t\tsetTableDataFormatted(rowTableData);\r\n\t}\r\n\r\n\tfunction getRowFormatForBrokerTable(ind, obj) {\r\n\t\treturn {\r\n\t\t\tindex: ind + 1,\r\n\t\t\tsymbol: obj.symbol,\r\n\t\t\torderid: obj.orderid,\r\n\t\t\tproduct: obj.product,\r\n\t\t\ttransaction: getTransactionType(obj.transaction),\r\n\t\t\tprice: obj.price,\r\n\t\t\ttime: formatStanderdTimeString(obj.time),\r\n\t\t\tobj: obj,\r\n\t\t};\r\n\t}\r\n\r\n\tconst actionTypeSortFunction = (a, b) => {\r\n\t\tif (\r\n\t\t\ta.obj.transaction === \"B\" ||\r\n\t\t\ta.obj.transaction === \"b\" ||\r\n\t\t\ta.obj.transaction === \"buy\" ||\r\n\t\t\ta.obj.transaction === \"BUY\" ||\r\n\t\t\ta.obj.transaction === \"Buy\"\r\n\t\t) {\r\n\t\t\treturn 1;\r\n\t\t} else {\r\n\t\t\treturn -1;\r\n\t\t}\r\n\t};\r\n\r\n\tconst columns = [\r\n\t\t{\r\n\t\t\tname: \"Id\",\r\n\t\t\tselector: (row) => row.index,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"65px\",\r\n\t\t\tminWidth: \"65px\",\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Symbol\",\r\n\t\t\tselector: (row) => row.symbol,\r\n\t\t\twrap: true,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"220px\",\r\n\t\t\tminWidth: \"220px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Order ID\",\r\n\t\t\tselector: (row) => row.orderid,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Product\",\r\n\t\t\tselector: (row) => row.product,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Trans\",\r\n\t\t\tselector: (row) => row.transaction,\r\n\t\t\tsortable: true,\r\n\t\t\tsortFunction: actionTypeSortFunction,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{ name: \"Price\", selector: (row) => row.price, sortable: true, wrap: true },\r\n\t\t{ name: \"Time\", selector: (row) => row.time, sortable: true, wrap: true },\r\n\t\t{ name: \"obj\", selector: (row) => row.obj, omit: true },\r\n\t];\r\n\r\n\tconst handleSearch = (e) => {\r\n\t\tvar search_val = e.target.value;\r\n\t\tvar searchValueLowerCase = search_val?.toLowerCase();\r\n\t\tvar filteredData = [];\r\n\t\tfor (let value of rawUserMap.values()) {\r\n\t\t\tconst timeVal = formatStanderdTimeString(value?.time);\r\n\t\t\tif (\r\n\t\t\t\tvalue.symbol\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tvalue.transaction\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tvalue.orderid\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tvalue.product\r\n\t\t\t\t\t.toString()\r\n\t\t\t\t\t.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tvalue.price\r\n\t\t\t\t\t?.toString()\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\ttimeVal?.toLowerCase().includes(searchValueLowerCase.toLowerCase()) ||\r\n\t\t\t\tvalue.time?.toLowerCase().includes(searchValueLowerCase.toLowerCase())\r\n\t\t\t) {\r\n\t\t\t\tfilteredData.push(value);\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetTableDataFormatted(\r\n\t\t\tfilteredData.map((obj, index) => getRowFormatForBrokerTable(index, obj))\r\n\t\t);\r\n\t};\r\n\r\n\t//createDataTableTheme(); // This is for designing Datatable in dark theme\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<div className=\"table-responsive mt-3\">\r\n\t\t\t\t<Form.Group>\r\n\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\tplaceholder=\"Search\"\r\n\t\t\t\t\t\tclassName=\"mb-2 searchbox-style\"\r\n\t\t\t\t\t\tonChange={handleSearch}\r\n\t\t\t\t\t\trequired\r\n\t\t\t\t\t/>\r\n\t\t\t\t</Form.Group>\r\n\t\t\t\t<DataTable\r\n\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\tdata={tableDataFormatted}\r\n\t\t\t\t\tpagination={false}\r\n\t\t\t\t\tpaginationPerPage={10}\r\n\t\t\t\t\thighlightOnHover\r\n\t\t\t\t\ttheme=\"solarized\"\r\n\t\t\t\t/>\r\n\t\t\t</div>\r\n\t\t</>\r\n\t);\r\n};\r\n", "import React, { Component } from \"react\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport \"../userpages.css\";\r\nimport { <PERSON><PERSON>, <PERSON>b, Ta<PERSON> } from \"react-bootstrap\";\r\nimport { UsersOrderTable } from \"../../components/table/UsersOrderTable\";\r\nimport {\r\n\tGetMas,\r\n\tGetMasterBrokerInfo,\r\n\tGetMasterBrokerInfoterBrokerInfo,\r\n} from \"../../../services/backendServices\";\r\nimport { UsersPositionTable } from \"../../components/table/UsersPositionTable\";\r\nimport { UsersTradesTable } from \"../../components/table/UsersTradesTable\";\r\nimport { getSubPnlComponent } from \"../ui-helper\";\r\nimport withAuth from \"../../components/higher-order/withauth\";\r\nimport PlaceOrderModal from \"../../components/modal/PlaceOrderModel\";\r\n\r\nexport class MasterTradeBook extends Component {\r\n\tconstructor(props) {\r\n\t\tsuper(props);\r\n\t\tthis.state = {\r\n\t\t\tselectedTab: \"positions\",\r\n\t\t\tisDataLoaded: false,\r\n\t\t\tbrokerData: null,\r\n\t\t\tisLoading: false,\r\n\t\t\tplaceOrderModal: { show: false, res: {} },\r\n\t\t};\r\n\t\tthis.fetchBrokerData = this.fetchBrokerData.bind(this);\r\n\t}\r\n\r\n\thandleTabSelect = (eventKey) => {\r\n\t\tthis.setState({\r\n\t\t\tselectedTab: eventKey,\r\n\t\t});\r\n\t};\r\n\r\n\tfetchBrokerData(broker_id, user_id) {\r\n\t\tthis.setState({ isLoading: true }); // set loading state when API call is made\r\n\t\tGetMasterBrokerInfo(broker_id, user_id)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\t// console.log(res);\r\n\t\t\t\t\tthis.setState({ brokerData: res, isDataLoaded: true });\r\n\t\t\t\t\t// console.log(brokerData);\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t})\r\n\t\t\t.finally(() => {\r\n\t\t\t\tthis.setState({ isLoading: false }); // clear loading state when API response is received\r\n\t\t\t});\r\n\t}\r\n\r\n\tcomponentDidMount() {\r\n\t\tconst broker_id = this.props.match.params.user_id;\r\n\t\tconst user_id = localStorage.getItem(\"user_id\");\r\n\t\tthis.fetchBrokerData(Number(broker_id), Number(user_id));\r\n\t\t// console.log(this.state);\r\n\t}\r\n\r\n\tsetPlaceOrderModal = () => {\r\n\t\tthis.setState({ placeOrderModal: { show: false, res: {} } });\r\n\t};\r\n\r\n\thandleOpenPlaceOrderModal = () => {\r\n\t\tthis.setState({\r\n\t\t\tplaceOrderModal: { show: true },\r\n\t\t});\r\n\t};\r\n\r\n\trender() {\r\n\t\tconst { brokerData, isDataLoaded, isLoading } = this.state;\r\n\t\t{\r\n\t\t\t// console.log(brokerData || []);\r\n\t\t}\r\n\t\tconst user_id = this.props.match.params.user_id;\r\n\t\tif (!isDataLoaded) {\r\n\t\t\treturn (\r\n\t\t\t\t<h1>\r\n\t\t\t\t\tLoading Tradebook, Orderbook, Positions and P&L from your demat\r\n\t\t\t\t\taccount...\r\n\t\t\t\t</h1>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn (\r\n\t\t\t<div>\r\n\t\t\t\t<div className=\"card mb-4\">\r\n\t\t\t\t\t<div className=\"card-body py-3\">\r\n\t\t\t\t\t\t<div className=\"row align-items-center justify-content-between\">\r\n\t\t\t\t\t\t\t<div className=\"d-flex align-items-center\">\r\n\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\tclassName=\"btn btn-primary p-1\"\r\n\t\t\t\t\t\t\t\t\tonClick={() => window.close()}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{\" \"}\r\n\t\t\t\t\t\t\t\t\t<i\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"mdi mdi-arrow-left-bold m-0\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ fontSize: \"20px\" }}\r\n\t\t\t\t\t\t\t\t\t></i>{\" \"}\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t{/* <button\r\n\t\t\t\t\t\t\t\t\tclassName=\"btn btn-primary p-2 ml-3\"\r\n\t\t\t\t\t\t\t\t\tonClick={() => this.handleOpenPlaceOrderModal()}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{\" \"}\r\n\t\t\t\t\t\t\t\t\tPlace Order{\" \"}\r\n\t\t\t\t\t\t\t\t</button> */}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<h3 className=\"mb-0 order-tradebook-user-text\">\r\n\t\t\t\t\t\t\t\t{brokerData.data?.demat_name}\r\n\t\t\t\t\t\t\t</h3>\r\n\t\t\t\t\t\t\t<h3 className=\"mb-0 order-tradebook-user-text\">\r\n\t\t\t\t\t\t\t\t{brokerData.data?.margin}\r\n\t\t\t\t\t\t\t\t<sub className=\"text-muted\">Margin</sub>\r\n\t\t\t\t\t\t\t</h3>\r\n\t\t\t\t\t\t\t<h3 className=\"mb-0 order-tradebook-user-text\">\r\n\t\t\t\t\t\t\t\t{getSubPnlComponent(brokerData.data?.pnl)}\r\n\t\t\t\t\t\t\t\t<sub className=\"text-muted\">PnL</sub>\r\n\t\t\t\t\t\t\t</h3>\r\n\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\tclassName=\"btn btn-info p-1\"\r\n\t\t\t\t\t\t\t\tonClick={() => this.fetchBrokerData(user_id)}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{isLoading ? (\r\n\t\t\t\t\t\t\t\t\t<Spinner animation=\"border\" size=\"sm\" />\r\n\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t<i\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"mdi mdi-refresh m-0 text-bold\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ fontSize: \"20px\" }}\r\n\t\t\t\t\t\t\t\t\t></i>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"row customtab\">\r\n\t\t\t\t\t<div className=\"col-lg-12 grid-margin stretch-card\">\r\n\t\t\t\t\t\t<div className=\"card\">\r\n\t\t\t\t\t\t\t<div className=\"card-body\">\r\n\t\t\t\t\t\t\t\t<Tabs\r\n\t\t\t\t\t\t\t\t\tdefaultActiveKey=\"positions\"\r\n\t\t\t\t\t\t\t\t\tid=\"fill-tab-example\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"mb-3\"\r\n\t\t\t\t\t\t\t\t\tfill\r\n\t\t\t\t\t\t\t\t\tjustify\r\n\t\t\t\t\t\t\t\t\tactiveKey={this.state.selectedTab}\r\n\t\t\t\t\t\t\t\t\tonSelect={this.handleTabSelect}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Tab eventKey=\"positions\" title=\"Positions\">\r\n\t\t\t\t\t\t\t\t\t\t{brokerData && (\r\n\t\t\t\t\t\t\t\t\t\t\t<UsersPositionTable\r\n\t\t\t\t\t\t\t\t\t\t\t\tdata={brokerData.data}\r\n\t\t\t\t\t\t\t\t\t\t\t\tuser_id={user_id}\r\n\t\t\t\t\t\t\t\t\t\t\t\tfetchBrokerData={this.fetchBrokerData}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t</Tab>\r\n\t\t\t\t\t\t\t\t\t<Tab eventKey=\"orders\" title=\"Orders\">\r\n\t\t\t\t\t\t\t\t\t\t{brokerData && <UsersOrderTable data={brokerData.data} />}\r\n\t\t\t\t\t\t\t\t\t</Tab>\r\n\t\t\t\t\t\t\t\t\t<Tab eventKey=\"trades\" title=\"Trades\">\r\n\t\t\t\t\t\t\t\t\t\t{brokerData && <UsersTradesTable data={brokerData.data} />}\r\n\t\t\t\t\t\t\t\t\t</Tab>\r\n\t\t\t\t\t\t\t\t</Tabs>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t{this.state.placeOrderModal.show && (\r\n\t\t\t\t\t<PlaceOrderModal\r\n\t\t\t\t\t\tsetPlaceOrderModal={this.setPlaceOrderModal}\r\n\t\t\t\t\t\tid={this.props.match.params.user_id}\r\n\t\t\t\t\t/>\r\n\t\t\t\t)}\r\n\t\t\t</div>\r\n\t\t);\r\n\t}\r\n}\r\n\r\nexport default withAuth(MasterTradeBook);\r\n", "import React from \"react\";\r\nimport { createTheme } from \"react-data-table-component\";\r\n\r\nexport const customStylesForSelect = {\r\n\toption: (provided, state) => ({\r\n\t\t...provided,\r\n\t\tbackgroundColor: state.isSelected ? \"blue\" : \"white\", // Change the background color as desired\r\n\t\tcolor: state.isSelected ? \"white\" : \"black\", // Change the text color as desired\r\n\t}),\r\n};\r\nexport const hoverEffectOnSelect = {\r\n\toption: (base, state) => ({\r\n\t\t...customStylesForSelect.option(base, state),\r\n\t\t\"&:hover\": {\r\n\t\t\tbackgroundColor: \"lightgray\", // Change the hover background color\r\n\t\t\tcolor: \"black\", // Change the hover text color\r\n\t\t},\r\n\t}),\r\n};\r\n\r\nexport const createDataTableTheme = () => {\r\n\tcreateTheme(\r\n\t\t\"solarized\",\r\n\t\t{\r\n\t\t\tbackground: {\r\n\t\t\t\tdefault: \"transparent\",\r\n\t\t\t},\r\n\t\t\taction: {\r\n\t\t\t\tbutton: \"rgba(0,0,0,.54)\",\r\n\t\t\t\thover: \"rgba(0,0,0,.08)\",\r\n\t\t\t\tdisabled: \"rgba(0,0,0,.12)\",\r\n\t\t\t},\r\n\t\t},\r\n\t\t\"dark\"\r\n\t);\r\n};\r\n\r\nexport function getTransactionType(transaction) {\r\n\tlet a_type = transaction.charAt(0);\r\n\tlet is_buy =\r\n\t\ta_type === \"B\" ||\r\n\t\ta_type === \"b\" ||\r\n\t\ta_type === \"buy\" ||\r\n\t\ta_type === \"BUY\" ||\r\n\t\ta_type === \"Buy\";\r\n\tlet is_sell =\r\n\t\ta_type === \"S\" ||\r\n\t\ta_type === \"s\" ||\r\n\t\ta_type === \"sell\" ||\r\n\t\ta_type === \"SELL\" ||\r\n\t\ta_type === \"Sell\";\r\n\tif (is_buy) {\r\n\t\treturn <label className=\"badge badge-outline-warning\">BUY</label>;\r\n\t} else if (is_sell) {\r\n\t\treturn <label className=\"badge badge-outline-primary\">SELL</label>;\r\n\t} else {\r\n\t\treturn (\r\n\t\t\ttransaction && (\r\n\t\t\t\t<label className=\"badge badge-outline-danger\">\r\n\t\t\t\t\t{transaction.toUpperCase()}\r\n\t\t\t\t</label>\r\n\t\t\t)\r\n\t\t);\r\n\t}\r\n}\r\n\r\nexport function getPnlComponent(pnlVal) {\r\n\tpnlVal = parseFloat(pnlVal || 0).toFixed(2);\r\n\tif (pnlVal < 0) {\r\n\t\treturn (\r\n\t\t\t'<span class=\"text-danger\"> ' +\r\n\t\t\tpnlVal +\r\n\t\t\t' <i class=\"mdi mdi-arrow-down\"></i></span>'\r\n\t\t);\r\n\t} else if (pnlVal === 0 || pnlVal === null) {\r\n\t\tpnlVal = 0;\r\n\t\treturn \"<span> \" + pnlVal + \"</span>\";\r\n\t}\r\n\treturn (\r\n\t\t'<span class=\"text-success\"> ' +\r\n\t\tpnlVal +\r\n\t\t' <i class=\"mdi mdi-arrow-up\"></i></span>'\r\n\t);\r\n}\r\n\r\nexport function getSubPnlComponent(pnlVal) {\r\n\tpnlVal = parseFloat(pnlVal || 0).toFixed(2);\r\n\tif (pnlVal < 0) {\r\n\t\treturn (\r\n\t\t\t<span className=\"text-danger\">\r\n\t\t\t\t{\" \"}\r\n\t\t\t\t{pnlVal} <i className=\"mdi mdi-arrow-down\"></i>\r\n\t\t\t</span>\r\n\t\t);\r\n\t} else if (pnlVal === 0 || pnlVal === null) {\r\n\t\tpnlVal = 0;\r\n\t\treturn <span> {pnlVal}</span>;\r\n\t}\r\n\treturn (\r\n\t\t<span className=\"text-success\">\r\n\t\t\t{\" \"}\r\n\t\t\t{pnlVal} <i className=\"mdi mdi-arrow-up\"></i>\r\n\t\t</span>\r\n\t);\r\n}\r\n\r\nexport function getSubTransactionType(transaction) {\r\n\tlet a_type = transaction.charAt(0);\r\n\tlet is_buy =\r\n\t\ta_type === \"B\" ||\r\n\t\ta_type === \"b\" ||\r\n\t\ta_type === \"buy\" ||\r\n\t\ta_type === \"BUY\" ||\r\n\t\ta_type === \"Buy\";\r\n\tif (is_buy) {\r\n\t\treturn '<label className=\"badge badge-outline-warning\">BUY</label>';\r\n\t} else {\r\n\t\treturn '<label className=\"badge badge-outline-primary\">SELL</label>';\r\n\t}\r\n}\r\n\r\nexport function getOrderStatus(status, reason) {\r\n\tif (status.charAt(0) === \"C\" || status.charAt(0) === \"c\") {\r\n\t\treturn (\r\n\t\t\t<label\r\n\t\t\t\tclassName=\"badge badge-outline-success\"\r\n\t\t\t\tdata-toggle=\"tooltip\"\r\n\t\t\t\tdata-placement=\"top\"\r\n\t\t\t\ttitle={reason}\r\n\t\t\t>\r\n\t\t\t\tCOMPLETED\r\n\t\t\t</label>\r\n\t\t);\r\n\t} else if (status.charAt(0) === \"R\" || status.charAt(0) === \"r\") {\r\n\t\treturn (\r\n\t\t\t<label\r\n\t\t\t\tclassName=\"badge badge-outline-danger\"\r\n\t\t\t\tdata-toggle=\"tooltip\"\r\n\t\t\t\tdata-placement=\"top\"\r\n\t\t\t\ttitle={reason}\r\n\t\t\t>\r\n\t\t\t\tREJECTED\r\n\t\t\t</label>\r\n\t\t);\r\n\t\t//} else if (status.charAt(0) === \"O\" || status.charAt(0) === \"o\") {\r\n\t\t//   return (\r\n\t\t//     <label\r\n\t\t//       className=\"badge badge-outline-secondary\"\r\n\t\t//       data-toggle=\"tooltip\"\r\n\t\t//       data-placement=\"top\"\r\n\t\t//       title={reason}\r\n\t\t//     >\r\n\t\t//       OPEN\r\n\t\t//     </label>\r\n\t\t//   );\r\n\t} else {\r\n\t\treturn (\r\n\t\t\t<label\r\n\t\t\t\tclassName=\"badge badge-outline-info\"\r\n\t\t\t\tdata-toggle=\"tooltip\"\r\n\t\t\t\tdata-placement=\"top\"\r\n\t\t\t\ttitle={reason}\r\n\t\t\t>\r\n\t\t\t\t{status}\r\n\t\t\t</label>\r\n\t\t);\r\n\t}\r\n}\r\n\r\nexport function getConvertedAmount(amount) {\r\n\tif (isNaN(amount)) {\r\n\t\treturn \"...\";\r\n\t}\r\n\tif (amount < 1000) {\r\n\t\treturn amount.toString();\r\n\t}\r\n\tif (amount >= 1000 && amount < 100000) {\r\n\t\treturn (amount / 1000).toFixed(2) + \" K\";\r\n\t}\r\n\tif (amount >= 100000 && amount < 10000000) {\r\n\t\treturn (amount / 100000).toFixed(2) + \" L\";\r\n\t}\r\n\tif (amount >= 10000000) {\r\n\t\treturn (amount / 10000000).toFixed(2) + \" Cr\";\r\n\t}\r\n}\r\n", "import React,{useState,useEffect}from\"react\";import ReactDOM from\"react-dom\";import{string,oneOfType,node,bool,func,number,shape}from\"prop-types\";var __assign=function(){return(__assign=Object.assign||function(t){for(var n,e=1,o=arguments.length;e<o;e++)for(var i in n=arguments[e])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)};function __spreadArrays(){for(var t=0,n=0,e=arguments.length;n<e;n++)t+=arguments[n].length;var o=Array(t),i=0;for(n=0;n<e;n++)for(var a=arguments[n],r=0,c=a.length;r<c;r++,i++)o[i]=a[r];return o}var success=function(t){return React.createElement(\"svg\",__assign({viewBox:\"0 0 426.667 426.667\",width:18,height:18},t),React.createElement(\"path\",{d:\"M213.333 0C95.518 0 0 95.514 0 213.333s95.518 213.333 213.333 213.333c117.828 0 213.333-95.514 213.333-213.333S331.157 0 213.333 0zm-39.134 322.918l-93.935-93.931 31.309-31.309 62.626 62.622 140.894-140.898 31.309 31.309-172.203 172.207z\",fill:\"#6ac259\"}))},warn=function(t){return React.createElement(\"svg\",__assign({viewBox:\"0 0 310.285 310.285\",width:18,height:18},t),React.createElement(\"path\",{d:\"M264.845 45.441C235.542 16.139 196.583 0 155.142 0 113.702 0 74.743 16.139 45.44 45.441 16.138 74.743 0 113.703 0 155.144c0 41.439 16.138 80.399 45.44 109.701 29.303 29.303 68.262 45.44 109.702 45.44s80.399-16.138 109.702-45.44c29.303-29.302 45.44-68.262 45.44-109.701.001-41.441-16.137-80.401-45.439-109.703zm-132.673 3.895a12.587 12.587 0 0 1 9.119-3.873h28.04c3.482 0 6.72 1.403 9.114 3.888 2.395 2.485 3.643 5.804 3.514 9.284l-4.634 104.895c-.263 7.102-6.26 12.933-13.368 12.933H146.33c-7.112 0-13.099-5.839-13.345-12.945L128.64 58.594c-.121-3.48 1.133-6.773 3.532-9.258zm23.306 219.444c-16.266 0-28.532-12.844-28.532-29.876 0-17.223 12.122-30.211 28.196-30.211 16.602 0 28.196 12.423 28.196 30.211.001 17.591-11.456 29.876-27.86 29.876z\",fill:\"#FFDA44\"}))},loading=function(t){return React.createElement(\"div\",__assign({className:\"ct-icon-loading\"},t))},info=function(t){return React.createElement(\"svg\",__assign({viewBox:\"0 0 23.625 23.625\",width:18,height:18},t),React.createElement(\"path\",{d:\"M11.812 0C5.289 0 0 5.289 0 11.812s5.289 11.813 11.812 11.813 11.813-5.29 11.813-11.813S18.335 0 11.812 0zm2.459 18.307c-.608.24-1.092.422-1.455.548a3.838 3.838 0 0 1-1.262.189c-.736 0-1.309-.18-1.717-.539s-.611-.814-.611-1.367c0-.215.015-.435.045-.659a8.23 8.23 0 0 1 .147-.759l.761-2.688c.067-.258.125-.503.171-.731.046-.23.068-.441.068-.633 0-.342-.071-.582-.212-.717-.143-.135-.412-.201-.813-.201-.196 0-.398.029-.605.09-.205.063-.383.12-.529.176l.201-.828c.498-.203.975-.377 1.43-.521a4.225 4.225 0 0 1 1.29-.218c.731 0 1.295.178 1.692.53.395.353.594.812.594 1.376 0 .117-.014.323-.041.617a4.129 4.129 0 0 1-.152.811l-.757 2.68a7.582 7.582 0 0 0-.167.736 3.892 3.892 0 0 0-.073.626c0 .356.079.599.239.728.158.129.435.194.827.194.185 0 .392-.033.626-.097.232-.064.4-.121.506-.17l-.203.827zm-.134-10.878a1.807 1.807 0 0 1-1.275.492c-.496 0-.924-.164-1.28-.492a1.57 1.57 0 0 1-.533-1.193c0-.465.18-.865.533-1.196a1.812 1.812 0 0 1 1.28-.497c.497 0 .923.165 1.275.497.353.331.53.731.53 1.196 0 .467-.177.865-.53 1.193z\",fill:\"#006DF0\"}))},error=function(t){return React.createElement(\"svg\",__assign({viewBox:\"0 0 51.976 51.976\",width:18,height:18},t),React.createElement(\"path\",{d:\"M44.373 7.603c-10.137-10.137-26.632-10.138-36.77 0-10.138 10.138-10.137 26.632 0 36.77s26.632 10.138 36.77 0c10.137-10.138 10.137-26.633 0-36.77zm-8.132 28.638a2 2 0 0 1-2.828 0l-7.425-7.425-7.778 7.778a2 2 0 1 1-2.828-2.828l7.778-7.778-7.425-7.425a2 2 0 1 1 2.828-2.828l7.425 7.425 7.071-7.071a2 2 0 1 1 2.828 2.828l-7.071 7.071 7.425 7.425a2 2 0 0 1 0 2.828z\",fill:\"#D80027\"}))},Icons={success:success,warn:warn,loading:loading,info:info,error:error},colors={success:\"#6EC05F\",info:\"#1271EC\",warn:\"#FED953\",error:\"#D60A2E\",loading:\"#0088ff\"},Toast=function(t){var n,e,o,i,a=\"margin\"+((t.position||\"top-center\").includes(\"bottom\")?\"Bottom\":\"Top\"),r=[\"ct-toast\",t.onClick?\" ct-cursor-pointer\":\"\",\"ct-toast-\"+t.type].join(\" \"),c=((null===(e=t.bar)||void 0===e?void 0:e.size)||\"3px\")+\" \"+((null===(o=t.bar)||void 0===o?void 0:o.style)||\"solid\")+\" \"+((null===(i=t.bar)||void 0===i?void 0:i.color)||colors[t.type]),s=Icons[t.type],l=useState(((n={opacity:0})[a]=-15,n)),d=l[0],u=l[1],f=__assign({paddingLeft:t.heading?25:void 0,minHeight:t.heading?50:void 0,borderLeft:c},d),p=function(){var n;u(((n={opacity:0})[a]=\"-15px\",n)),setTimeout((function(){t.onHide(t.id,t.position)}),300)};useEffect((function(){var n,e=setTimeout((function(){var t;u(((t={opacity:1})[a]=\"15px\",t))}),50);return 0!==t.hideAfter&&(n=setTimeout((function(){p()}),1e3*t.hideAfter)),function(){clearTimeout(e),n&&clearTimeout(n)}}),[]),useEffect((function(){t.show||p()}),[t.show]);var g={tabIndex:0,onClick:t.onClick,onKeyPress:function(n){13===n.keyCode&&t.onClick(n)}};return React.createElement(\"div\",__assign({className:r,role:t.role?t.role:\"status\",style:f},t.onClick?g:{}),t.renderIcon?t.renderIcon():React.createElement(s,null),React.createElement(\"div\",{className:t.heading?\"ct-text-group-heading\":\"ct-text-group\"},t.heading&&React.createElement(\"h4\",{className:\"ct-heading\"},t.heading),React.createElement(\"div\",{className:\"ct-text\"},t.text)))};Toast.propTypes={type:string.isRequired,text:oneOfType([string,node]).isRequired,show:bool,onHide:func,id:oneOfType([string,number]),hideAfter:number,heading:string,position:string,renderIcon:func,bar:shape({}),onClick:func,role:string},Toast.defaultProps={id:void 0,show:!0,onHide:void 0,hideAfter:3,heading:void 0,position:\"top-center\",renderIcon:void 0,bar:{},onClick:void 0,role:\"status\"};var camelCase=function(t){return t.replace(/-([a-z])/g,(function(t){return t[1].toUpperCase()}))},defaultToasts={topLeft:[],topCenter:[],topRight:[],bottomLeft:[],bottomCenter:[],bottomRight:[]},ToastContainer=function(t){var n=t.toast,e=t.hiddenID,o=useState(defaultToasts),i=o[0],a=o[1];useEffect((function(){n&&a((function(t){var e,o=camelCase(n.position||\"top-center\");return __assign(__assign({},t),((e={})[o]=__spreadArrays(t[o],[n]),e))}))}),[n]);var r=function(t,n){a((function(e){var o,i=camelCase(n||\"top-center\");return __assign(__assign({},e),((o={})[i]=e[i].filter((function(n){return n.id!==t})),o))}))},c=[\"Left\",\"Center\",\"Right\"];return React.createElement(React.Fragment,null,[\"top\",\"bottom\"].map((function(t){return React.createElement(\"div\",{key:\"row_\"+t,className:\"ct-row\"},c.map((function(n){var o=\"\"+t+n,a=[\"ct-group\",\"bottom\"===t?\"ct-flex-bottom\":\"\"].join(\" \");return React.createElement(\"div\",{key:o,className:a},i[o].map((function(t){return React.createElement(Toast,__assign({key:o+\"_\"+t.id},t,{id:t.id,text:t.text,type:t.type,onClick:t.onClick,hideAfter:t.hideAfter,show:e!==t.id,onHide:r}))})))})))})))};function styleInject(t,n){void 0===n&&(n={});var e=n.insertAt;if(t&&\"undefined\"!=typeof document){var o=document.head||document.getElementsByTagName(\"head\")[0],i=document.createElement(\"style\");i.type=\"text/css\",\"top\"===e&&o.firstChild?o.insertBefore(i,o.firstChild):o.appendChild(i),i.styleSheet?i.styleSheet.cssText=t:i.appendChild(document.createTextNode(t))}}ToastContainer.propTypes={toast:shape({}),hiddenID:number},ToastContainer.defaultProps={toast:void 0,hiddenID:void 0};var css=\"#ct-container {\\n\\tposition: fixed;\\n\\twidth: 100%;\\n\\theight: 100vh;\\n\\ttop: 0px;\\n\\tleft: 0px;\\n\\tz-index: 2000;\\n\\tdisplay: flex;\\n\\tflex-direction: column;\\n\\tjustify-content: space-between;\\n\\tpointer-events: none;\\n}\\n\\n.ct-row {\\n\\tdisplay: flex;\\n\\tjustify-content: space-between;\\n}\\n\\n.ct-group {\\n\\tflex: 1;\\n\\tmargin: 10px 20px;\\n\\tdisplay: flex;\\n\\tflex-direction: column;\\n\\talign-items: center;\\n}\\n\\n.ct-group:first-child {\\n\\talign-items: flex-start;\\n}\\n\\n.ct-group:last-child {\\n\\talign-items: flex-end;\\n}\\n\\n.ct-flex-bottom {\\n\\tjustify-content: flex-end;\\n}\\n\\n.ct-toast {\\n\\tdisplay: flex;\\n\\tjustify-content: center;\\n\\talign-items: center;\\n\\tpadding: 12px 20px;\\n\\tbackground-color: #fff;\\n\\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n\\tcolor: #000;\\n\\tborder-radius: 4px;\\n\\tmargin: 0px;\\n\\topacity: 1;\\n\\ttransition: 0.3s all ease-in-out;\\n\\tmin-height: 45px;\\n\\tpointer-events: all;\\n}\\n\\n.ct-toast:focus {\\n\\toutline: none;\\n}\\n\\n.ct-toast svg {\\n\\tmin-width: 18px;\\n}\\n\\n.ct-cursor-pointer {\\n\\tcursor: pointer;\\n}\\n\\n.ct-icon-loading {\\n\\tdisplay: inline-block;\\n\\twidth: 20px;\\n\\theight: 20px;\\n}\\n\\n.ct-icon-loading:after {\\n\\tcontent: ' ';\\n\\tdisplay: block;\\n\\twidth: 14px;\\n\\theight: 14px;\\n\\tmargin: 1px;\\n\\tborder-radius: 50%;\\n\\tborder: 2px solid #0088ff;\\n\\tborder-color: #0088ff transparent #0088ff transparent;\\n\\tanimation: ct-icon-loading 1.2s linear infinite;\\n}\\n\\n@keyframes ct-icon-loading {\\n\\t0% {\\n\\t\\ttransform: rotate(0deg);\\n\\t}\\n\\t100% {\\n\\t\\ttransform: rotate(360deg);\\n\\t}\\n}\\n\\n.ct-text-group {\\n\\tmargin-left: 15px;\\n}\\n\\n.ct-text-group-heading {\\n\\tmargin-left: 25px;\\n}\\n\\n.ct-heading {\\n\\tfont-size: 18px;\\n\\tmargin: 0px;\\n\\tmargin-bottom: 5px;\\n}\\n\\n.ct-text {\\n\\tfont-size: 14px;\\n}\\n\\n@media (max-width: 768px) {\\n\\t.ct-row {\\n\\t\\tjustify-content: flex-start;\\n\\t\\tflex-direction: column;\\n\\t\\tmargin: 7px 0px;\\n\\t}\\n\\n\\t.ct-group {\\n\\t\\tflex: none;\\n\\t\\tmargin: 0px;\\n\\t}\\n\\n\\t.ct-toast {\\n\\t\\tmargin: 8px 15px;\\n\\t\\twidth: initial;\\n\\t}\\n}\\n\";styleInject(css);var ctToastCount=0,cogoToast=function(t,n){var e,o,i=document.getElementById((null===(e=n)||void 0===e?void 0:e.toastContainerID)||\"ct-container\");i||((i=document.createElement(\"div\")).id=\"ct-container\",document.body.appendChild(i)),ctToastCount+=1;var a=1e3*(void 0===(null===(o=n)||void 0===o?void 0:o.hideAfter)?3:n.hideAfter),r=__assign({id:ctToastCount,text:t},n);ReactDOM.render(React.createElement(ToastContainer,{toast:r}),i);var c=new Promise((function(t){setTimeout((function(){t()}),a)}));return c.hide=function(){ReactDOM.render(React.createElement(ToastContainer,{hiddenID:r.id}),i)},c};cogoToast.success=function(t,n){return cogoToast(t,__assign(__assign({},n),{type:\"success\"}))},cogoToast.warn=function(t,n){return cogoToast(t,__assign(__assign({},n),{type:\"warn\"}))},cogoToast.info=function(t,n){return cogoToast(t,__assign(__assign({},n),{type:\"info\"}))},cogoToast.error=function(t,n){return cogoToast(t,__assign(__assign({},n),{type:\"error\"}))},cogoToast.loading=function(t,n){return cogoToast(t,__assign(__assign({},n),{type:\"loading\"}))};export default cogoToast;export{Toast};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nvar _excluded = [\"bsPrefix\", \"variant\", \"animation\", \"size\", \"children\", \"as\", \"className\"];\nimport classNames from 'classnames';\nimport React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nvar Spinner = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var bsPrefix = _ref.bsPrefix,\n      variant = _ref.variant,\n      animation = _ref.animation,\n      size = _ref.size,\n      children = _ref.children,\n      _ref$as = _ref.as,\n      Component = _ref$as === void 0 ? 'div' : _ref$as,\n      className = _ref.className,\n      props = _objectWithoutPropertiesLoose(_ref, _excluded);\n\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'spinner');\n  var bsSpinnerPrefix = bsPrefix + \"-\" + animation;\n  return /*#__PURE__*/React.createElement(Component, _extends({\n    ref: ref\n  }, props, {\n    className: classNames(className, bsSpinnerPrefix, size && bsSpinnerPrefix + \"-\" + size, variant && \"text-\" + variant)\n  }), children);\n});\nSpinner.displayName = 'Spinner';\nexport default Spinner;"], "names": ["formatStanderdTimeString", "inputTime", "inputDate", "Date", "isNaN", "getTime", "year", "getFullYear", "month", "getMonth", "toString", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "concat", "WrappedComponent", "props", "history", "useHistory", "useEffect", "token", "localStorage", "getItem", "hostname", "window", "location", "login_url", "requestOptions", "method", "headers", "body", "JSON", "stringify", "fetch", "API_URL", "then", "response", "json", "data", "code", "href", "catch", "error", "console", "_jsx", "PlaceOrderModal", "showModal", "setShowModal", "useState", "onBtnClose", "setPlaceOrderModal", "show", "apiResponseModal", "setApiResponseModal", "res", "qty", "set<PERSON><PERSON>", "selectedOption", "setSelectedOption", "value", "label", "selectedOptionSide", "setSelectedOptionSide", "selectedTypeOption", "setSelectedTypeOption", "selectedProductOption", "setSelectedProductOption", "script", "setScript", "selectedDate", "setSelectedDate", "strikPrice", "setStrikPrice", "confirmModel", "setConfirmModel", "placeOrderApiCall", "useMutation", "params", "PlaceOrderApi", "status", "cogoToast", "success", "msg", "e", "log", "_jsxs", "Modal", "animation", "size", "className", "children", "Header", "Title", "Body", "Form", "Group", "Label", "Select", "options", "onChange", "styles", "mergeStyles", "hoverEffectOnSelect", "customStylesForSelect", "Control", "type", "placeholder", "_e$target$value", "target", "toUpperCase", "required", "DatePicker", "selected", "date", "dateFormat", "_Fragment", "<PERSON><PERSON>", "onClick", "handleClearButton", "preventDefault", "style", "backgroundColor", "width", "margin", "borderCollapse", "color", "format", "Footer", "variant", "values", "order_type", "script_name", "strategy_id", "Number", "id", "quantity", "transaction", "product", "expiry_date", "strike_price", "option_type", "mutate", "ResponseModal", "UsersOrderTable", "user_id", "useParams", "tableDataFormatted", "setTableDataFormatted", "rawUserMap", "useMemo", "Map", "showDeletePopup", "setShowDeletePopup", "obj", "ind", "executenowPopup", "setExecutenowPopup", "getRowFormatForBrokerTable", "index", "symbol", "getTransactionType", "ordertype", "price", "time", "orderid", "is_open", "getOrderStatus", "reason", "_props$data", "tableData", "rowTableData", "map", "push", "setTableData", "orders", "set", "columns", "name", "selector", "row", "sortable", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "wrap", "sortFunction", "actionTypeSortFunction", "a", "b", "omit", "CancelOrderApiCall", "CancelOrderApi", "delete", "prevData", "updatedData", "splice", "Promise", "reject", "ExecuteOrderNowApiCall", "ExecuteOrderNow", "search_val", "searchValueLowerCase", "toLowerCase", "filteredData", "_statusVal$props", "_value$symbol", "_value$transaction", "_value$product", "_value$ordertype", "_value$quantity", "_value$quantity$toStr", "_value$price", "_value$price$toString", "_value$orderid", "_value$orderid$toStri", "_value$time", "timeVal", "statusVal", "formatedStatusVal", "includes", "DataTable", "pagination", "paginationPerPage", "highlightOnHover", "theme", "UsersPositionTable", "showSquareOffPopup", "setShowSquareOffPopup", "squareOffMutation", "SquareOff", "fetchBrokerData", "tradingsymbol", "producttype", "pnl", "getSubPnlComponent", "ltp", "avgprice", "action", "squareoff", "tradingSymbol", "disabled", "is_all", "positions", "pnlSortFunction", "_value$pnl", "_value$pnl$toString", "_value$ltp", "_value$ltp$toString", "_value$avgprice", "_value$avgprice$toStr", "SquareOffThisPosition", "UsersTradesTable", "trades", "MasterTradeBook", "Component", "constructor", "super", "handleTabSelect", "eventKey", "this", "setState", "selectedTab", "placeOrderModal", "handleOpenPlaceOrderModal", "state", "isDataLoaded", "brokerData", "isLoading", "bind", "broker_id", "GetMasterBrokerInfo", "finally", "componentDidMount", "match", "render", "_brokerData$data", "_brokerData$data2", "_brokerData$data3", "close", "fontSize", "demat_name", "Spinner", "Tabs", "defaultActiveKey", "fill", "justify", "active<PERSON><PERSON>", "onSelect", "Tab", "title", "<PERSON><PERSON><PERSON>", "option", "provided", "isSelected", "base", "a_type", "char<PERSON>t", "is_sell", "pnlVal", "parseFloat", "toFixed", "getConvertedAmount", "amount", "__assign", "Object", "assign", "t", "n", "o", "arguments", "length", "i", "prototype", "hasOwnProperty", "call", "apply", "Icons", "React", "viewBox", "height", "d", "warn", "loading", "info", "colors", "Toast", "position", "r", "join", "c", "bar", "s", "l", "opacity", "u", "f", "paddingLeft", "heading", "minHeight", "borderLeft", "p", "setTimeout", "onHide", "hideAfter", "clearTimeout", "g", "tabIndex", "onKeyPress", "keyCode", "role", "renderIcon", "text", "propTypes", "string", "isRequired", "oneOfType", "node", "bool", "func", "number", "shape", "defaultProps", "camelCase", "replace", "defaultToasts", "topLeft", "topCenter", "topRight", "bottomLeft", "bottomCenter", "bottomRight", "ToastContainer", "toast", "hiddenID", "Array", "__spreadA<PERSON>ys", "filter", "key", "insertAt", "document", "head", "getElementsByTagName", "createElement", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "append<PERSON><PERSON><PERSON>", "styleSheet", "cssText", "createTextNode", "styleInject", "ctToastCount", "getElementById", "toastContainerID", "ReactDOM", "hide", "_excluded", "_ref", "ref", "bsPrefix", "_ref$as", "as", "_objectWithoutPropertiesLoose", "bsSpinnerPrefix", "useBootstrapPrefix", "_extends", "classNames", "displayName"], "sourceRoot": ""}
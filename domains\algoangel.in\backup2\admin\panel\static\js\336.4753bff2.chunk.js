"use strict";(self.webpackChunkadminpanel=self.webpackChunkadminpanel||[]).push([[336],{7455:(e,t,a)=>{a.a(e,(async(e,n)=>{try{a.d(t,{Z:()=>c});var s=a(2791),o=a(4880),i=a(7971),r=a(184),l=e([i]);i=(l.then?(await l)():l)[0];const c=e=>t=>{const a=(0,o.k6)();return(0,s.useEffect)((()=>{const e=localStorage.getItem("admin_access_token"),{hostname:t}=window.location,a="https://"+t+"/admin/";if(e){const t={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:e})};fetch(i.T5+"/auth/verify",t).then((e=>e.json())).then((e=>{"token_not_valid"===e.code&&(window.location.href=a)})).catch((e=>{console.error("Error:",e),window.location.href=a}))}else window.location.href=a}),[a]),(0,r.jsx)(e,{...t})};n()}catch(c){n(c)}}))},4336:(e,t,a)=>{a.a(e,(async(e,n)=>{try{a.r(t),a.d(t,{default:()=>j});var s=a(2791),o=(a(4129),a(7455)),i=a(4912),r=a(3360),l=a(9513),c=a.n(l),d=(a(8639),a(1951)),m=a(7691),h=a(1025),u=a(4970),p=a(3513),b=a(4849),g=a(184),x=e([o,m]);function v(){const[e,t]=(0,s.useState)({show:!1,res:{}}),[a,n]=(0,s.useState)(new Date),[o,l]=(0,s.useState)(),[x,y]=(0,s.useState)(!1),[v,j]=(0,s.useState)([]),w=(0,s.useMemo)((()=>new Map),[]);function f(e){var t=[];e.map(((e,a)=>t.push(N(a,e)))),j(t)}function N(e,t){return{index:e+1,ref_name:t.ref_name,strategy_name:t.name,trading_symbol:t.trading_symbol,exchange:t.exchange,quantity:t.quantity,price:t.price,action_type:(0,u.le)(t.action_type),order_time:t.order_time.split(".")[0].split("T")[1],view:(0,g.jsx)("button",{type:"button",className:"btn btn-outline-primary btn-icon-text btnShowChildOrders",onClick:()=>window.open("/admin/panel/orders/".concat(t.id),"_blank"),children:"View"}),obj:t}}(0,s.useEffect)((()=>{(0,m.KV)(-1,(0,d.default)(new Date,"MM-dd-yyyy")).then((e=>{l(e.data);const t=Object.values(e.data);f(t),t.map(((e,t)=>w.set(e.id,e))),y(!0)})).catch((e=>{console.log("error : ",e),y(!0)}))}),[]);const S=[{name:"Id",selector:e=>e.index,sortable:!0,maxWidth:"65px",minWidth:"65px"},{name:"Strategy",selector:e=>e.strategy_name,wrap:!0,sortable:!0},{name:"Script",selector:e=>e.trading_symbol,sortable:!0,wrap:!0},{name:"Exchange",selector:e=>e.exchange,sortable:!0},{name:"Quantity",selector:e=>e.quantity,sortable:!0},{name:"Price",selector:e=>e.price,sortable:!0},{name:"Action",selector:e=>e.action_type,sortable:!0,sortFunction:(e,t)=>"B"===e.obj.action_type||"b"===e.obj.action_type||"buy"===e.obj.action_type||"BUY"===e.obj.action_type||"Buy"===e.obj.action_type?1:-1},{name:"Time",selector:e=>e.order_time,sortable:!0},{name:"View",selector:e=>e.view},{name:"obj",selector:e=>e.obj,omit:!0}];return(0,g.jsxs)("div",{children:[(0,g.jsx)("div",{className:"page-header",children:(0,g.jsx)("h3",{className:"page-title",children:"Orders"})}),(0,g.jsx)("div",{className:"card",children:(0,g.jsx)("div",{className:"card-body",children:(0,g.jsxs)("div",{className:"row",children:[(0,g.jsx)("div",{className:"col-md-3",children:(0,g.jsx)(i.Z,{children:(0,g.jsx)(i.Z.Group,{controlId:"datePicker",className:"mb-0",children:(0,g.jsx)(c(),{selected:a,onChange:e=>{n(e)},className:"form-control",dateFormat:"dd-MM-yyyy"})})})}),(0,g.jsx)("div",{className:"col-md-2 py-1 rounded",children:(0,g.jsx)(r.Z,{type:"btn",className:"btn btn-md btn-primary",onClick:()=>{(0,m.KV)(-1,(0,d.default)(a,"MM-dd-yyyy")).then((e=>{l(e.data);const t=Object.values(e.data);f(t),t.map(((e,t)=>w.set(e.id,e)))})).catch((e=>{console.log("error : ",e)}))},children:"Show Orders"})}),(0,g.jsx)("div",{className:"col-md-4"})]})})}),(0,g.jsx)("div",{className:"row customtab mt-3",children:(0,g.jsx)("div",{className:"col-lg-12 grid-margin stretch-card",children:(0,g.jsx)("div",{className:"card",children:(0,g.jsx)("div",{className:"card-body",children:x?(0,g.jsxs)("div",{className:"table-responsive",children:[(0,g.jsx)(i.Z.Group,{children:(0,g.jsx)(i.Z.Control,{type:"text",placeholder:"Search",className:"mb-2 searchbox-style",onChange:e=>{var t=e.target.value,a=[];for(let d of w.values()){var n,s,o,i,r,l,c;const e=(0,u.le)(d.action_type).props.children;(null!==(n=d.ref_name)&&void 0!==n&&n.toLowerCase().includes(null===t||void 0===t?void 0:t.toLowerCase())||null!==(s=d.trading_symbol)&&void 0!==s&&s.toLowerCase().includes(null===t||void 0===t?void 0:t.toLowerCase())||null!==(o=d.exchange)&&void 0!==o&&o.toLowerCase().includes(null===t||void 0===t?void 0:t.toLowerCase())||d.price.toString().includes(t)||null!==(i=d.quantity)&&void 0!==i&&i.toString().includes(t)||null!==(r=d.name.toString())&&void 0!==r&&r.toLowerCase().includes(null===t||void 0===t?void 0:t.toLowerCase())||null!==(l=e.toString())&&void 0!==l&&l.toLowerCase().includes(null===t||void 0===t?void 0:t.toLowerCase())||null!==(c=d.order_time)&&void 0!==c&&c.split(".")[0].split("T")[1].toString().includes(t))&&a.push(d)}j(a.map(((e,t)=>N(t,e))))},required:!0})}),(0,g.jsx)(p.ZP,{columns:S,data:v,pagination:!0,paginationPerPage:10,striped:!0,highlightOnHover:!0,noHeader:!0})]}):(0,g.jsx)("div",{className:"d-flex justify-content-center align-items-center",style:{height:"100%"},children:(0,g.jsx)(b.Z,{animation:"border",style:{width:"5rem",height:"5rem"}})})})})})}),e.show&&(0,g.jsx)(h.Z,{res:e.res,setApiResponseModal:t,msg:e.msg}),e.show&&(0,g.jsx)(h.Z,{res:e.res,setApiResponseModal:t,msg:e.res.msg})]})}[o,m]=x.then?(await x)():x;const j=(0,o.Z)(v);n()}catch(y){n(y)}}))},4970:(e,t,a)=>{a.d(t,{Gr:()=>c,SX:()=>l,UO:()=>s,b4:()=>o,le:()=>i,nZ:()=>r});a(2791),a(3513);var n=a(184);const s={option:(e,t)=>({...e,backgroundColor:t.isSelected?"blue":"white",color:t.isSelected?"white":"black"})},o={option:(e,t)=>({...s.option(e,t),"&:hover":{backgroundColor:"lightgray",color:"black"}})};function i(e){let t=e.charAt(0),a="S"===t||"s"===t||"sell"===t||"SELL"===t||"Sell"===t;return"B"===t||"b"===t||"buy"===t||"BUY"===t||"Buy"===t?(0,n.jsx)("label",{className:"badge badge-outline-warning",children:"BUY"}):a?(0,n.jsx)("label",{className:"badge badge-outline-primary",children:"SELL"}):e&&(0,n.jsx)("label",{className:"badge badge-outline-danger",children:e.toUpperCase()})}function r(e){return(e=parseFloat(e||0).toFixed(2))<0?(0,n.jsxs)("span",{className:"text-danger",children:[" ",e," ",(0,n.jsx)("i",{className:"mdi mdi-arrow-down"})]}):0===e||null===e?(e=0,(0,n.jsxs)("span",{children:[" ",e]})):(0,n.jsxs)("span",{className:"text-success",children:[" ",e," ",(0,n.jsx)("i",{className:"mdi mdi-arrow-up"})]})}function l(e,t){return"C"===e.charAt(0)||"c"===e.charAt(0)?(0,n.jsx)("label",{className:"badge badge-outline-success","data-toggle":"tooltip","data-placement":"top",title:t,children:"COMPLETED"}):"R"===e.charAt(0)||"r"===e.charAt(0)?(0,n.jsx)("label",{className:"badge badge-outline-danger","data-toggle":"tooltip","data-placement":"top",title:t,children:"REJECTED"}):(0,n.jsx)("label",{className:"badge badge-outline-info","data-toggle":"tooltip","data-placement":"top",title:t,children:e})}function c(e){return isNaN(e)?"...":e<1e3?e.toString():e>=1e3&&e<1e5?(e/1e3).toFixed(2)+" K":e>=1e5&&e<1e7?(e/1e5).toFixed(2)+" L":e>=1e7?(e/1e7).toFixed(2)+" Cr":void 0}},4849:(e,t,a)=>{a.d(t,{Z:()=>m});var n=a(7462),s=a(3366),o=a(1694),i=a.n(o),r=a(2791),l=a(162),c=["bsPrefix","variant","animation","size","children","as","className"],d=r.forwardRef((function(e,t){var a=e.bsPrefix,o=e.variant,d=e.animation,m=e.size,h=e.children,u=e.as,p=void 0===u?"div":u,b=e.className,g=(0,s.Z)(e,c),x=(a=(0,l.vE)(a,"spinner"))+"-"+d;return r.createElement(p,(0,n.Z)({ref:t},g,{className:i()(b,x,m&&x+"-"+m,o&&"text-"+o)}),h)}));d.displayName="Spinner";const m=d},4129:()=>{}}]);
//# sourceMappingURL=336.4753bff2.chunk.js.map
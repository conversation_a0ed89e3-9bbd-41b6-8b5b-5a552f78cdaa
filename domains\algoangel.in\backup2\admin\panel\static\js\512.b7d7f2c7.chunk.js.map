{"version": 3, "file": "static/js/512.b7d7f2c7.chunk.js", "mappings": "uSAYA,SAASA,EAAoBC,GAC5BC,QAAQC,IAAI,OAAQF,GACpB,MAAMG,GAAcC,EAAAA,EAAAA,mBACbC,EAAkBC,IAAuBC,EAAAA,EAAAA,UAAS,CACxDC,MAAM,EACNC,IAAK,CAAC,KAEAC,EAAiBC,IAAsBJ,EAAAA,EAAAA,WAAS,IAChDK,EAAoBC,IAAyBN,EAAAA,EAAAA,UAAS,IACvDO,GAAaC,EAAAA,EAAAA,UAAQ,IAAM,IAAIC,KAAO,IAwB5C,SAASC,EAAaC,GACrB,IAAIC,EAAe,GACV,OAATD,QAAS,IAATA,GAAAA,EAAWE,KAAI,CAACC,EAAKC,IACbH,EAAaI,KAAKC,EAAqBF,EAAOD,MAEtDR,EAAsBM,EACvB,CAEA,SAASK,EAAqBC,EAAKJ,GAClC,MAAO,CACNC,MAAOG,EAAM,EACbC,eAAgBL,EAAIK,eACpBC,SAAUN,EAAIM,SACdC,SAAUP,EAAIO,SACdC,MAAOR,EAAIQ,MACXC,aAAaC,EAAAA,EAAAA,IAAmBV,EAAIS,aACpCT,IAAKA,EAEP,EAvCAW,EAAAA,EAAAA,UAAS,iBAAiB,KACzBC,EAAAA,EAAAA,IAAcjC,EAAMkC,IAAIC,EAAAA,EAAAA,SAAO,IAAIC,KAAQ,eACzCC,MAAM5B,IACN,GAAIA,EAAI6B,OAAQ,CACf,MAAMC,EAAUC,OAAOC,OAAOhC,EAAIiC,MAMlC,OALAzB,EAAasB,GAEN,OAAPA,QAAO,IAAPA,GAAAA,EAASnB,KAAI,CAACC,EAAKC,IACXR,EAAW6B,IAAItB,EAAIa,GAAIb,KAExBZ,CACR,CACC,OAAO,CACR,IAEAmC,OAAOC,IACP5C,QAAQC,IAAI,WAAY2C,GACxBvC,EAAoB,CAAEE,MAAM,EAAMC,IAAKoC,GAAI,MAwB9C,MAcMC,EAAU,CACf,CACCC,KAAM,KACNC,SAAWC,GAAQA,EAAI3B,MACvB4B,UAAU,EACVC,SAAU,OACVC,SAAU,QAEX,CACCL,KAAM,iBACNC,SAAWC,GAAQA,EAAIvB,eACvB2B,MAAM,EACNH,UAAU,GAEX,CACCH,KAAM,WACNC,SAAWC,GAAQA,EAAItB,SACvBuB,UAAU,EACVG,MAAM,GAIP,CACCN,KAAM,MACNC,SAAWC,GAAQA,EAAIrB,SACvBsB,UAAU,GAIX,CACCH,KAAM,QACNC,SAAWC,GAAQA,EAAIpB,MACvBqB,UAAU,GAIX,CACCH,KAAM,OACNC,SAAWC,GAAQA,EAAInB,YACvBoB,UAAU,EACVI,aAtD6BC,CAACC,EAAGC,IAEX,MAAtBD,EAAEnC,IAAIS,aACgB,MAAtB0B,EAAEnC,IAAIS,aACgB,QAAtB0B,EAAEnC,IAAIS,aACgB,QAAtB0B,EAAEnC,IAAIS,aACgB,QAAtB0B,EAAEnC,IAAIS,YAEC,GAEC,GAgDT,CAAEiB,KAAM,MAAOC,SAAWC,GAAQA,EAAI5B,IAAKqC,MAAM,KAwBlDC,EAAAA,EAAAA,YAAU,KACLjD,IACHP,EAAYyD,eAAe,oBAC3BjD,GAAmB,GACpB,GACE,CAACD,KAEJiD,EAAAA,EAAAA,YAAU,KACL3D,EAAM6D,0BACT1D,EAAYyD,eAAe,oBAC3B5D,EAAM8D,4BAA2B,GAClC,GACE,CAAC9D,EAAM6D,0BACV,MAGOE,EAAcC,IAAmBzD,EAAAA,EAAAA,UAAS,IAAI6B,OA0B/C,YAAE6B,IAAgBC,EAAAA,EAAAA,KACxB,OACCC,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,EACCF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,OAAMD,SAAA,EACpBE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,WAAUD,UACxBE,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAAAH,UACJE,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAACC,UAAU,aAAaJ,UAAS,GAAAK,OAAKV,GAAe,kBAAiB,SAAQI,UAExFE,EAAAA,EAAAA,KAACK,IAAU,CACVC,SAAUd,EACVe,SAvCmBC,IACzBf,EAAgBe,EAAK,EAuCfT,UAAU,eACVU,WAAW,sBAKfT,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wBAAuBD,UACrCE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CACNC,KAAK,MACLZ,UAAU,yBACVa,QA7CmBC,MAGvBnD,EAAAA,EAAAA,IAAcjC,EAAMkC,IAAIC,EAAAA,EAAAA,SAAO4B,EAAc,eAC3C1B,MAAM5B,IACN,GAAIA,EAAI6B,OAAQ,CACf,MAAMC,EAAUC,OAAOC,OAAOhC,EAAIiC,MAMlC,OALAzB,EAAasB,GAEN,OAAPA,QAAO,IAAPA,GAAAA,EAASnB,KAAI,CAACC,EAAKC,IACXR,EAAW6B,IAAItB,EAAIa,GAAIb,KAExBZ,CACR,CACC,OAAO,CACR,IAEAmC,OAAOC,IACP5C,QAAQC,IAAI,WAAY2C,GACxBvC,EAAoB,CAAEE,MAAM,EAAMC,IAAKoC,GAAI,GAC1C,EAyB0BwB,SACzB,mBAIFE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,cACfC,EAAAA,EAAAA,KAAA,OAAKD,UAAU,WAAUD,UACxBE,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAAAJ,UACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKa,QAAO,CACZH,KAAK,OACLI,YAAY,SACZhB,UAAS,GAAAK,OAAKV,GAAe,oBAAmB,wBAChDa,SAhGgBjC,IACrB,IAAI0C,EAAa1C,EAAE2C,OAAOC,MACtBC,EAAe,GACnB,IAAK,IAAID,KAAS3E,EAAW2B,SAAU,CAAC,IAADkD,EAAAC,EAAAC,GAEjB,QAApBF,EAAAF,EAAM/D,sBAAc,IAAAiE,GAApBA,EACGG,cACDC,SAAmB,OAAVR,QAAU,IAAVA,OAAU,EAAVA,EAAYO,gBACT,QADuBF,EACrCH,EAAM9D,gBAAQ,IAAAiE,GAAdA,EAAgBE,cAAcC,SAAmB,OAAVR,QAAU,IAAVA,OAAU,EAAVA,EAAYO,gBACnDL,EAAM7D,SAASoE,WAAWD,SAAmB,OAAVR,QAAU,IAAVA,OAAU,EAAVA,EAAYO,gBAC9B,QAD4CD,EAC7DJ,EAAM3D,mBAAW,IAAA+D,GAAjBA,EAAmBC,cAAcC,SAAmB,OAAVR,QAAU,IAAVA,OAAU,EAAVA,EAAYO,gBACtDL,EAAM5D,MAAMmE,WAAWD,SAASR,KAEhCG,EAAanE,KAAKkE,EAEpB,CACA5E,EACC6E,EAAatE,KAAI,CAACC,EAAKC,IAAUE,EAAqBF,EAAOD,KAC7D,EA+EI4E,UAAQ,YAKZ9B,EAAAA,EAAAA,MAAA,OAAKG,UAAU,wBAAuBD,SAAA,EACrCE,EAAAA,EAAAA,KAAC2B,EAAAA,GAAS,CACTpD,QAASA,EACTJ,KAAM9B,EACNuF,YAAU,EACVC,kBAAmB,GACnBC,SAAUpC,EACVqC,kBAAgB,EAChBC,OAAOC,EAAAA,EAAAA,MACPC,UAAQ,IAERpG,EAAiBG,OACjB+D,EAAAA,EAAAA,KAACmC,EAAAA,EAAa,CACbjG,IAAKJ,EAAiBI,IACtBH,oBAAqBA,EACrBqG,IAAKtG,EAAiBI,WAM5B,C,4BAEA,U,mNC5OA,SAASmG,EAAoB5G,GAC5B,MAAMG,GAAcC,EAAAA,EAAAA,mBACbC,EAAkBC,IAAuBC,EAAAA,EAAAA,UAAS,CACxDC,MAAM,EACNC,IAAK,CAAC,KAEAoG,EAA2BC,IAAgCvG,EAAAA,EAAAA,UAAS,CAC1E+B,QAAQ,EACRJ,GAAI,MAEE6E,EAAyBC,IAA8BzG,EAAAA,EAAAA,UAAS,CACtE+B,QAAQ,EACRJ,GAAI,GACJ6C,KAAM,GACNkC,IAAK,KAECC,EAAiBC,IAAsB5G,EAAAA,EAAAA,WAAS,IAChDK,EAAoBC,IAAyBN,EAAAA,EAAAA,UAAS,IACvDO,GAAaC,EAAAA,EAAAA,UAAQ,IAAM,IAAIC,KAAO,IAgC5C,SAASQ,EAAqBC,EAAKJ,GAClC,MAAO,CACNC,MAAOG,EAAM,EACb2F,YAAa/F,EAAI0D,KAAKsC,MAAM,EAAG,IAC/BJ,KAAKK,EAAAA,EAAAA,IAAmBjG,EAAI4F,KAC5BM,QACCpD,EAAAA,EAAAA,MAAA,OAAKG,UAAU,2BAA0BD,SAAA,EACxCE,EAAAA,EAAAA,KAAA,UACCW,KAAK,SACLZ,UAAU,6EACVa,QAASA,KACR6B,EAA2B,CAC1B1E,QAAQ,EACRJ,GAAIb,EAAIa,GACR6C,KAAM1D,EAAI0D,KAAKsC,MAAM,EAAG,IACxBJ,IAAK5F,EAAI4F,KACR,EACD5C,UAEFE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,+CAEdC,EAAAA,EAAAA,KAAA,UACCW,KAAK,SACLZ,UAAU,wFACVa,QAASA,KACR2B,EAA6B,CAAExE,QAAQ,EAAMJ,GAAIb,EAAIa,IAAK,EACzDmC,UAEFE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,uDAIhBjD,IAAKA,EAEP,EA/DAW,EAAAA,EAAAA,UAAS,qBAAqB,KAC7BwF,EAAAA,EAAAA,IAAkBxH,EAAMkC,IACtBG,MAAM5B,IACN,GAAIA,EAAI6B,OAAQ,CACf,MAAMC,EAAUC,OAAOC,OAAOhC,EAAIiC,MAMlC,OAWJ,SAAsBxB,GACrB,IAAIC,EAAe,GACnBD,EAAUE,KAAI,CAACC,EAAKC,IACZH,EAAaI,KAAKC,EAAqBF,EAAOD,MAEtDR,EAAsBM,EACvB,CAtBIF,CAAasB,GAEbA,EAAQnB,KAAI,CAACC,EAAKC,IACVR,EAAW6B,IAAIrB,EAAOD,KAEvBZ,CACR,CACC,OAAO,CACR,IAEAmC,OAAOC,IACP5C,QAAQC,IAAI,WAAY2C,GACxBvC,EAAoB,CAAEE,MAAM,EAAMC,IAAKoC,GAAI,MAgD9C,MAIMC,EAAU,CACf,CACCC,KAAM,KACNC,SAAWC,GAAQA,EAAI3B,MACvB4B,UAAU,EACVC,SAAU,OACVC,SAAU,QAEX,CACCL,KAAM,cACNC,SAAWC,GAAQA,EAAImE,YACvB/D,MAAM,EACNH,UAAU,GAEX,CACCH,KAAM,MACNC,SAAWC,GAAQA,EAAIgE,IACvB/D,UAAU,EACVG,MAAM,EACNC,aAvBsBmE,CAACjE,EAAGC,IACpBD,EAAEnC,IAAI4F,IAAMxD,EAAEpC,IAAI4F,KAAO,EAAI,GAwBpC,CAAElE,KAAM,SAAUC,SAAWC,GAAQA,EAAIsE,QACzC,CAAExE,KAAM,MAAOC,SAAWC,GAAQA,EAAI5B,IAAKqC,MAAM,IAuB5CgE,GAAiBC,EAAAA,EAAAA,aAAY,wBAAyBlF,IAC3DmF,EAAAA,EAAAA,IAAqBnF,GACnBJ,MAAM5B,GACFA,EAAI6B,QACP6E,GAAmB,GACnBL,EAA6B,CAAExE,QAAQ,EAAOJ,GAAI,KAClD2F,EAAAA,EAAUC,QAAQ,GAADnD,OAAIlE,EAAIkG,MAClBlG,IAEPH,EAAoB,CAAEE,MAAM,EAAMC,IAAKA,KAChC,KAGRmC,OAAOC,IACP5C,QAAQC,IAAI,WAAY2C,GACxBvC,EAAoB,CAAEE,MAAM,EAAMC,IAAKoC,GAAI,MAU9C,MAAMkF,GAAeJ,EAAAA,EAAAA,aAAY,4BAA6BlF,IAC7DuF,EAAAA,EAAAA,IAAyBvF,GACvBJ,MAAM5B,GACFA,EAAI6B,QACP6E,GAAmB,GACnBH,EAA2B,CAC1B1E,QAAQ,EACRJ,GAAI,GACJ6C,KAAM,GACNkC,IAAK,IAEN3G,EAAoB,CAAEE,MAAM,EAAMC,IAAKA,IAChCA,IAEPH,EAAoB,CAAEE,MAAM,EAAMC,IAAKA,KAChC,KAGRmC,OAAOC,IACP5C,QAAQC,IAAI,WAAY2C,GACxBvC,EAAoB,CAAEE,MAAM,EAAMC,IAAKoC,GAAI,OAe9Cc,EAAAA,EAAAA,YAAU,KACLuD,IACH/G,EAAYyD,eAAe,qBAC3BuD,GAAmB,GACpB,GACE,CAACD,KAEJvD,EAAAA,EAAAA,YAAU,KACL3D,EAAMiI,0BACT9H,EAAYyD,eAAe,qBAC3B5D,EAAMkI,4BAA2B,GAClC,GACE,CAAClI,EAAMiI,0BAGV,MAAM,YAAEhE,IAAgBC,EAAAA,EAAAA,KACxB,OACCK,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAC,UACCF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,wBAAuBD,SAAA,EACrCE,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAAAJ,UACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKa,QAAO,CACZH,KAAK,OACLI,YAAY,SACZhB,UAAS,GAAAK,OAAKV,GAAe,oBAAmB,yBAChDa,SAxGiBjC,IACrB,IAAI0C,EAAa1C,EAAE2C,OAAOC,MACtBC,EAAe,GACnB,IAAK,IAAID,KAAS3E,EAAW2B,SAAU,CAAC,IAAD0F,EAAAC,GAE3B,QAAVD,EAAA1C,EAAMV,YAAI,IAAAoD,GACK,QADLC,EAAVD,EACGd,MAAM,EAAG,WAAG,IAAAe,GADfA,EAEGtC,cACDC,SAAmB,OAAVR,QAAU,IAAVA,OAAU,EAAVA,EAAYO,gBACvBL,EAAMwB,IAAIjB,WAAWD,SAAmB,OAAVR,QAAU,IAAVA,OAAU,EAAVA,EAAYO,iBAE1CJ,EAAanE,KAAKkE,EAEpB,CACA5E,EACC6E,EAAatE,KAAI,CAACC,EAAKC,IAAUE,EAAqBF,EAAOD,KAC7D,EAyFG4E,UAAQ,OAGV1B,EAAAA,EAAAA,KAAC2B,EAAAA,GAAS,CACTpD,QAASA,EACTJ,KAAM9B,EACNuF,YAAU,EACVC,kBAAmB,GACnBC,SAAUpC,EACVsC,OAAOC,EAAAA,EAAAA,MACPF,kBAAgB,EAChBG,UAAQ,IAERI,EAA0BvE,SAC1B6B,EAAAA,EAAAA,MAACkE,EAAAA,EAAK,CAAC7H,MAAM,EAAM8H,WAAW,EAAMC,KAAK,KAAKjE,UAAS,GAAAK,OAAKV,GAAe,aAAY,SAAQI,SAAA,EAC9FE,EAAAA,EAAAA,KAAC8D,EAAAA,EAAMG,OAAM,CAAClE,UAAU,mBAAkBD,UACzCE,EAAAA,EAAAA,KAAC8D,EAAAA,EAAMI,MAAK,CAACnE,UAAU,cAAaD,UACnCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,OAAMD,SAAC,uBAGvBE,EAAAA,EAAAA,KAAC8D,EAAAA,EAAMK,KAAI,CAACpE,UAAU,OAAMD,SAAC,yCAG7BF,EAAAA,EAAAA,MAACkE,EAAAA,EAAMM,OAAM,CAACrE,UAAU,qCAAoCD,SAAA,EAC3DE,EAAAA,EAAAA,KAAA,OAAAF,UACCE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CACN2D,QAAQ,iBACRzD,QAASA,IACR2B,EAA6B,CAAExE,QAAQ,EAAOJ,GAAI,KAClDmC,SACD,UAIFE,EAAAA,EAAAA,KAAA,OAAAF,UACCE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CACN2D,QAAQ,kBACRtE,UAAU,YACVa,QAASA,KAAM0D,OAxGG3G,EAwGc2E,EAA0B3E,GAvGjEwF,EAAeoB,OAAO5G,QACtBpB,EAAWiI,OAAO7G,GAFnB,IAA0BA,CAwG4C,EAAAmC,SAC9D,gBAOJ0C,EAAwBzE,SACxBiC,EAAAA,EAAAA,KAAC8D,EAAAA,EAAK,CAAC7H,MAAM,EAAM8H,WAAW,EAAMC,KAAK,KAAKjE,UAAS,GAAAK,OAAKV,GAAe,aAAY,SAAQI,UAC9FF,EAAAA,EAAAA,MAACK,EAAAA,EAAI,CAACwE,SArFX,SAAgCC,GAC/BA,EAAMC,iBACN,MAAMC,EAASC,OAAOH,EAAMzD,OAAOyB,IAAIxB,OACvC,IAAIhD,EAAS,CACZ4G,UAAWtC,EAAwB7E,GACnC6C,KAAMkE,EAAMzD,OAAO4B,YAAY3B,MAC/BwB,IAAKkC,GAGNpB,EAAae,OAAOrG,EACrB,EA2E4C4B,SAAA,EACtCE,EAAAA,EAAAA,KAAC8D,EAAAA,EAAMG,OAAM,CAAClE,UAAU,mBAAkBD,UACzCE,EAAAA,EAAAA,KAAC8D,EAAAA,EAAMI,MAAK,CAACnE,UAAU,cAAaD,UACnCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,OAAMD,SAAC,qBAGvBF,EAAAA,EAAAA,MAACkE,EAAAA,EAAMK,KAAI,CAACpE,UAAU,OAAMD,SAAA,EAC3BF,EAAAA,EAAAA,MAACK,EAAAA,EAAKC,MAAK,CAAAJ,SAAA,EACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAK8E,MAAK,CAAChF,UAAS,GAAAK,OAAKV,GAAe,aAAcI,SAAC,yBACxDE,EAAAA,EAAAA,KAACC,EAAAA,EAAKa,QAAO,CACZf,UAAS,GAAAK,OAAKV,GAAe,qBAC7BiB,KAAK,OACLnC,KAAK,cACLwG,aAAcxC,EAAwBhC,KACtCD,SAAWjC,GAAMA,EAAE2C,OAAOC,MAC1BQ,UAAQ,QAGV9B,EAAAA,EAAAA,MAACK,EAAAA,EAAKC,MAAK,CAAAJ,SAAA,EACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAK8E,MAAK,CAAChF,UAAS,GAAAK,OAAKV,GAAe,aAAcI,SAAC,SACxDE,EAAAA,EAAAA,KAACC,EAAAA,EAAKa,QAAO,CACZf,UAAS,GAAAK,OAAKV,GAAe,qBAC7BiB,KAAK,SACLnC,KAAK,MACLwG,aAAcxC,EAAwBE,IACtCnC,SAAWjC,GAAMA,EAAE2C,OAAOC,MAC1BQ,UAAQ,WAIX9B,EAAAA,EAAAA,MAACkE,EAAAA,EAAMM,OAAM,CAACrE,UAAU,qCAAoCD,SAAA,EAC3DE,EAAAA,EAAAA,KAAA,OAAAF,UACCE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CACN2D,QAAQ,iBACRzD,QAASA,IACR6B,EAA2B,CAC1B1E,QAAQ,EACRJ,GAAI,GACJ6C,KAAM,GACNkC,IAAK,IAEN5C,SACD,UAIFE,EAAAA,EAAAA,KAAA,OAAAF,UACCE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CAACC,KAAK,SAASZ,UAAU,YAAWD,SAAC,kBAQhDhE,EAAiBG,OACjB+D,EAAAA,EAAAA,KAACmC,EAAAA,EAAa,CACbjG,IAAKJ,EAAiBI,IACtBH,oBAAqBA,EACrBqG,IAAKtG,EAAiBI,UAM5B,C,4BAEA,U,4QC9UA,SAAS+I,IACR,MAAM,YAAEC,IAAgBC,EAAAA,EAAAA,OACjBC,EAAwBC,IAA6BrJ,EAAAA,EAAAA,WAAS,IAC9DsJ,EAAwBC,IAA6BvJ,EAAAA,EAAAA,WAAS,IAC9DF,EAAkBC,IAAuBC,EAAAA,EAAAA,UAAS,CACxDC,MAAM,EACNC,IAAK,CAAC,KAEAwH,EAAyBC,IAA8B3H,EAAAA,EAAAA,WAAS,IAChEsD,EAAyBC,IAA8BvD,EAAAA,EAAAA,WAAS,IAEhEwJ,EAAaC,IAAkBzJ,EAAAA,EAAAA,UAAS,YAKxC0J,EAAcC,IAAmB3J,EAAAA,EAAAA,aAExCoD,EAAAA,EAAAA,YAAU,KACL8F,IACHU,EAAAA,EAAAA,IAAgBV,GACdpH,MAAM5B,GACFA,EAAI6B,QACP4H,EAAgBzJ,EAAIiC,MACbjC,IAEPH,EAAoB,CAAEE,MAAM,EAAMC,IAAKA,KAChC,KAGRmC,OAAOC,IACP5C,QAAQC,IAAI,WAAY2C,GACxBvC,EAAoB,CAAEE,MAAM,EAAMC,IAAKoC,GAAI,GAE9C,GACE,IAGH,MAAMuH,GAAoBzC,EAAAA,EAAAA,aAAY,yBAA0BlF,IAC/D4H,EAAAA,EAAAA,IAAsB5H,GACpBJ,MAAM5B,GACFA,EAAI6B,QACP4F,GAA2B,GAC3B4B,GAA0B,GAC1BjC,EAAAA,EAAUC,QAAQ,GAADnD,OAAIlE,EAAIkG,MAClBlG,IAEPH,EAAoB,CAAEE,MAAM,EAAMC,IAAKA,KAChC,KAGRmC,OAAOC,IACP5C,QAAQC,IAAI,WAAY2C,GACxBvC,EAAoB,CAAEE,MAAM,EAAMC,IAAKoC,GAAI,MA8B9C,MAAM,YAAEoB,IAAgBC,EAAAA,EAAAA,KACxB,OACCC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACCE,EAAAA,EAAAA,KAAA,OAAKD,UAAS,QAAAK,OAAUV,GAAe,kBAAiB,SAAQI,UAC/DE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,iBAAgBD,UAC9BF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,iDAAgDD,SAAA,EAC9DF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,4BAA2BD,SAAA,EACzCF,EAAAA,EAAAA,MAAA,UACCG,UAAU,sBACVa,QAASA,IAAMmF,OAAOC,QAAQlG,SAAA,CAE7B,KACDE,EAAAA,EAAAA,KAAA,KACCD,UAAU,8BACVkG,MAAO,CAAEC,SAAU,UACd,QAEPlG,EAAAA,EAAAA,KAAA,UACCD,UAAU,6CACVa,QAASA,IAAM2E,GAA0B,GAAMzF,SAC/C,mBAIFE,EAAAA,EAAAA,KAAA,MAAID,UAAU,0BAAyBD,SACzB,OAAZ4F,QAAY,IAAZA,OAAY,EAAZA,EAAcS,wBAKnBnG,EAAAA,EAAAA,KAAA,OAAKD,UAAU,qBAAoBD,UAClCE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,qCAAoCD,UAClDE,EAAAA,EAAAA,KAAA,OAAKD,UAAS,QAAAK,OAAUV,GAAe,mBAAoBI,UAC1DE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWD,UACzBF,EAAAA,EAAAA,MAACwG,EAAAA,EAAI,CACJC,iBAAiB,UACjB1I,GAAG,mBACHoC,UAAS,GAAAK,OAAKV,GAAe,gBAAe,SAC5C4G,MAAI,EACJC,SAAO,EACPC,UAAWhB,EACXiB,SAhHkBC,IACxBjB,EAAeiB,EAAS,EA+GQ5G,SAAA,EAE1BE,EAAAA,EAAAA,KAAC2G,EAAAA,EAAG,CAACD,SAAS,UAAUE,MAAM,UAAUC,aAAY,GAAAzG,OAAKV,GAAe,iBAAkBI,UACzFE,EAAAA,EAAAA,KAACqC,EAAAA,EAAmB,CACnB1E,GAAIuH,EACJvE,KAAK,SACL+C,wBAAyBA,EACzBC,2BAA4BA,OAG9B3D,EAAAA,EAAAA,KAAC2G,EAAAA,EAAG,CAACD,SAAS,SAASE,MAAM,SAASC,aAAY,GAAAzG,OAAKV,GAAe,iBAAkBI,UACvFE,EAAAA,EAAAA,KAACxE,EAAAA,EAAmB,CACnBmC,GAAIuH,EACJvE,KAAK,QACLrB,wBAAyBA,EACzBC,2BAA4BA,iBAQlC6F,IACApF,EAAAA,EAAAA,KAAC8D,EAAAA,EAAK,CAAC7H,MAAM,EAAM8H,WAAW,EAAMC,KAAK,KAAKjE,UAAS,GAAAK,OAAKV,GAAe,aAAY,SAAQI,UAC9FF,EAAAA,EAAAA,MAACK,EAAAA,EAAI,CAACwE,SAhFV,SAA+BC,GAC9BA,EAAMC,iBAIWD,EAAMzD,OAAO9D,eAAe+D,MAClCwD,EAAMzD,OAAO7D,SAAS8D,MACtBwD,EAAMzD,OAAO5D,SAAS6D,MACzBwD,EAAMzD,OAAO3D,MAAM4D,MACbwD,EAAMzD,OAAO1D,YAAY2D,KAGxC,EAoE0CpB,SAAA,EACrCE,EAAAA,EAAAA,KAAC8D,EAAAA,EAAMG,OAAM,CAAClE,UAAU,mBAAkBD,UACzCE,EAAAA,EAAAA,KAAC8D,EAAAA,EAAMI,MAAK,CAACnE,UAAU,cAAaD,UACnCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,OAAMD,SAAC,mBAGvBF,EAAAA,EAAAA,MAACkE,EAAAA,EAAMK,KAAI,CAACpE,UAAU,QAAOD,SAAA,EAC5BE,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAAAJ,UACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKa,QAAO,CACZf,UAAS,GAAAK,OAAKV,GAAe,qBAC7BiB,KAAK,OACLnC,KAAK,iBACLuC,YAAY,SACZR,SAAWjC,GAAMA,EAAE2C,OAAOC,MAC1BQ,UAAQ,OAGV1B,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAAAJ,UACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKa,QAAO,CACZf,UAAS,GAAAK,OAAKV,GAAe,qBAC7BiB,KAAK,OACLnC,KAAK,WACLuC,YAAY,WACZR,SAAWjC,GAAMA,EAAE2C,OAAOC,MAC1BQ,UAAQ,OAGV1B,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAAAJ,UACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKa,QAAO,CACZf,UAAS,GAAAK,OAAKV,GAAe,qBAC7BiB,KAAK,SACLnC,KAAK,WACLuC,YAAY,WACZR,SAAWjC,GAAMA,EAAE2C,OAAOC,MAC1BQ,UAAQ,OAGV1B,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAAAJ,UACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKa,QAAO,CACZf,UAAS,GAAAK,OAAKV,GAAe,qBAC7BiB,KAAK,SACLmG,KAAK,MACLtI,KAAK,QACLuC,YAAY,QACZR,SAAWjC,GAAMA,EAAE2C,OAAOC,MAC1BQ,UAAQ,OAGV1B,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAAAJ,UACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKa,QAAO,CACZf,UAAS,GAAAK,OAAKV,GAAe,qBAC7BiB,KAAK,OACLnC,KAAK,cACLuC,YAAY,WACZR,SAAWjC,GAAMA,EAAE2C,OAAOC,MAC1BQ,UAAQ,UAIX9B,EAAAA,EAAAA,MAACkE,EAAAA,EAAMM,OAAM,CAACrE,UAAU,qCAAoCD,SAAA,EAC3DE,EAAAA,EAAAA,KAAA,OAAAF,UACCE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CACN2D,QAAQ,iBACRzD,QAASA,IAAMyE,GAA0B,GAAOvF,SAChD,UAIFE,EAAAA,EAAAA,KAAA,OAAAF,UACCE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CAACC,KAAK,SAASZ,UAAU,YAAWD,SAAC,kBAQhDwF,IACAtF,EAAAA,EAAAA,KAAC8D,EAAAA,EAAK,CAAC7H,MAAM,EAAM8H,WAAW,EAAMC,KAAK,KAAKjE,UAAS,GAAAK,OAAKV,GAAe,aAAY,SAAQI,UAC9FF,EAAAA,EAAAA,MAACK,EAAAA,EAAI,CAACwE,SA3KV,SAA+BC,GAC9BA,EAAMC,iBACN,MAAMC,EAASC,OAAOH,EAAMzD,OAAOyB,IAAIxB,OACvC,IAAIhD,EAAS,CACZgH,YAAaA,EACb1E,KAAMkE,EAAMzD,OAAO4B,YAAY3B,MAC/BwB,IAAKkC,GAGNiB,EAAkBtB,OAAOrG,EAC1B,EAiK0C4B,SAAA,EACrCE,EAAAA,EAAAA,KAAC8D,EAAAA,EAAMG,OAAM,CAAClE,UAAU,mBAAkBD,UACzCE,EAAAA,EAAAA,KAAC8D,EAAAA,EAAMI,MAAK,CAACnE,UAAU,cAAaD,UACnCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,OAAMD,SAAC,oBAGvBF,EAAAA,EAAAA,MAACkE,EAAAA,EAAMK,KAAI,CAACpE,UAAU,QAAOD,SAAA,EAC5BE,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAAAJ,UACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKa,QAAO,CACZf,UAAS,GAAAK,OAAKV,GAAe,qBAC7BiB,KAAK,OACLnC,KAAK,cACL+B,SAAWjC,GAAMA,EAAE2C,OAAOC,MAC1BQ,UAAQ,OAGV1B,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAAAJ,UACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKa,QAAO,CACZf,UAAS,GAAAK,OAAKV,GAAe,qBAC7BiB,KAAK,SACLnC,KAAK,MACLuC,YAAY,MACZR,SAAWjC,GAAMA,EAAE2C,OAAOC,MAC1BQ,UAAQ,UAIX9B,EAAAA,EAAAA,MAACkE,EAAAA,EAAMM,OAAM,CAACrE,UAAU,qCAAoCD,SAAA,EAC3DE,EAAAA,EAAAA,KAAA,OAAAF,UACCE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CACN2D,QAAQ,iBACRzD,QAASA,IAAM2E,GAA0B,GAAOzF,SAChD,UAIFE,EAAAA,EAAAA,KAAA,OAAAF,UACCE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CAACC,KAAK,SAASZ,UAAU,YAAWD,SAAC,kBAQhDhE,EAAiBG,OACjB+D,EAAAA,EAAAA,KAACmC,EAAAA,EAAa,CACbjG,IAAKJ,EAAiBI,IACtBH,oBAAqBA,EACrBqG,IAAKtG,EAAiBI,QAK3B,E,4BAEA,U,6ICzSO,MAAM6K,EAAwB,CACpCC,OAAQA,CAACC,EAAUC,KAAK,IACpBD,EACHE,gBAAiBD,EAAME,WAAa,OAAS,QAC7CC,MAAOH,EAAME,WAAa,QAAU,WAGzBE,EAAsB,CAClCN,OAAQA,CAACO,EAAML,KAAK,IAChBH,EAAsBC,OAAOO,EAAML,GACtC,UAAW,CACVC,gBAAiB,YACjBE,MAAO,YAsBH,SAAS7J,EAAmBgK,GAClC,IAAIC,EAASD,EAAYE,OAAO,GAO5BC,EACQ,MAAXF,GACW,MAAXA,GACW,SAAXA,GACW,SAAXA,GACW,SAAXA,EACD,MAXY,MAAXA,GACW,MAAXA,GACW,QAAXA,GACW,QAAXA,GACW,QAAXA,GAQOzH,EAAAA,EAAAA,KAAA,SAAOD,UAAU,8BAA6BD,SAAC,QAC5C6H,GACH3H,EAAAA,EAAAA,KAAA,SAAOD,UAAU,8BAA6BD,SAAC,SAGrD0H,IACCxH,EAAAA,EAAAA,KAAA,SAAOD,UAAU,6BAA4BD,SAC3C0H,EAAYrB,eAKlB,CAqBO,SAASpD,EAAmB6B,GAElC,OADAA,EAASgD,WAAWhD,GAAU,GAAGiD,QAAQ,IAC5B,GAEXjI,EAAAA,EAAAA,MAAA,QAAMG,UAAU,cAAaD,SAAA,CAC3B,IACA8E,EAAO,KAAC5E,EAAAA,EAAAA,KAAA,KAAGD,UAAU,0BAGH,IAAX6E,GAA2B,OAAXA,GAC1BA,EAAS,GACFhF,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,IAAE8E,OAGfhF,EAAAA,EAAAA,MAAA,QAAMG,UAAU,eAAcD,SAAA,CAC5B,IACA8E,EAAO,KAAC5E,EAAAA,EAAAA,KAAA,KAAGD,UAAU,uBAGzB,CAiBO,SAAS+H,EAAe/J,EAAQgK,GACtC,MAAyB,MAArBhK,EAAO2J,OAAO,IAAmC,MAArB3J,EAAO2J,OAAO,IAE5C1H,EAAAA,EAAAA,KAAA,SACCD,UAAU,8BACV,cAAY,UACZ,iBAAe,MACf6G,MAAOmB,EAAOjI,SACd,cAI6B,MAArB/B,EAAO2J,OAAO,IAAmC,MAArB3J,EAAO2J,OAAO,IAEnD1H,EAAAA,EAAAA,KAAA,SACCD,UAAU,6BACV,cAAY,UACZ,iBAAe,MACf6G,MAAOmB,EAAOjI,SACd,cAiBDE,EAAAA,EAAAA,KAAA,SACCD,UAAU,2BACV,cAAY,UACZ,iBAAe,MACf6G,MAAOmB,EAAOjI,SAEb/B,GAIL,CAEO,SAASiK,EAAmBC,GAClC,OAAIC,MAAMD,GACF,MAEJA,EAAS,IACLA,EAAOxG,WAEXwG,GAAU,KAAQA,EAAS,KACtBA,EAAS,KAAMJ,QAAQ,GAAK,KAEjCI,GAAU,KAAUA,EAAS,KACxBA,EAAS,KAAQJ,QAAQ,GAAK,KAEnCI,GAAU,KACLA,EAAS,KAAUJ,QAAQ,GAAK,WADzC,CAGD,CAKO,MAoCM5F,EAAWA,IACqC,SAAxCkG,aAAaC,QAAQ,iBApCzCC,EAAAA,EAAAA,IACC,YACA,CACCC,WAAY,CACXC,QAAS,eAEVvF,OAAQ,CACPwF,OAAQ,kBACRC,MAAO,kBACPC,SAAU,oBAGZ,QA4BO,eAtBRL,EAAAA,EAAAA,IACC,gBACA,CACCC,WAAY,CACXC,QAAS,QAEVvF,OAAQ,CACPwF,OAAQ,kBACRC,MAAO,kBACPC,SAAU,oBAGZ,SAaO,gB,oEC1O6IC,EAAS,WAAW,OAAOA,EAAS1K,OAAO2K,QAAQ,SAASC,GAAG,IAAI,IAAIC,EAAExK,EAAE,EAAEyK,EAAEC,UAAUC,OAAO3K,EAAEyK,EAAEzK,IAAI,IAAI,IAAI4K,KAAKJ,EAAEE,UAAU1K,GAAGL,OAAOkL,UAAUC,eAAeC,KAAKP,EAAEI,KAAKL,EAAEK,GAAGJ,EAAEI,IAAI,OAAOL,CAAC,GAAGS,MAAMC,KAAKP,UAAU,EAAsM,IAA8iGQ,EAAM,CAACjG,QAAziG,SAASsF,GAAG,OAAOY,EAAAA,cAAoB,MAAMd,EAAS,CAACe,QAAQ,sBAAsBC,MAAM,GAAGC,OAAO,IAAIf,GAAGY,EAAAA,cAAoB,OAAO,CAACI,EAAE,gPAAgPvD,KAAK,YAAY,EAA8qFwD,KAAvqF,SAASjB,GAAG,OAAOY,EAAAA,cAAoB,MAAMd,EAAS,CAACe,QAAQ,sBAAsBC,MAAM,GAAGC,OAAO,IAAIf,GAAGY,EAAAA,cAAoB,OAAO,CAACI,EAAE,wuBAAwuBvD,KAAK,YAAY,EAA8yDyD,QAApyD,SAASlB,GAAG,OAAOY,EAAAA,cAAoB,MAAMd,EAAS,CAAC5I,UAAU,mBAAmB8I,GAAG,EAA6tDmB,KAAttD,SAASnB,GAAG,OAAOY,EAAAA,cAAoB,MAAMd,EAAS,CAACe,QAAQ,oBAAoBC,MAAM,GAAGC,OAAO,IAAIf,GAAGY,EAAAA,cAAoB,OAAO,CAACI,EAAE,8/BAA8/BvD,KAAK,YAAY,EAAykB2D,MAAjkB,SAASpB,GAAG,OAAOY,EAAAA,cAAoB,MAAMd,EAAS,CAACe,QAAQ,oBAAoBC,MAAM,GAAGC,OAAO,IAAIf,GAAGY,EAAAA,cAAoB,OAAO,CAACI,EAAE,2WAA2WvD,KAAK,YAAY,GAA0E4D,EAAO,CAAC3G,QAAQ,UAAUyG,KAAK,UAAUF,KAAK,UAAUG,MAAM,UAAUF,QAAQ,WAAWI,EAAM,SAAStB,GAAG,IAAIC,EAAExK,EAAEyK,EAAEG,EAAEjK,EAAE,WAAW4J,EAAEuB,UAAU,cAAc5I,SAAS,UAAU,SAAS,OAAO6I,EAAE,CAAC,WAAWxB,EAAEjI,QAAQ,qBAAqB,GAAG,YAAYiI,EAAElI,MAAM2J,KAAK,KAAKC,IAAI,QAAQjM,EAAEuK,EAAE2B,WAAM,IAASlM,OAAE,EAAOA,EAAE0F,OAAO,OAAO,MAAM,QAAQ+E,EAAEF,EAAE2B,WAAM,IAASzB,OAAE,EAAOA,EAAE9C,QAAQ,SAAS,MAAM,QAAQiD,EAAEL,EAAE2B,WAAM,IAAStB,OAAE,EAAOA,EAAE7B,QAAQ6C,EAAOrB,EAAElI,OAAO8J,EAAEjB,EAAMX,EAAElI,MAAM+J,GAAE1O,EAAAA,EAAAA,YAAW8M,EAAE,CAAC6B,QAAQ,IAAI1L,IAAI,GAAG6J,IAAIe,EAAEa,EAAE,GAAGE,EAAEF,EAAE,GAAGG,EAAElC,EAAS,CAACmC,YAAYjC,EAAEkC,QAAQ,QAAG,EAAOC,UAAUnC,EAAEkC,QAAQ,QAAG,EAAOE,WAAWV,GAAGV,GAAGqB,EAAE,WAAW,IAAIpC,EAAE8B,IAAI9B,EAAE,CAAC6B,QAAQ,IAAI1L,GAAG,QAAQ6J,IAAIqC,YAAY,WAAWtC,EAAEuC,OAAOvC,EAAElL,GAAGkL,EAAEuB,SAAS,GAAG,IAAI,GAAEhL,EAAAA,EAAAA,YAAW,WAAW,IAAI0J,EAAExK,EAAE6M,YAAY,WAAW,IAAItC,EAAE+B,IAAI/B,EAAE,CAAC8B,QAAQ,IAAI1L,GAAG,OAAO4J,GAAG,GAAG,IAAI,OAAO,IAAIA,EAAEwC,YAAYvC,EAAEqC,YAAY,WAAWD,GAAG,GAAG,IAAIrC,EAAEwC,YAAY,WAAWC,aAAahN,GAAGwK,GAAGwC,aAAaxC,EAAE,CAAC,GAAG,KAAI1J,EAAAA,EAAAA,YAAW,WAAWyJ,EAAE5M,MAAMiP,GAAG,GAAG,CAACrC,EAAE5M,OAAO,IAAIsP,EAAE,CAACC,SAAS,EAAE5K,QAAQiI,EAAEjI,QAAQ6K,WAAW,SAAS3C,GAAG,KAAKA,EAAE4C,SAAS7C,EAAEjI,QAAQkI,EAAE,GAAG,OAAOW,EAAAA,cAAoB,MAAMd,EAAS,CAAC5I,UAAUsK,EAAEsB,KAAK9C,EAAE8C,KAAK9C,EAAE8C,KAAK,SAAS1F,MAAM4E,GAAGhC,EAAEjI,QAAQ2K,EAAE,CAAC,GAAG1C,EAAE+C,WAAW/C,EAAE+C,aAAanC,EAAAA,cAAoBgB,EAAE,MAAMhB,EAAAA,cAAoB,MAAM,CAAC1J,UAAU8I,EAAEkC,QAAQ,wBAAwB,iBAAiBlC,EAAEkC,SAAStB,EAAAA,cAAoB,KAAK,CAAC1J,UAAU,cAAc8I,EAAEkC,SAAStB,EAAAA,cAAoB,MAAM,CAAC1J,UAAU,WAAW8I,EAAEgD,OAAO,EAAE1B,EAAM2B,UAAU,CAACnL,KAAKoL,EAAAA,OAAOC,WAAWH,MAAKI,EAAAA,EAAAA,WAAU,CAACF,EAAAA,OAAOG,EAAAA,OAAOF,WAAW/P,KAAKkQ,EAAAA,KAAKf,OAAOgB,EAAAA,KAAKzO,IAAGsO,EAAAA,EAAAA,WAAU,CAACF,EAAAA,OAAOM,EAAAA,SAAShB,UAAUgB,EAAAA,OAAOtB,QAAQgB,EAAAA,OAAO3B,SAAS2B,EAAAA,OAAOH,WAAWQ,EAAAA,KAAK5B,KAAI8B,EAAAA,EAAAA,OAAM,CAAC,GAAG1L,QAAQwL,EAAAA,KAAKT,KAAKI,EAAAA,QAAQ5B,EAAMoC,aAAa,CAAC5O,QAAG,EAAO1B,MAAK,EAAGmP,YAAO,EAAOC,UAAU,EAAEN,aAAQ,EAAOX,SAAS,aAAawB,gBAAW,EAAOpB,IAAI,CAAC,EAAE5J,aAAQ,EAAO+K,KAAK,UAAU,IAAIa,EAAU,SAAS3D,GAAG,OAAOA,EAAE4D,QAAQ,aAAa,SAAS5D,GAAG,OAAOA,EAAE,GAAG1C,aAAa,GAAG,EAAEuG,EAAc,CAACC,QAAQ,GAAGC,UAAU,GAAGC,SAAS,GAAGC,WAAW,GAAGC,aAAa,GAAGC,YAAY,IAAIC,EAAe,SAASpE,GAAG,IAAIC,EAAED,EAAEqE,MAAM5O,EAAEuK,EAAEsE,SAASpE,GAAE/M,EAAAA,EAAAA,UAAS0Q,GAAexD,EAAEH,EAAE,GAAG9J,EAAE8J,EAAE,IAAG3J,EAAAA,EAAAA,YAAW,WAAW0J,GAAG7J,GAAG,SAAS4J,GAAG,IAAIvK,EAAEyK,EAAEyD,EAAU1D,EAAEsB,UAAU,cAAc,OAAOzB,EAASA,EAAS,CAAC,EAAEE,KAAKvK,EAAE,CAAC,GAAGyK,GAA/hL,WAA0B,IAAI,IAAIF,EAAE,EAAEC,EAAE,EAAExK,EAAE0K,UAAUC,OAAOH,EAAExK,EAAEwK,IAAID,GAAGG,UAAUF,GAAGG,OAAO,IAAIF,EAAEqE,MAAMvE,GAAGK,EAAE,EAAE,IAAIJ,EAAE,EAAEA,EAAExK,EAAEwK,IAAI,IAAI,IAAI7J,EAAE+J,UAAUF,GAAGuB,EAAE,EAAEE,EAAEtL,EAAEgK,OAAOoB,EAAEE,EAAEF,IAAInB,IAAIH,EAAEG,GAAGjK,EAAEoL,GAAG,OAAOtB,CAAC,CAA+1KsE,CAAexE,EAAEE,GAAG,CAACD,IAAIxK,GAAG,GAAG,GAAG,CAACwK,IAAI,IAAIuB,EAAE,SAASxB,EAAEC,GAAG7J,GAAG,SAASX,GAAG,IAAIyK,EAAEG,EAAEsD,EAAU1D,GAAG,cAAc,OAAOH,EAASA,EAAS,CAAC,EAAErK,KAAKyK,EAAE,CAAC,GAAGG,GAAG5K,EAAE4K,GAAGoE,QAAQ,SAASxE,GAAG,OAAOA,EAAEnL,KAAKkL,CAAC,IAAIE,GAAG,GAAG,EAAEwB,EAAE,CAAC,OAAO,SAAS,SAAS,OAAOd,EAAAA,cAAoBA,EAAAA,SAAe,KAAK,CAAC,MAAM,UAAU5M,KAAK,SAASgM,GAAG,OAAOY,EAAAA,cAAoB,MAAM,CAAC8D,IAAI,OAAO1E,EAAE9I,UAAU,UAAUwK,EAAE1N,KAAK,SAASiM,GAAG,IAAIC,EAAE,GAAGF,EAAEC,EAAE7J,EAAE,CAAC,WAAW,WAAW4J,EAAE,iBAAiB,IAAIyB,KAAK,KAAK,OAAOb,EAAAA,cAAoB,MAAM,CAAC8D,IAAIxE,EAAEhJ,UAAUd,GAAGiK,EAAEH,GAAGlM,KAAK,SAASgM,GAAG,OAAOY,EAAAA,cAAoBU,EAAMxB,EAAS,CAAC4E,IAAIxE,EAAE,IAAIF,EAAElL,IAAIkL,EAAE,CAAClL,GAAGkL,EAAElL,GAAGkO,KAAKhD,EAAEgD,KAAKlL,KAAKkI,EAAElI,KAAKC,QAAQiI,EAAEjI,QAAQyK,UAAUxC,EAAEwC,UAAUpP,KAAKqC,IAAIuK,EAAElL,GAAGyN,OAAOf,IAAI,IAAI,IAAI,IAAI,EAA6W4C,EAAenB,UAAU,CAACoB,OAAMZ,EAAAA,EAAAA,OAAM,CAAC,GAAGa,SAASd,EAAAA,QAAQY,EAAeV,aAAa,CAACW,WAAM,EAAOC,cAAS,IAAzd,SAAqBtE,EAAEC,QAAG,IAASA,IAAIA,EAAE,CAAC,GAAG,IAAIxK,EAAEwK,EAAE0E,SAAS,GAAG3E,GAAG,oBAAoB4E,SAAS,CAAC,IAAI1E,EAAE0E,SAASC,MAAMD,SAASE,qBAAqB,QAAQ,GAAGzE,EAAEuE,SAASG,cAAc,SAAS1E,EAAEvI,KAAK,WAAW,QAAQrC,GAAGyK,EAAE8E,WAAW9E,EAAE+E,aAAa5E,EAAEH,EAAE8E,YAAY9E,EAAEgF,YAAY7E,GAAGA,EAAE8E,WAAW9E,EAAE8E,WAAWC,QAAQpF,EAAEK,EAAE6E,YAAYN,SAASS,eAAerF,GAAG,CAAC,CAA6lEsF,CAA99D,+9DAA++D,IAAIC,EAAa,EAAE9K,EAAU,SAASuF,EAAEC,GAAG,IAAIxK,EAAEyK,EAAEG,EAAEuE,SAASY,gBAAgB,QAAQ/P,EAAEwK,SAAI,IAASxK,OAAE,EAAOA,EAAEgQ,mBAAmB,gBAAgBpF,KAAKA,EAAEuE,SAASG,cAAc,QAAQjQ,GAAG,eAAe8P,SAASc,KAAKR,YAAY7E,IAAIkF,GAAc,EAAE,IAAInP,EAAE,UAAK,KAAU,QAAQ8J,EAAED,SAAI,IAASC,OAAE,EAAOA,EAAEsC,WAAW,EAAEvC,EAAEuC,WAAWhB,EAAE1B,EAAS,CAAChL,GAAGyQ,EAAavC,KAAKhD,GAAGC,GAAG0F,EAAAA,OAAgB/E,EAAAA,cAAoBwD,EAAe,CAACC,MAAM7C,IAAInB,GAAG,IAAIqB,EAAE,IAAIkE,SAAS,SAAS5F,GAAGsC,YAAY,WAAWtC,GAAG,GAAG5J,EAAE,IAAI,OAAOsL,EAAEmE,KAAK,WAAWF,EAAAA,OAAgB/E,EAAAA,cAAoBwD,EAAe,CAACE,SAAS9C,EAAE1M,KAAKuL,EAAE,EAAEqB,CAAC,EAAEjH,EAAUC,QAAQ,SAASsF,EAAEC,GAAG,OAAOxF,EAAUuF,EAAEF,EAASA,EAAS,CAAC,EAAEG,GAAG,CAACnI,KAAK,YAAY,EAAE2C,EAAUwG,KAAK,SAASjB,EAAEC,GAAG,OAAOxF,EAAUuF,EAAEF,EAASA,EAAS,CAAC,EAAEG,GAAG,CAACnI,KAAK,SAAS,EAAE2C,EAAU0G,KAAK,SAASnB,EAAEC,GAAG,OAAOxF,EAAUuF,EAAEF,EAASA,EAAS,CAAC,EAAEG,GAAG,CAACnI,KAAK,SAAS,EAAE2C,EAAU2G,MAAM,SAASpB,EAAEC,GAAG,OAAOxF,EAAUuF,EAAEF,EAASA,EAAS,CAAC,EAAEG,GAAG,CAACnI,KAAK,UAAU,EAAE2C,EAAUyG,QAAQ,SAASlB,EAAEC,GAAG,OAAOxF,EAAUuF,EAAEF,EAASA,EAAS,CAAC,EAAEG,GAAG,CAACnI,KAAK,YAAY,EAAE,S", "sources": ["app/components/table/StrategyOrdersTable.jsx", "app/components/table/StrategyRecordTable.jsx", "app/user-pages/strategy/StrategyData.jsx", "app/user-pages/ui-helper.jsx", "../node_modules/cogo-toast/dist/index.es.js"], "sourcesContent": ["import React, { useEffect, useMemo, useState } from \"react\";\r\nimport DataTable from \"react-data-table-component\";\r\nimport { Button, Form, Modal } from \"react-bootstrap\";\r\nimport ResponseModal from \"../modal/ResponseModal\";\r\nimport { useMutation, useQuery, useQueryClient } from \"react-query\";\r\nimport { GetOrdersData } from \"../../../services/backendServices\";\r\nimport { getTheme, getTransactionType } from \"../../user-pages/ui-helper\";\r\nimport DatePicker from \"react-datepicker\";\r\nimport \"react-datepicker/dist/react-datepicker.css\";\r\nimport { format } from \"date-fns\";\r\nimport { useTheme } from \"../../context/ThemeContext\";\r\n\r\nfunction StrategyOrdersTable(props) {\r\n\tconsole.log(\"data\", props);\r\n\tconst queryClient = useQueryClient();\r\n\tconst [apiResponseModal, setApiResponseModal] = useState({\r\n\t\tshow: false,\r\n\t\tres: {},\r\n\t}); // Modal : on API Response\r\n\tconst [fetchOrdersData, setFetchOrdersData] = useState(false); // Fetch Data\r\n\tconst [tableDataFormatted, setTableDataFormatted] = useState([]);\r\n\tconst rawUserMap = useMemo(() => new Map(), []);\r\n\r\n\t//  API CALL for GetStrategyOrder\r\n\tuseQuery(\"GetOrdersData\", () =>\r\n\t\tGetOrdersData(props.id, format(new Date(), \"yyyy-MM-dd\"))\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tconst jsonArr = Object.values(res.data);\r\n\t\t\t\t\tsetTableData(jsonArr);\r\n\t\t\t\t\t// Setting row data in rawUserMap with key value\r\n\t\t\t\t\tjsonArr?.map((obj, index) => {\r\n\t\t\t\t\t\treturn rawUserMap.set(obj.id, obj);\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t})\r\n\t);\r\n\r\n\tfunction setTableData(tableData) {\r\n\t\tvar rowTableData = [];\r\n\t\ttableData?.map((obj, index) => {\r\n\t\t\treturn rowTableData.push(getRowFormatForTable(index, obj));\r\n\t\t});\r\n\t\tsetTableDataFormatted(rowTableData);\r\n\t}\r\n\r\n\tfunction getRowFormatForTable(ind, obj) {\r\n\t\treturn {\r\n\t\t\tindex: ind + 1,\r\n\t\t\ttrading_symbol: obj.trading_symbol,\r\n\t\t\texchange: obj.exchange,\r\n\t\t\tquantity: obj.quantity,\r\n\t\t\tprice: obj.price,\r\n\t\t\taction_type: getTransactionType(obj.action_type),\r\n\t\t\tobj: obj,\r\n\t\t};\r\n\t}\r\n\r\n\tconst actionTypeSortFunction = (a, b) => {\r\n\t\tif (\r\n\t\t\ta.obj.action_type === \"B\" ||\r\n\t\t\ta.obj.action_type === \"b\" ||\r\n\t\t\ta.obj.action_type === \"buy\" ||\r\n\t\t\ta.obj.action_type === \"BUY\" ||\r\n\t\t\ta.obj.action_type === \"Buy\"\r\n\t\t) {\r\n\t\t\treturn 1;\r\n\t\t} else {\r\n\t\t\treturn -1;\r\n\t\t}\r\n\t};\r\n\r\n\tconst columns = [\r\n\t\t{\r\n\t\t\tname: \"Id\",\r\n\t\t\tselector: (row) => row.index,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"65px\",\r\n\t\t\tminWidth: \"65px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Trading Symbol\",\r\n\t\t\tselector: (row) => row.trading_symbol,\r\n\t\t\twrap: true,\r\n\t\t\tsortable: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Exchange\",\r\n\t\t\tselector: (row) => row.exchange,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t\t// maxWidth: \"150px\",\r\n\t\t\t// minWidth: \"150px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Qty\",\r\n\t\t\tselector: (row) => row.quantity,\r\n\t\t\tsortable: true,\r\n\t\t\t// maxWidth: \"130px\",\r\n\t\t\t// minWidth: \"130px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Price\",\r\n\t\t\tselector: (row) => row.price,\r\n\t\t\tsortable: true,\r\n\t\t\t// maxWidth: \"130px\",\r\n\t\t\t// minWidth: \"130px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Type\",\r\n\t\t\tselector: (row) => row.action_type,\r\n\t\t\tsortable: true,\r\n\t\t\tsortFunction: actionTypeSortFunction,\r\n\t\t\t// maxWidth: \"140px\",\r\n\t\t\t// minWidth: \"140px\",\r\n\t\t},\r\n\t\t{ name: \"obj\", selector: (row) => row.obj, omit: true },\r\n\t];\r\n\r\n\tconst handleSearch = (e) => {\r\n\t\tvar search_val = e.target.value;\r\n\t\tvar filteredData = [];\r\n\t\tfor (let value of rawUserMap.values()) {\r\n\t\t\tif (\r\n\t\t\t\tvalue.trading_symbol\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.exchange?.toLowerCase().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.quantity.toString().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.action_type?.toLowerCase().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.price.toString().includes(search_val)\r\n\t\t\t) {\r\n\t\t\t\tfilteredData.push(value);\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetTableDataFormatted(\r\n\t\t\tfilteredData.map((obj, index) => getRowFormatForTable(index, obj))\r\n\t\t);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tif (fetchOrdersData) {\r\n\t\t\tqueryClient.refetchQueries(\"GetStrategyOrder\");\r\n\t\t\tsetFetchOrdersData(false);\r\n\t\t}\r\n\t}, [fetchOrdersData]);\r\n\r\n\tuseEffect(() => {\r\n\t\tif (props.fetchOrdersDataAfterAdd) {\r\n\t\t\tqueryClient.refetchQueries(\"GetStrategyOrder\");\r\n\t\t\tprops.setFetchOrdersDataAfterAdd(false);\r\n\t\t}\r\n\t}, [props.fetchOrdersDataAfterAdd]);\r\n\tconst handleDateChange = (date) => {\r\n\t\tsetSelectedDate(date);\r\n\t};\r\n\tconst [selectedDate, setSelectedDate] = useState(new Date());\r\n\r\n\tconst handleShowOrder = () => {\r\n\t\t//  API CALL for GetStrategyOrder\r\n\r\n\t\tGetOrdersData(props.id, format(selectedDate, \"yyyy-MM-dd\"))\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tconst jsonArr = Object.values(res.data);\r\n\t\t\t\t\tsetTableData(jsonArr);\r\n\t\t\t\t\t// Setting row data in rawUserMap with key value\r\n\t\t\t\t\tjsonArr?.map((obj, index) => {\r\n\t\t\t\t\t\treturn rawUserMap.set(obj.id, obj);\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t});\r\n\t};\r\n\r\n\r\n\tconst { isDarkTheme } = useTheme()\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<div className=\"row \">\r\n\t\t\t\t<div className=\"col-md-3\">\r\n\t\t\t\t\t<Form>\r\n\t\t\t\t\t\t<Form.Group controlId=\"datePicker\" className={`${isDarkTheme && \"dark-datepicker\"} mb-0`}>\r\n\t\t\t\t\t\t\t{/* Select Date:&nbsp; &nbsp; */}\r\n\t\t\t\t\t\t\t<DatePicker\r\n\t\t\t\t\t\t\t\tselected={selectedDate}\r\n\t\t\t\t\t\t\t\tonChange={handleDateChange}\r\n\t\t\t\t\t\t\t\tclassName=\"form-control\"\r\n\t\t\t\t\t\t\t\tdateFormat=\"dd-MM-yyyy\" // Customize date format if needed\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t</Form>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"col-md-2 py-1 rounded\">\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\ttype=\"btn\"\r\n\t\t\t\t\t\tclassName=\"btn btn-md btn-primary\"\r\n\t\t\t\t\t\tonClick={handleShowOrder}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tShow Orders\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"col-md-3\"></div>\r\n\t\t\t\t<div className=\"col-md-4\">\r\n\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\tplaceholder=\"Search\"\r\n\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"} mb-2 inputbox-style`}\r\n\t\t\t\t\t\t\tonChange={handleSearch}\r\n\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Form.Group>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div className=\"table-responsive mt-4\">\r\n\t\t\t\t<DataTable\r\n\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\tdata={tableDataFormatted}\r\n\t\t\t\t\tpagination\r\n\t\t\t\t\tpaginationPerPage={10}\r\n\t\t\t\t\tstriped={!isDarkTheme}\r\n\t\t\t\t\thighlightOnHover\r\n\t\t\t\t\ttheme={getTheme()}\r\n\t\t\t\t\tnoHeader\r\n\t\t\t\t/>\r\n\t\t\t\t{apiResponseModal.show && (\r\n\t\t\t\t\t<ResponseModal\r\n\t\t\t\t\t\tres={apiResponseModal.res}\r\n\t\t\t\t\t\tsetApiResponseModal={setApiResponseModal}\r\n\t\t\t\t\t\tmsg={apiResponseModal.res}\r\n\t\t\t\t\t/>\r\n\t\t\t\t)}\r\n\t\t\t</div>\r\n\t\t</>\r\n\t);\r\n}\r\n\r\nexport default StrategyOrdersTable;\r\n", "import React, { useEffect, useMemo, useState } from \"react\";\r\nimport DataTable from \"react-data-table-component\";\r\nimport { Button, Form, Modal } from \"react-bootstrap\";\r\nimport ResponseModal from \"../modal/ResponseModal\";\r\nimport { useMutation, useQuery, useQueryClient } from \"react-query\";\r\nimport { getSubPnlComponent, getTheme } from \"../../user-pages/ui-helper\";\r\nimport {\r\n\tGetStrategyRecord,\r\n\tRemoveStrategyRecord,\r\n\tUpdateStrategyRecordData,\r\n} from \"../../../services/backendServices\";\r\nimport cogoToast from \"cogo-toast\";\r\nimport { formatStanderdTimeString } from \"../../../Util/helper_functions\";\r\nimport { useTheme } from \"../../context/ThemeContext\";\r\n\r\nfunction StrategyRecordTable(props) {\r\n\tconst queryClient = useQueryClient();\r\n\tconst [apiResponseModal, setApiResponseModal] = useState({\r\n\t\tshow: false,\r\n\t\tres: {},\r\n\t}); // Modal : on API Response\r\n\tconst [showDeleteRecordDataModal, setShowDeleteRecordDataModal] = useState({\r\n\t\tstatus: false,\r\n\t\tid: \"\",\r\n\t});\r\n\tconst [showEditRecordDataModal, setShowEditRecordDataModal] = useState({\r\n\t\tstatus: false,\r\n\t\tid: \"\",\r\n\t\tdate: \"\",\r\n\t\tpnl: 0,\r\n\t});\r\n\tconst [fetchRecordData, setFetchRecordData] = useState(false); // Fetch Data\r\n\tconst [tableDataFormatted, setTableDataFormatted] = useState([]);\r\n\tconst rawUserMap = useMemo(() => new Map(), []);\r\n\r\n\t// API CALL for GetStrategyRecord\r\n\tuseQuery(\"GetStrategyRecord\", () =>\r\n\t\tGetStrategyRecord(props.id)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tconst jsonArr = Object.values(res.data);\r\n\t\t\t\t\tsetTableData(jsonArr);\r\n\t\t\t\t\t// Setting row data in rawUserMap with key value\r\n\t\t\t\t\tjsonArr.map((obj, index) => {\r\n\t\t\t\t\t\treturn rawUserMap.set(index, obj);\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t})\r\n\t);\r\n\r\n\tfunction setTableData(tableData) {\r\n\t\tvar rowTableData = [];\r\n\t\ttableData.map((obj, index) => {\r\n\t\t\treturn rowTableData.push(getRowFormatForTable(index, obj));\r\n\t\t});\r\n\t\tsetTableDataFormatted(rowTableData);\r\n\t}\r\n\r\n\tfunction getRowFormatForTable(ind, obj) {\r\n\t\treturn {\r\n\t\t\tindex: ind + 1,\r\n\t\t\trecord_date: obj.date.slice(0, 10),\r\n\t\t\tpnl: getSubPnlComponent(obj.pnl),\r\n\t\t\taction: (\r\n\t\t\t\t<div className=\"d-flex align-item-center\">\r\n\t\t\t\t\t<button\r\n\t\t\t\t\t\ttype=\"button\"\r\n\t\t\t\t\t\tclassName=\"btn btn-primary btn-icon-text btnEditRecord mx-1 d-flex align-items-center\"\r\n\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\tsetShowEditRecordDataModal({\r\n\t\t\t\t\t\t\t\tstatus: true,\r\n\t\t\t\t\t\t\t\tid: obj.id,\r\n\t\t\t\t\t\t\t\tdate: obj.date.slice(0, 10),\r\n\t\t\t\t\t\t\t\tpnl: obj.pnl,\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<i className=\"mdi mdi-pencil btn-icon-append m-0 pl-1\"></i>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<button\r\n\t\t\t\t\t\ttype=\"button\"\r\n\t\t\t\t\t\tclassName=\"btn btn-danger btn-icon-text btnDeleteRecord my-button mx-1 d-flex align-items-center\"\r\n\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\tsetShowDeleteRecordDataModal({ status: true, id: obj.id });\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<i className=\"mdi mdi-delete-sweep btn-icon-append m-0 pl-1\"></i>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</div>\r\n\t\t\t),\r\n\t\t\tobj: obj,\r\n\t\t};\r\n\t}\r\n\r\n\tconst pnlSortFunction = (a, b) => {\r\n\t\treturn a.obj.pnl > b.obj.pnl ? -1 : 1;\r\n\t};\r\n\r\n\tconst columns = [\r\n\t\t{\r\n\t\t\tname: \"Id\",\r\n\t\t\tselector: (row) => row.index,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"65px\",\r\n\t\t\tminWidth: \"65px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Record Date\",\r\n\t\t\tselector: (row) => row.record_date,\r\n\t\t\twrap: true,\r\n\t\t\tsortable: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"PnL\",\r\n\t\t\tselector: (row) => row.pnl,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t\tsortFunction: pnlSortFunction,\r\n\t\t},\r\n\t\t{ name: \"Action\", selector: (row) => row.action },\r\n\t\t{ name: \"obj\", selector: (row) => row.obj, omit: true },\r\n\t];\r\n\r\n\tconst handleSearch = (e) => {\r\n\t\tvar search_val = e.target.value;\r\n\t\tvar filteredData = [];\r\n\t\tfor (let value of rawUserMap.values()) {\r\n\t\t\tif (\r\n\t\t\t\tvalue.date\r\n\t\t\t\t\t?.slice(0, 10)\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.pnl.toString().includes(search_val?.toLowerCase())\r\n\t\t\t) {\r\n\t\t\t\tfilteredData.push(value);\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetTableDataFormatted(\r\n\t\t\tfilteredData.map((obj, index) => getRowFormatForTable(index, obj))\r\n\t\t);\r\n\t};\r\n\r\n\t// API CALL to Delete Strategy Record\r\n\tconst DeleteMutation = useMutation(\"RemoveStrategyRecord\", (values) =>\r\n\t\tRemoveStrategyRecord(values)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tsetFetchRecordData(true);\r\n\t\t\t\t\tsetShowDeleteRecordDataModal({ status: false, id: \"\" });\r\n\t\t\t\t\tcogoToast.success(`${res.msg}`);\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t})\r\n\t);\r\n\t// Delete Strategy Record\r\n\tfunction DeleteRecordData(id) {\r\n\t\tDeleteMutation.mutate(id);\r\n\t\trawUserMap.delete(id);\r\n\t}\r\n\r\n\t// // API CALL to Edit Strategy Record\r\n\tconst editMutation = useMutation(\"UpdateStrategyRecordData\", (values) =>\r\n\t\tUpdateStrategyRecordData(values)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tsetFetchRecordData(true);\r\n\t\t\t\t\tsetShowEditRecordDataModal({\r\n\t\t\t\t\t\tstatus: false,\r\n\t\t\t\t\t\tid: \"\",\r\n\t\t\t\t\t\tdate: \"\",\r\n\t\t\t\t\t\tpnl: 0,\r\n\t\t\t\t\t});\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t})\r\n\t);\r\n\tfunction handleEditRecordSubmit(event) {\r\n\t\tevent.preventDefault();\r\n\t\tconst pnlVal = Number(event.target.pnl.value);\r\n\t\tvar values = {\r\n\t\t\trecord_id: showEditRecordDataModal.id,\r\n\t\t\tdate: event.target.record_date.value,\r\n\t\t\tpnl: pnlVal,\r\n\t\t};\r\n\r\n\t\teditMutation.mutate(values);\r\n\t}\r\n\r\n\tuseEffect(() => {\r\n\t\tif (fetchRecordData) {\r\n\t\t\tqueryClient.refetchQueries(\"GetStrategyRecord\");\r\n\t\t\tsetFetchRecordData(false);\r\n\t\t}\r\n\t}, [fetchRecordData]);\r\n\r\n\tuseEffect(() => {\r\n\t\tif (props.fetchRecordDataAfterAdd) {\r\n\t\t\tqueryClient.refetchQueries(\"GetStrategyRecord\");\r\n\t\t\tprops.setFetchRecordDataAfterAdd(false);\r\n\t\t}\r\n\t}, [props.fetchRecordDataAfterAdd]);\r\n\r\n\r\n\tconst { isDarkTheme } = useTheme()\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<div className=\"table-responsive mt-3\">\r\n\t\t\t\t<Form.Group>\r\n\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\tplaceholder=\"Search\"\r\n\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"} mb-2 searchbox-style`}\r\n\t\t\t\t\t\tonChange={handleSearch}\r\n\t\t\t\t\t\trequired\r\n\t\t\t\t\t/>\r\n\t\t\t\t</Form.Group>\r\n\t\t\t\t<DataTable\r\n\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\tdata={tableDataFormatted}\r\n\t\t\t\t\tpagination\r\n\t\t\t\t\tpaginationPerPage={10}\r\n\t\t\t\t\tstriped={!isDarkTheme}\r\n\t\t\t\t\ttheme={getTheme()}\r\n\t\t\t\t\thighlightOnHover\r\n\t\t\t\t\tnoHeader\r\n\t\t\t\t/>\r\n\t\t\t\t{showDeleteRecordDataModal.status && (\r\n\t\t\t\t\t<Modal show={true} animation={true} size=\"md\" className={`${isDarkTheme && \"dark-modal\"} mt-5`}>\r\n\t\t\t\t\t\t<Modal.Header className=\"text-center py-2\">\r\n\t\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t\t<h3 className=\"mb-0\">Delete Record</h3>\r\n\t\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t\t<Modal.Body className=\"p-10\">\r\n\t\t\t\t\t\t\tDo you want to delete this Record ?\r\n\t\t\t\t\t\t</Modal.Body>\r\n\t\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\tsetShowDeleteRecordDataModal({ status: false, id: \"\" })\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tvariant=\"outline-primary\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"mx-2 px-3\"\r\n\t\t\t\t\t\t\t\t\tonClick={() => DeleteRecordData(showDeleteRecordDataModal.id)}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tYes\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t\t</Modal>\r\n\t\t\t\t)}\r\n\t\t\t\t{showEditRecordDataModal.status && (\r\n\t\t\t\t\t<Modal show={true} animation={true} size=\"md\" className={`${isDarkTheme && \"dark-modal\"} mt-5`}>\r\n\t\t\t\t\t\t<Form onSubmit={handleEditRecordSubmit}>\r\n\t\t\t\t\t\t\t<Modal.Header className=\"text-center py-2\">\r\n\t\t\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t\t\t<h3 className=\"mb-0\">Edit Record</h3>\r\n\t\t\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t\t\t<Modal.Body className=\"p-10\">\r\n\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t<Form.Label className={`${isDarkTheme && \"dark-text\"}`}>Date : (mm/dd/yyyy)</Form.Label>\r\n\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"}`}\r\n\t\t\t\t\t\t\t\t\t\ttype=\"date\"\r\n\t\t\t\t\t\t\t\t\t\tname=\"record_date\"\r\n\t\t\t\t\t\t\t\t\t\tdefaultValue={showEditRecordDataModal.date}\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => e.target.value}\r\n\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t<Form.Label className={`${isDarkTheme && \"dark-text\"}`}>Pnl</Form.Label>\r\n\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"}`}\r\n\t\t\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\t\t\tname=\"pnl\"\r\n\t\t\t\t\t\t\t\t\t\tdefaultValue={showEditRecordDataModal.pnl}\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => e.target.value}\r\n\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t</Modal.Body>\r\n\t\t\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\t\tsetShowEditRecordDataModal({\r\n\t\t\t\t\t\t\t\t\t\t\t\tstatus: false,\r\n\t\t\t\t\t\t\t\t\t\t\t\tid: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tdate: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpnl: 0,\r\n\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<Button type=\"submit\" className=\"mx-2 px-3\">\r\n\t\t\t\t\t\t\t\t\t\tYes\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t\t\t</Form>\r\n\t\t\t\t\t</Modal>\r\n\t\t\t\t)}\r\n\t\t\t\t{apiResponseModal.show && (\r\n\t\t\t\t\t<ResponseModal\r\n\t\t\t\t\t\tres={apiResponseModal.res}\r\n\t\t\t\t\t\tsetApiResponseModal={setApiResponseModal}\r\n\t\t\t\t\t\tmsg={apiResponseModal.res}\r\n\t\t\t\t\t/>\r\n\t\t\t\t)}\r\n\t\t\t</div>\r\n\t\t</>\r\n\t);\r\n}\r\n\r\nexport default StrategyRecordTable;\r\n", "import React, { useEffect, useState } from \"react\";\r\nimport \"../userpages.css\";\r\nimport { Button, Form, Modal, Tab, Tabs } from \"react-bootstrap\";\r\nimport StrategyOrdersTable from \"../../components/table/StrategyOrdersTable\";\r\nimport StrategyRecordTable from \"../../components/table/StrategyRecordTable\";\r\nimport {\r\n\tAddStrategyRecordData,\r\n\tGetStrategyName,\r\n} from \"../../../services/backendServices\";\r\nimport { useMutation } from \"react-query\";\r\nimport ResponseModal from \"../../components/modal/ResponseModal\";\r\nimport { useParams } from \"react-router-dom\";\r\n\r\nimport cogoToast from \"cogo-toast\";\r\nimport { useTheme } from \"../../context/ThemeContext\";\r\n\r\nfunction StrategyData() {\r\n\tconst { strategy_id } = useParams();\r\n\tconst [showAddOrdersDataModal, setShowAddOrdersDataModal] = useState(false);\r\n\tconst [showAddRecordDataModal, setShowAddRecordDataModal] = useState(false);\r\n\tconst [apiResponseModal, setApiResponseModal] = useState({\r\n\t\tshow: false,\r\n\t\tres: {},\r\n\t}); // Modal : on API Response\r\n\tconst [fetchRecordDataAfterAdd, setFetchRecordDataAfterAdd] = useState(false);\r\n\tconst [fetchOrdersDataAfterAdd, setFetchOrdersDataAfterAdd] = useState(false);\r\n\r\n\tconst [selectedTab, setSelectedTab] = useState(\"records\");\r\n\tconst handleTabSelect = (eventKey) => {\r\n\t\tsetSelectedTab(eventKey);\r\n\t};\r\n\r\n\tconst [strategyName, setStrategyName] = useState();\r\n\r\n\tuseEffect(() => {\r\n\t\tif (strategy_id) {\r\n\t\t\tGetStrategyName(strategy_id)\r\n\t\t\t\t.then((res) => {\r\n\t\t\t\t\tif (res.status) {\r\n\t\t\t\t\t\tsetStrategyName(res.data);\r\n\t\t\t\t\t\treturn res;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((e) => {\r\n\t\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t\t});\r\n\t\t}\r\n\t}, []);\r\n\r\n\t// API CALL to Add Strategy Records\r\n\tconst AddRecordMutation = useMutation(\"AddStrategyRecordData\", (values) =>\r\n\t\tAddStrategyRecordData(values)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tsetFetchRecordDataAfterAdd(true);\r\n\t\t\t\t\tsetShowAddRecordDataModal(false);\r\n\t\t\t\t\tcogoToast.success(`${res.msg}`);\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t})\r\n\t);\r\n\r\n\tfunction handleAddRecordSubmit(event) {\r\n\t\tevent.preventDefault();\r\n\t\tconst pnlVal = Number(event.target.pnl.value);\r\n\t\tvar values = {\r\n\t\t\tstrategy_id: strategy_id,\r\n\t\t\tdate: event.target.record_date.value,\r\n\t\t\tpnl: pnlVal,\r\n\t\t};\r\n\r\n\t\tAddRecordMutation.mutate(values);\r\n\t}\r\n\r\n\tfunction handleAddOrdersSubmit(event) {\r\n\t\tevent.preventDefault();\r\n\t\tvar values = {\r\n\t\t\tid: strategy_id,\r\n\t\t\ttype: \"order\",\r\n\t\t\ttrading_symbol: event.target.trading_symbol.value,\r\n\t\t\texchange: event.target.exchange.value,\r\n\t\t\tquantity: event.target.quantity.value,\r\n\t\t\tprice: event.target.price.value,\r\n\t\t\taction_type: event.target.action_type.value,\r\n\t\t};\r\n\t\t// mutation.mutate(values);\r\n\t}\r\n\r\n\tconst { isDarkTheme } = useTheme()\r\n\treturn (\r\n\t\t<div>\r\n\t\t\t<div className={`card ${isDarkTheme && \"dark-theme-card\"} mb-2`}>\r\n\t\t\t\t<div className=\"card-body py-3\">\r\n\t\t\t\t\t<div className=\"row align-items-center justify-content-between\">\r\n\t\t\t\t\t\t<div className=\"d-flex align-items-center\">\r\n\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\tclassName=\"btn btn-primary p-1\"\r\n\t\t\t\t\t\t\t\tonClick={() => window.close()}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{\" \"}\r\n\t\t\t\t\t\t\t\t<i\r\n\t\t\t\t\t\t\t\t\tclassName=\"mdi mdi-arrow-left-bold m-0\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ fontSize: \"20px\" }}\r\n\t\t\t\t\t\t\t\t></i>{\" \"}\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\tclassName=\"btn btn-primary btn-rounded px-3 py-2 ml-4\"\r\n\t\t\t\t\t\t\t\tonClick={() => setShowAddRecordDataModal(true)}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tAdd Record\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<h3 className=\"mb-0 strategy-data-text\">\r\n\t\t\t\t\t\t\t{strategyName?.toUpperCase()}\r\n\t\t\t\t\t\t</h3>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div className=\"row customtab mt-2\">\r\n\t\t\t\t<div className=\"col-lg-12 grid-margin stretch-card\">\r\n\t\t\t\t\t<div className={`card ${isDarkTheme && \"dark-theme-card\"}`}>\r\n\t\t\t\t\t\t<div className=\"card-body\">\r\n\t\t\t\t\t\t\t<Tabs\r\n\t\t\t\t\t\t\t\tdefaultActiveKey=\"records\"\r\n\t\t\t\t\t\t\t\tid=\"fill-tab-example\"\r\n\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-nav-tabs\"} mb-3`}\r\n\t\t\t\t\t\t\t\tfill\r\n\t\t\t\t\t\t\t\tjustify\r\n\t\t\t\t\t\t\t\tactiveKey={selectedTab}\r\n\t\t\t\t\t\t\t\tonSelect={handleTabSelect}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Tab eventKey=\"records\" title=\"Records\" tabClassName={`${isDarkTheme && \"dark-nav-tabs\"}`}>\r\n\t\t\t\t\t\t\t\t\t<StrategyRecordTable\r\n\t\t\t\t\t\t\t\t\t\tid={strategy_id}\r\n\t\t\t\t\t\t\t\t\t\ttype=\"record\"\r\n\t\t\t\t\t\t\t\t\t\tfetchRecordDataAfterAdd={fetchRecordDataAfterAdd}\r\n\t\t\t\t\t\t\t\t\t\tsetFetchRecordDataAfterAdd={setFetchRecordDataAfterAdd}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Tab>\r\n\t\t\t\t\t\t\t\t<Tab eventKey=\"orders\" title=\"Orders\" tabClassName={`${isDarkTheme && \"dark-nav-tabs\"}`}>\r\n\t\t\t\t\t\t\t\t\t<StrategyOrdersTable\r\n\t\t\t\t\t\t\t\t\t\tid={strategy_id}\r\n\t\t\t\t\t\t\t\t\t\ttype=\"order\"\r\n\t\t\t\t\t\t\t\t\t\tfetchOrdersDataAfterAdd={fetchOrdersDataAfterAdd}\r\n\t\t\t\t\t\t\t\t\t\tsetFetchOrdersDataAfterAdd={setFetchOrdersDataAfterAdd}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Tab>\r\n\t\t\t\t\t\t\t</Tabs>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t{showAddOrdersDataModal && (\r\n\t\t\t\t<Modal show={true} animation={true} size=\"md\" className={`${isDarkTheme && \"dark-modal\"} mt-5`}>\r\n\t\t\t\t\t<Form onSubmit={handleAddOrdersSubmit}>\r\n\t\t\t\t\t\t<Modal.Header className=\"text-center py-2\">\r\n\t\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t\t<h3 className=\"mb-0\">Add Order</h3>\r\n\t\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t\t<Modal.Body className=\"p-10 \">\r\n\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"}`}\r\n\t\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\t\tname=\"trading_symbol\"\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"Symbol\"\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => e.target.value}\r\n\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"}`}\r\n\t\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\t\tname=\"exchange\"\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"Exchange\"\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => e.target.value}\r\n\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"}`}\r\n\t\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\t\tname=\"quantity\"\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"Quantity\"\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => e.target.value}\r\n\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"}`}\r\n\t\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\t\tstep=\"any\"\r\n\t\t\t\t\t\t\t\t\tname=\"price\"\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"Price\"\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => e.target.value}\r\n\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"}`}\r\n\t\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\t\tname=\"action_type\"\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"BUY/SELL\"\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => e.target.value}\r\n\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t</Modal.Body>\r\n\t\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\t\tonClick={() => setShowAddOrdersDataModal(false)}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button type=\"submit\" className=\"mx-2 px-3\">\r\n\t\t\t\t\t\t\t\t\tAdd\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t\t</Form>\r\n\t\t\t\t</Modal>\r\n\t\t\t)}\r\n\t\t\t{showAddRecordDataModal && (\r\n\t\t\t\t<Modal show={true} animation={true} size=\"md\" className={`${isDarkTheme && \"dark-modal\"} mt-5`}>\r\n\t\t\t\t\t<Form onSubmit={handleAddRecordSubmit}>\r\n\t\t\t\t\t\t<Modal.Header className=\"text-center py-2\">\r\n\t\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t\t<h3 className=\"mb-0\">Add Record</h3>\r\n\t\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t\t<Modal.Body className=\"p-10 \">\r\n\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"}`}\r\n\t\t\t\t\t\t\t\t\ttype=\"date\"\r\n\t\t\t\t\t\t\t\t\tname=\"record_date\"\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => e.target.value}\r\n\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"}`}\r\n\t\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\t\tname=\"pnl\"\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"P&L\"\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => e.target.value}\r\n\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t</Modal.Body>\r\n\t\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\t\tonClick={() => setShowAddRecordDataModal(false)}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button type=\"submit\" className=\"mx-2 px-3\">\r\n\t\t\t\t\t\t\t\t\tAdd\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t\t</Form>\r\n\t\t\t\t</Modal>\r\n\t\t\t)}\r\n\t\t\t{apiResponseModal.show && (\r\n\t\t\t\t<ResponseModal\r\n\t\t\t\t\tres={apiResponseModal.res}\r\n\t\t\t\t\tsetApiResponseModal={setApiResponseModal}\r\n\t\t\t\t\tmsg={apiResponseModal.res}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t</div>\r\n\t);\r\n}\r\n\r\nexport default StrategyData;\r\n", "import React from \"react\";\r\nimport { createTheme } from \"react-data-table-component\";\r\n\r\nexport const customStylesForSelect = {\r\n\toption: (provided, state) => ({\r\n\t\t...provided,\r\n\t\tbackgroundColor: state.isSelected ? \"blue\" : \"white\", // Change the background color as desired\r\n\t\tcolor: state.isSelected ? \"white\" : \"black\", // Change the text color as desired\r\n\t}),\r\n};\r\nexport const hoverEffectOnSelect = {\r\n\toption: (base, state) => ({\r\n\t\t...customStylesForSelect.option(base, state),\r\n\t\t\"&:hover\": {\r\n\t\t\tbackgroundColor: \"lightgray\", // Change the hover background color\r\n\t\t\tcolor: \"black\", // Change the hover text color\r\n\t\t},\r\n\t}),\r\n};\r\n\r\nexport const createDataTableTheme = () => {\r\n\tcreateTheme(\r\n\t\t\"solarized\",\r\n\t\t{\r\n\t\t\tbackground: {\r\n\t\t\t\tdefault: \"transparent\",\r\n\t\t\t},\r\n\t\t\taction: {\r\n\t\t\t\tbutton: \"rgba(0,0,0,.54)\",\r\n\t\t\t\thover: \"rgba(0,0,0,.08)\",\r\n\t\t\t\tdisabled: \"rgba(0,0,0,.12)\",\r\n\t\t\t},\r\n\t\t},\r\n\t\t\"dark\"\r\n\t);\r\n};\r\n\r\nexport function getTransactionType(transaction) {\r\n\tlet a_type = transaction.charAt(0);\r\n\tlet is_buy =\r\n\t\ta_type === \"B\" ||\r\n\t\ta_type === \"b\" ||\r\n\t\ta_type === \"buy\" ||\r\n\t\ta_type === \"BUY\" ||\r\n\t\ta_type === \"Buy\";\r\n\tlet is_sell =\r\n\t\ta_type === \"S\" ||\r\n\t\ta_type === \"s\" ||\r\n\t\ta_type === \"sell\" ||\r\n\t\ta_type === \"SELL\" ||\r\n\t\ta_type === \"Sell\";\r\n\tif (is_buy) {\r\n\t\treturn <label className=\"badge badge-outline-warning\">BUY</label>;\r\n\t} else if (is_sell) {\r\n\t\treturn <label className=\"badge badge-outline-primary\">SELL</label>;\r\n\t} else {\r\n\t\treturn (\r\n\t\t\ttransaction && (\r\n\t\t\t\t<label className=\"badge badge-outline-danger\">\r\n\t\t\t\t\t{transaction.toUpperCase()}\r\n\t\t\t\t</label>\r\n\t\t\t)\r\n\t\t);\r\n\t}\r\n}\r\n\r\nexport function getPnlComponent(pnlVal) {\r\n\tpnlVal = parseFloat(pnlVal || 0).toFixed(2);\r\n\tif (pnlVal < 0) {\r\n\t\treturn (\r\n\t\t\t'<span class=\"text-danger\"> ' +\r\n\t\t\tpnlVal +\r\n\t\t\t' <i class=\"mdi mdi-arrow-down\"></i></span>'\r\n\t\t);\r\n\t} else if (pnlVal === 0 || pnlVal === null) {\r\n\t\tpnlVal = 0;\r\n\t\treturn \"<span> \" + pnlVal + \"</span>\";\r\n\t}\r\n\treturn (\r\n\t\t'<span class=\"text-success\"> ' +\r\n\t\tpnlVal +\r\n\t\t' <i class=\"mdi mdi-arrow-up\"></i></span>'\r\n\t);\r\n}\r\n\r\nexport function getSubPnlComponent(pnlVal) {\r\n\tpnlVal = parseFloat(pnlVal || 0).toFixed(2);\r\n\tif (pnlVal < 0) {\r\n\t\treturn (\r\n\t\t\t<span className=\"text-danger\">\r\n\t\t\t\t{\" \"}\r\n\t\t\t\t{pnlVal} <i className=\"mdi mdi-arrow-down\"></i>\r\n\t\t\t</span>\r\n\t\t);\r\n\t} else if (pnlVal === 0 || pnlVal === null) {\r\n\t\tpnlVal = 0;\r\n\t\treturn <span> {pnlVal}</span>;\r\n\t}\r\n\treturn (\r\n\t\t<span className=\"text-success\">\r\n\t\t\t{\" \"}\r\n\t\t\t{pnlVal} <i className=\"mdi mdi-arrow-up\"></i>\r\n\t\t</span>\r\n\t);\r\n}\r\n\r\nexport function getSubTransactionType(transaction) {\r\n\tlet a_type = transaction.charAt(0);\r\n\tlet is_buy =\r\n\t\ta_type === \"B\" ||\r\n\t\ta_type === \"b\" ||\r\n\t\ta_type === \"buy\" ||\r\n\t\ta_type === \"BUY\" ||\r\n\t\ta_type === \"Buy\";\r\n\tif (is_buy) {\r\n\t\treturn '<label className=\"badge badge-outline-warning\">BUY</label>';\r\n\t} else {\r\n\t\treturn '<label className=\"badge badge-outline-primary\">SELL</label>';\r\n\t}\r\n}\r\n\r\nexport function getOrderStatus(status, reason) {\r\n\tif (status.charAt(0) === \"C\" || status.charAt(0) === \"c\") {\r\n\t\treturn (\r\n\t\t\t<label\r\n\t\t\t\tclassName=\"badge badge-outline-success\"\r\n\t\t\t\tdata-toggle=\"tooltip\"\r\n\t\t\t\tdata-placement=\"top\"\r\n\t\t\t\ttitle={reason}\r\n\t\t\t>\r\n\t\t\t\tCOMPLETED\r\n\t\t\t</label>\r\n\t\t);\r\n\t} else if (status.charAt(0) === \"R\" || status.charAt(0) === \"r\") {\r\n\t\treturn (\r\n\t\t\t<label\r\n\t\t\t\tclassName=\"badge badge-outline-danger\"\r\n\t\t\t\tdata-toggle=\"tooltip\"\r\n\t\t\t\tdata-placement=\"top\"\r\n\t\t\t\ttitle={reason}\r\n\t\t\t>\r\n\t\t\t\tREJECTED\r\n\t\t\t</label>\r\n\t\t);\r\n\t\t//} else if (status.charAt(0) === \"O\" || status.charAt(0) === \"o\") {\r\n\t\t//   return (\r\n\t\t//     <label\r\n\t\t//       className=\"badge badge-outline-secondary\"\r\n\t\t//       data-toggle=\"tooltip\"\r\n\t\t//       data-placement=\"top\"\r\n\t\t//       title={reason}\r\n\t\t//     >\r\n\t\t//       OPEN\r\n\t\t//     </label>\r\n\t\t//   );\r\n\t} else {\r\n\t\treturn (\r\n\t\t\t<label\r\n\t\t\t\tclassName=\"badge badge-outline-info\"\r\n\t\t\t\tdata-toggle=\"tooltip\"\r\n\t\t\t\tdata-placement=\"top\"\r\n\t\t\t\ttitle={reason}\r\n\t\t\t>\r\n\t\t\t\t{status}\r\n\t\t\t</label>\r\n\t\t);\r\n\t}\r\n}\r\n\r\nexport function getConvertedAmount(amount) {\r\n\tif (isNaN(amount)) {\r\n\t\treturn \"...\";\r\n\t}\r\n\tif (amount < 1000) {\r\n\t\treturn amount.toString();\r\n\t}\r\n\tif (amount >= 1000 && amount < 100000) {\r\n\t\treturn (amount / 1000).toFixed(2) + \" K\";\r\n\t}\r\n\tif (amount >= 100000 && amount < 10000000) {\r\n\t\treturn (amount / 100000).toFixed(2) + \" L\";\r\n\t}\r\n\tif (amount >= 10000000) {\r\n\t\treturn (amount / 10000000).toFixed(2) + \" Cr\";\r\n\t}\r\n}\r\n\r\n\r\n\r\n// Function to create dark theme for DataTable\r\nexport const createDataTableDarkTheme = () => {\r\n\tcreateTheme(\r\n\t\t\"solarized\",\r\n\t\t{\r\n\t\t\tbackground: {\r\n\t\t\t\tdefault: \"transparent\",\r\n\t\t\t},\r\n\t\t\taction: {\r\n\t\t\t\tbutton: \"rgba(0,0,0,.54)\",\r\n\t\t\t\thover: \"rgba(0,0,0,.08)\",\r\n\t\t\t\tdisabled: \"rgba(0,0,0,.12)\",\r\n\t\t\t},\r\n\t\t},\r\n\t\t\"dark\"\r\n\t);\r\n}\r\n\r\n// Function to reset DataTable theme to default values\r\nexport const resetDataTableTheme = () => {\r\n\tcreateTheme(\r\n\t\t\"resesolarized\",\r\n\t\t{\r\n\t\t\tbackground: {\r\n\t\t\t\tdefault: \"#fff\",\r\n\t\t\t},\r\n\t\t\taction: {\r\n\t\t\t\tbutton: \"rgba(0,0,0,.54)\",\r\n\t\t\t\thover: \"rgba(0,0,0,.08)\",\r\n\t\t\t\tdisabled: \"rgba(0,0,0,.12)\",\r\n\t\t\t},\r\n\t\t},\r\n\t\t\"light\"\r\n\t);\r\n}\r\n\r\n// theme color for DATATABLE package\r\nexport const getTheme = () => {\r\n\tconst isDarkTheme = localStorage.getItem(\"isDarkTheme\") === \"true\";\r\n\r\n\tif (isDarkTheme) {\r\n\t\tcreateDataTableDarkTheme();\r\n\t\treturn \"solarized\";\r\n\t} else {\r\n\t\tresetDataTableTheme(); // Reset the theme when switching to light theme\r\n\t\treturn \"resesolarized\";\r\n\t}\r\n};\r\n\r\n", "import React,{useState,useEffect}from\"react\";import ReactDOM from\"react-dom\";import{string,oneOfType,node,bool,func,number,shape}from\"prop-types\";var __assign=function(){return(__assign=Object.assign||function(t){for(var n,e=1,o=arguments.length;e<o;e++)for(var i in n=arguments[e])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)};function __spreadArrays(){for(var t=0,n=0,e=arguments.length;n<e;n++)t+=arguments[n].length;var o=Array(t),i=0;for(n=0;n<e;n++)for(var a=arguments[n],r=0,c=a.length;r<c;r++,i++)o[i]=a[r];return o}var success=function(t){return React.createElement(\"svg\",__assign({viewBox:\"0 0 426.667 426.667\",width:18,height:18},t),React.createElement(\"path\",{d:\"M213.333 0C95.518 0 0 95.514 0 213.333s95.518 213.333 213.333 213.333c117.828 0 213.333-95.514 213.333-213.333S331.157 0 213.333 0zm-39.134 322.918l-93.935-93.931 31.309-31.309 62.626 62.622 140.894-140.898 31.309 31.309-172.203 172.207z\",fill:\"#6ac259\"}))},warn=function(t){return React.createElement(\"svg\",__assign({viewBox:\"0 0 310.285 310.285\",width:18,height:18},t),React.createElement(\"path\",{d:\"M264.845 45.441C235.542 16.139 196.583 0 155.142 0 113.702 0 74.743 16.139 45.44 45.441 16.138 74.743 0 113.703 0 155.144c0 41.439 16.138 80.399 45.44 109.701 29.303 29.303 68.262 45.44 109.702 45.44s80.399-16.138 109.702-45.44c29.303-29.302 45.44-68.262 45.44-109.701.001-41.441-16.137-80.401-45.439-109.703zm-132.673 3.895a12.587 12.587 0 0 1 9.119-3.873h28.04c3.482 0 6.72 1.403 9.114 3.888 2.395 2.485 3.643 5.804 3.514 9.284l-4.634 104.895c-.263 7.102-6.26 12.933-13.368 12.933H146.33c-7.112 0-13.099-5.839-13.345-12.945L128.64 58.594c-.121-3.48 1.133-6.773 3.532-9.258zm23.306 219.444c-16.266 0-28.532-12.844-28.532-29.876 0-17.223 12.122-30.211 28.196-30.211 16.602 0 28.196 12.423 28.196 30.211.001 17.591-11.456 29.876-27.86 29.876z\",fill:\"#FFDA44\"}))},loading=function(t){return React.createElement(\"div\",__assign({className:\"ct-icon-loading\"},t))},info=function(t){return React.createElement(\"svg\",__assign({viewBox:\"0 0 23.625 23.625\",width:18,height:18},t),React.createElement(\"path\",{d:\"M11.812 0C5.289 0 0 5.289 0 11.812s5.289 11.813 11.812 11.813 11.813-5.29 11.813-11.813S18.335 0 11.812 0zm2.459 18.307c-.608.24-1.092.422-1.455.548a3.838 3.838 0 0 1-1.262.189c-.736 0-1.309-.18-1.717-.539s-.611-.814-.611-1.367c0-.215.015-.435.045-.659a8.23 8.23 0 0 1 .147-.759l.761-2.688c.067-.258.125-.503.171-.731.046-.23.068-.441.068-.633 0-.342-.071-.582-.212-.717-.143-.135-.412-.201-.813-.201-.196 0-.398.029-.605.09-.205.063-.383.12-.529.176l.201-.828c.498-.203.975-.377 1.43-.521a4.225 4.225 0 0 1 1.29-.218c.731 0 1.295.178 1.692.53.395.353.594.812.594 1.376 0 .117-.014.323-.041.617a4.129 4.129 0 0 1-.152.811l-.757 2.68a7.582 7.582 0 0 0-.167.736 3.892 3.892 0 0 0-.073.626c0 .356.079.599.239.728.158.129.435.194.827.194.185 0 .392-.033.626-.097.232-.064.4-.121.506-.17l-.203.827zm-.134-10.878a1.807 1.807 0 0 1-1.275.492c-.496 0-.924-.164-1.28-.492a1.57 1.57 0 0 1-.533-1.193c0-.465.18-.865.533-1.196a1.812 1.812 0 0 1 1.28-.497c.497 0 .923.165 1.275.497.353.331.53.731.53 1.196 0 .467-.177.865-.53 1.193z\",fill:\"#006DF0\"}))},error=function(t){return React.createElement(\"svg\",__assign({viewBox:\"0 0 51.976 51.976\",width:18,height:18},t),React.createElement(\"path\",{d:\"M44.373 7.603c-10.137-10.137-26.632-10.138-36.77 0-10.138 10.138-10.137 26.632 0 36.77s26.632 10.138 36.77 0c10.137-10.138 10.137-26.633 0-36.77zm-8.132 28.638a2 2 0 0 1-2.828 0l-7.425-7.425-7.778 7.778a2 2 0 1 1-2.828-2.828l7.778-7.778-7.425-7.425a2 2 0 1 1 2.828-2.828l7.425 7.425 7.071-7.071a2 2 0 1 1 2.828 2.828l-7.071 7.071 7.425 7.425a2 2 0 0 1 0 2.828z\",fill:\"#D80027\"}))},Icons={success:success,warn:warn,loading:loading,info:info,error:error},colors={success:\"#6EC05F\",info:\"#1271EC\",warn:\"#FED953\",error:\"#D60A2E\",loading:\"#0088ff\"},Toast=function(t){var n,e,o,i,a=\"margin\"+((t.position||\"top-center\").includes(\"bottom\")?\"Bottom\":\"Top\"),r=[\"ct-toast\",t.onClick?\" ct-cursor-pointer\":\"\",\"ct-toast-\"+t.type].join(\" \"),c=((null===(e=t.bar)||void 0===e?void 0:e.size)||\"3px\")+\" \"+((null===(o=t.bar)||void 0===o?void 0:o.style)||\"solid\")+\" \"+((null===(i=t.bar)||void 0===i?void 0:i.color)||colors[t.type]),s=Icons[t.type],l=useState(((n={opacity:0})[a]=-15,n)),d=l[0],u=l[1],f=__assign({paddingLeft:t.heading?25:void 0,minHeight:t.heading?50:void 0,borderLeft:c},d),p=function(){var n;u(((n={opacity:0})[a]=\"-15px\",n)),setTimeout((function(){t.onHide(t.id,t.position)}),300)};useEffect((function(){var n,e=setTimeout((function(){var t;u(((t={opacity:1})[a]=\"15px\",t))}),50);return 0!==t.hideAfter&&(n=setTimeout((function(){p()}),1e3*t.hideAfter)),function(){clearTimeout(e),n&&clearTimeout(n)}}),[]),useEffect((function(){t.show||p()}),[t.show]);var g={tabIndex:0,onClick:t.onClick,onKeyPress:function(n){13===n.keyCode&&t.onClick(n)}};return React.createElement(\"div\",__assign({className:r,role:t.role?t.role:\"status\",style:f},t.onClick?g:{}),t.renderIcon?t.renderIcon():React.createElement(s,null),React.createElement(\"div\",{className:t.heading?\"ct-text-group-heading\":\"ct-text-group\"},t.heading&&React.createElement(\"h4\",{className:\"ct-heading\"},t.heading),React.createElement(\"div\",{className:\"ct-text\"},t.text)))};Toast.propTypes={type:string.isRequired,text:oneOfType([string,node]).isRequired,show:bool,onHide:func,id:oneOfType([string,number]),hideAfter:number,heading:string,position:string,renderIcon:func,bar:shape({}),onClick:func,role:string},Toast.defaultProps={id:void 0,show:!0,onHide:void 0,hideAfter:3,heading:void 0,position:\"top-center\",renderIcon:void 0,bar:{},onClick:void 0,role:\"status\"};var camelCase=function(t){return t.replace(/-([a-z])/g,(function(t){return t[1].toUpperCase()}))},defaultToasts={topLeft:[],topCenter:[],topRight:[],bottomLeft:[],bottomCenter:[],bottomRight:[]},ToastContainer=function(t){var n=t.toast,e=t.hiddenID,o=useState(defaultToasts),i=o[0],a=o[1];useEffect((function(){n&&a((function(t){var e,o=camelCase(n.position||\"top-center\");return __assign(__assign({},t),((e={})[o]=__spreadArrays(t[o],[n]),e))}))}),[n]);var r=function(t,n){a((function(e){var o,i=camelCase(n||\"top-center\");return __assign(__assign({},e),((o={})[i]=e[i].filter((function(n){return n.id!==t})),o))}))},c=[\"Left\",\"Center\",\"Right\"];return React.createElement(React.Fragment,null,[\"top\",\"bottom\"].map((function(t){return React.createElement(\"div\",{key:\"row_\"+t,className:\"ct-row\"},c.map((function(n){var o=\"\"+t+n,a=[\"ct-group\",\"bottom\"===t?\"ct-flex-bottom\":\"\"].join(\" \");return React.createElement(\"div\",{key:o,className:a},i[o].map((function(t){return React.createElement(Toast,__assign({key:o+\"_\"+t.id},t,{id:t.id,text:t.text,type:t.type,onClick:t.onClick,hideAfter:t.hideAfter,show:e!==t.id,onHide:r}))})))})))})))};function styleInject(t,n){void 0===n&&(n={});var e=n.insertAt;if(t&&\"undefined\"!=typeof document){var o=document.head||document.getElementsByTagName(\"head\")[0],i=document.createElement(\"style\");i.type=\"text/css\",\"top\"===e&&o.firstChild?o.insertBefore(i,o.firstChild):o.appendChild(i),i.styleSheet?i.styleSheet.cssText=t:i.appendChild(document.createTextNode(t))}}ToastContainer.propTypes={toast:shape({}),hiddenID:number},ToastContainer.defaultProps={toast:void 0,hiddenID:void 0};var css=\"#ct-container {\\n\\tposition: fixed;\\n\\twidth: 100%;\\n\\theight: 100vh;\\n\\ttop: 0px;\\n\\tleft: 0px;\\n\\tz-index: 2000;\\n\\tdisplay: flex;\\n\\tflex-direction: column;\\n\\tjustify-content: space-between;\\n\\tpointer-events: none;\\n}\\n\\n.ct-row {\\n\\tdisplay: flex;\\n\\tjustify-content: space-between;\\n}\\n\\n.ct-group {\\n\\tflex: 1;\\n\\tmargin: 10px 20px;\\n\\tdisplay: flex;\\n\\tflex-direction: column;\\n\\talign-items: center;\\n}\\n\\n.ct-group:first-child {\\n\\talign-items: flex-start;\\n}\\n\\n.ct-group:last-child {\\n\\talign-items: flex-end;\\n}\\n\\n.ct-flex-bottom {\\n\\tjustify-content: flex-end;\\n}\\n\\n.ct-toast {\\n\\tdisplay: flex;\\n\\tjustify-content: center;\\n\\talign-items: center;\\n\\tpadding: 12px 20px;\\n\\tbackground-color: #fff;\\n\\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n\\tcolor: #000;\\n\\tborder-radius: 4px;\\n\\tmargin: 0px;\\n\\topacity: 1;\\n\\ttransition: 0.3s all ease-in-out;\\n\\tmin-height: 45px;\\n\\tpointer-events: all;\\n}\\n\\n.ct-toast:focus {\\n\\toutline: none;\\n}\\n\\n.ct-toast svg {\\n\\tmin-width: 18px;\\n}\\n\\n.ct-cursor-pointer {\\n\\tcursor: pointer;\\n}\\n\\n.ct-icon-loading {\\n\\tdisplay: inline-block;\\n\\twidth: 20px;\\n\\theight: 20px;\\n}\\n\\n.ct-icon-loading:after {\\n\\tcontent: ' ';\\n\\tdisplay: block;\\n\\twidth: 14px;\\n\\theight: 14px;\\n\\tmargin: 1px;\\n\\tborder-radius: 50%;\\n\\tborder: 2px solid #0088ff;\\n\\tborder-color: #0088ff transparent #0088ff transparent;\\n\\tanimation: ct-icon-loading 1.2s linear infinite;\\n}\\n\\n@keyframes ct-icon-loading {\\n\\t0% {\\n\\t\\ttransform: rotate(0deg);\\n\\t}\\n\\t100% {\\n\\t\\ttransform: rotate(360deg);\\n\\t}\\n}\\n\\n.ct-text-group {\\n\\tmargin-left: 15px;\\n}\\n\\n.ct-text-group-heading {\\n\\tmargin-left: 25px;\\n}\\n\\n.ct-heading {\\n\\tfont-size: 18px;\\n\\tmargin: 0px;\\n\\tmargin-bottom: 5px;\\n}\\n\\n.ct-text {\\n\\tfont-size: 14px;\\n}\\n\\n@media (max-width: 768px) {\\n\\t.ct-row {\\n\\t\\tjustify-content: flex-start;\\n\\t\\tflex-direction: column;\\n\\t\\tmargin: 7px 0px;\\n\\t}\\n\\n\\t.ct-group {\\n\\t\\tflex: none;\\n\\t\\tmargin: 0px;\\n\\t}\\n\\n\\t.ct-toast {\\n\\t\\tmargin: 8px 15px;\\n\\t\\twidth: initial;\\n\\t}\\n}\\n\";styleInject(css);var ctToastCount=0,cogoToast=function(t,n){var e,o,i=document.getElementById((null===(e=n)||void 0===e?void 0:e.toastContainerID)||\"ct-container\");i||((i=document.createElement(\"div\")).id=\"ct-container\",document.body.appendChild(i)),ctToastCount+=1;var a=1e3*(void 0===(null===(o=n)||void 0===o?void 0:o.hideAfter)?3:n.hideAfter),r=__assign({id:ctToastCount,text:t},n);ReactDOM.render(React.createElement(ToastContainer,{toast:r}),i);var c=new Promise((function(t){setTimeout((function(){t()}),a)}));return c.hide=function(){ReactDOM.render(React.createElement(ToastContainer,{hiddenID:r.id}),i)},c};cogoToast.success=function(t,n){return cogoToast(t,__assign(__assign({},n),{type:\"success\"}))},cogoToast.warn=function(t,n){return cogoToast(t,__assign(__assign({},n),{type:\"warn\"}))},cogoToast.info=function(t,n){return cogoToast(t,__assign(__assign({},n),{type:\"info\"}))},cogoToast.error=function(t,n){return cogoToast(t,__assign(__assign({},n),{type:\"error\"}))},cogoToast.loading=function(t,n){return cogoToast(t,__assign(__assign({},n),{type:\"loading\"}))};export default cogoToast;export{Toast};"], "names": ["StrategyOrdersTable", "props", "console", "log", "queryClient", "useQueryClient", "apiResponseModal", "setApiResponseModal", "useState", "show", "res", "fetchOrdersData", "setFetchOrdersData", "tableDataFormatted", "setTableDataFormatted", "rawUserMap", "useMemo", "Map", "setTableData", "tableData", "rowTableData", "map", "obj", "index", "push", "getRowFormatForTable", "ind", "trading_symbol", "exchange", "quantity", "price", "action_type", "getTransactionType", "useQuery", "GetOrdersData", "id", "format", "Date", "then", "status", "jsonArr", "Object", "values", "data", "set", "catch", "e", "columns", "name", "selector", "row", "sortable", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "wrap", "sortFunction", "actionTypeSortFunction", "a", "b", "omit", "useEffect", "refetchQueries", "fetchOrdersDataAfterAdd", "setFetchOrdersDataAfterAdd", "selectedDate", "setSelectedDate", "isDarkTheme", "useTheme", "_jsxs", "_Fragment", "children", "className", "_jsx", "Form", "Group", "controlId", "concat", "DatePicker", "selected", "onChange", "date", "dateFormat", "<PERSON><PERSON>", "type", "onClick", "handleShowOrder", "Control", "placeholder", "search_val", "target", "value", "filteredData", "_value$trading_symbol", "_value$exchange", "_value$action_type", "toLowerCase", "includes", "toString", "required", "DataTable", "pagination", "paginationPerPage", "striped", "highlightOnHover", "theme", "getTheme", "<PERSON><PERSON><PERSON><PERSON>", "ResponseModal", "msg", "StrategyRecordTable", "showDeleteRecordDataModal", "setShowDeleteRecordDataModal", "showEditRecordDataModal", "setShowEditRecordDataModal", "pnl", "fetchRecordData", "setFetchRecordData", "record_date", "slice", "getSubPnlComponent", "action", "GetStrategyRecord", "pnlSortFunction", "DeleteMutation", "useMutation", "RemoveStrategyRecord", "cogoToast", "success", "editMutation", "UpdateStrategyRecordData", "fetchRecordDataAfterAdd", "setFetchRecordDataAfterAdd", "_value$date", "_value$date$slice", "Modal", "animation", "size", "Header", "Title", "Body", "Footer", "variant", "DeleteRecordData", "mutate", "delete", "onSubmit", "event", "preventDefault", "pnlVal", "Number", "record_id", "Label", "defaultValue", "StrategyData", "strategy_id", "useParams", "showAddOrdersDataModal", "setShowAddOrdersDataModal", "showAddRecordDataModal", "setShowAddRecordDataModal", "selectedTab", "setSelectedTab", "strategyName", "setStrategyName", "GetStrategyName", "AddRecordMutation", "AddStrategyRecordData", "window", "close", "style", "fontSize", "toUpperCase", "Tabs", "defaultActiveKey", "fill", "justify", "active<PERSON><PERSON>", "onSelect", "eventKey", "Tab", "title", "tabClassName", "step", "customStylesForSelect", "option", "provided", "state", "backgroundColor", "isSelected", "color", "hoverEffectOnSelect", "base", "transaction", "a_type", "char<PERSON>t", "is_sell", "parseFloat", "toFixed", "getOrderStatus", "reason", "getConvertedAmount", "amount", "isNaN", "localStorage", "getItem", "createTheme", "background", "default", "button", "hover", "disabled", "__assign", "assign", "t", "n", "o", "arguments", "length", "i", "prototype", "hasOwnProperty", "call", "apply", "this", "Icons", "React", "viewBox", "width", "height", "d", "warn", "loading", "info", "error", "colors", "Toast", "position", "r", "join", "c", "bar", "s", "l", "opacity", "u", "f", "paddingLeft", "heading", "minHeight", "borderLeft", "p", "setTimeout", "onHide", "hideAfter", "clearTimeout", "g", "tabIndex", "onKeyPress", "keyCode", "role", "renderIcon", "text", "propTypes", "string", "isRequired", "oneOfType", "node", "bool", "func", "number", "shape", "defaultProps", "camelCase", "replace", "defaultToasts", "topLeft", "topCenter", "topRight", "bottomLeft", "bottomCenter", "bottomRight", "ToastContainer", "toast", "hiddenID", "Array", "__spreadA<PERSON>ys", "filter", "key", "insertAt", "document", "head", "getElementsByTagName", "createElement", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "append<PERSON><PERSON><PERSON>", "styleSheet", "cssText", "createTextNode", "styleInject", "ctToastCount", "getElementById", "toastContainerID", "body", "ReactDOM", "Promise", "hide"], "sourceRoot": ""}
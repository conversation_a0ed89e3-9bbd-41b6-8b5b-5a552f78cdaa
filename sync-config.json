{"hostinger": {"host": "***************", "username": "u352667016", "port": 65002, "baseRemotePath": "/domains", "localBasePath": "./domains", "excludePatterns": ["node_modules/", ".git/", "*.log", ".env", "temp/", "cache/", ".DS_Store", "Thumbs.db", "*.tmp", "*.bak"]}, "domains": {}, "sync": {"watchEnabled": true, "autoUpload": true, "backupBeforeSync": true, "syncInterval": 3000, "maxConcurrentUploads": 5, "retryAttempts": 3}, "operations": {"enableDelete": true, "enableAdd": true, "enableModify": true, "confirmDestructive": true}}
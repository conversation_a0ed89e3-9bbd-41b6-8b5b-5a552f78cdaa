#!/usr/bin/env node

const HostingerAccountManager = require('./hostinger-account-manager');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

class SyncRecovery {
    constructor() {
        this.manager = new HostingerAccountManager();
    }

    async findUnsyncedFiles(domainName) {
        const domainConfig = this.manager.config.domains[domainName];
        if (!domainConfig) {
            console.log(chalk.red(`Domain ${domainName} not found`));
            return [];
        }

        await this.manager.connect();

        console.log(chalk.blue(`🔍 Checking for unsynced files in ${domainName}...`));

        const localFiles = await this.getLocalFiles(domainConfig.localPath);
        const remoteFiles = await this.getRemoteFiles(domainConfig.remotePath);

        const unsyncedFiles = [];

        for (const localFile of localFiles) {
            const relativePath = path.relative(domainConfig.localPath, localFile);
            const remoteFilePath = path.posix.join(domainConfig.remotePath, relativePath);

            // Check if file exists on remote
            const exists = remoteFiles.includes(remoteFilePath);
            
            if (!exists) {
                // Check file modification time
                const stats = await fs.stat(localFile);
                const lastSync = domainConfig.lastSync ? new Date(domainConfig.lastSync) : new Date(0);
                
                if (stats.mtime > lastSync) {
                    unsyncedFiles.push({
                        localPath: localFile,
                        relativePath: relativePath,
                        remotePath: remoteFilePath,
                        reason: 'missing_on_remote'
                    });
                }
            } else {
                // Check if local file is newer
                try {
                    const localStats = await fs.stat(localFile);
                    const remoteStats = await this.manager.ssh.execCommand(`stat -c %Y "${remoteFilePath}" 2>/dev/null || echo "0"`);
                    const remoteTime = parseInt(remoteStats.stdout.trim()) * 1000;
                    
                    if (localStats.mtime.getTime() > remoteTime) {
                        unsyncedFiles.push({
                            localPath: localFile,
                            relativePath: relativePath,
                            remotePath: remoteFilePath,
                            reason: 'local_newer'
                        });
                    }
                } catch (error) {
                    // If we can't check, assume it needs sync
                    unsyncedFiles.push({
                        localPath: localFile,
                        relativePath: relativePath,
                        remotePath: remoteFilePath,
                        reason: 'check_failed'
                    });
                }
            }
        }

        return unsyncedFiles;
    }

    async getLocalFiles(localPath) {
        const files = [];
        
        async function scanDir(dir) {
            const items = await fs.readdir(dir);
            
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stats = await fs.stat(fullPath);
                
                if (stats.isDirectory()) {
                    // Skip excluded directories
                    if (!item.startsWith('.') && 
                        item !== 'node_modules' && 
                        item !== 'cache' && 
                        item !== 'temp') {
                        await scanDir(fullPath);
                    }
                } else {
                    // Skip excluded files
                    if (!item.startsWith('.') && 
                        !item.endsWith('.log') && 
                        !item.endsWith('.tmp')) {
                        files.push(fullPath);
                    }
                }
            }
        }
        
        await scanDir(localPath);
        return files;
    }

    async getRemoteFiles(remotePath) {
        try {
            const result = await this.manager.ssh.execCommand(`find "${remotePath}" -type f 2>/dev/null || echo ""`);
            return result.stdout.split('\n').filter(line => line.trim());
        } catch (error) {
            console.error(chalk.red('Error getting remote files:'), error.message);
            return [];
        }
    }

    async syncUnsyncedFiles(domainName, unsyncedFiles) {
        console.log(chalk.blue(`🔄 Syncing ${unsyncedFiles.length} unsynced files for ${domainName}...`));

        let successCount = 0;
        let failCount = 0;

        for (const file of unsyncedFiles) {
            try {
                console.log(chalk.cyan(`⬆️  Syncing: ${file.relativePath} (${file.reason})`));
                
                const success = await this.manager.uploadFile(file.localPath, domainName);
                
                if (success) {
                    successCount++;
                    console.log(chalk.green(`✅ Synced: ${file.relativePath}`));
                } else {
                    failCount++;
                    console.log(chalk.red(`❌ Failed: ${file.relativePath}`));
                }
                
                // Small delay to prevent overwhelming the server
                await new Promise(resolve => setTimeout(resolve, 500));
                
            } catch (error) {
                failCount++;
                console.error(chalk.red(`❌ Error syncing ${file.relativePath}:`), error.message);
            }
        }

        console.log(chalk.green(`\n✅ Sync complete: ${successCount} success, ${failCount} failed`));
        return { successCount, failCount };
    }

    async recoverAllDomains() {
        console.log(chalk.cyan('🚀 Starting sync recovery for all domains...'));

        const domains = Object.keys(this.manager.config.domains);
        let totalUnsynced = 0;
        let totalSynced = 0;
        let totalFailed = 0;

        for (const domainName of domains) {
            try {
                console.log(chalk.blue(`\n📂 Checking domain: ${domainName}`));
                
                const unsyncedFiles = await this.findUnsyncedFiles(domainName);
                totalUnsynced += unsyncedFiles.length;

                if (unsyncedFiles.length > 0) {
                    console.log(chalk.yellow(`Found ${unsyncedFiles.length} unsynced files`));
                    
                    const result = await this.syncUnsyncedFiles(domainName, unsyncedFiles);
                    totalSynced += result.successCount;
                    totalFailed += result.failCount;
                } else {
                    console.log(chalk.green(`✅ All files in sync`));
                }
                
            } catch (error) {
                console.error(chalk.red(`❌ Error processing ${domainName}:`), error.message);
            }
        }

        console.log(chalk.cyan('\n📊 Recovery Summary:'));
        console.log(`Total domains checked: ${domains.length}`);
        console.log(`Total unsynced files found: ${totalUnsynced}`);
        console.log(`Successfully synced: ${totalSynced}`);
        console.log(`Failed to sync: ${totalFailed}`);

        if (totalFailed === 0) {
            console.log(chalk.green('🎉 All files are now in sync!'));
        } else {
            console.log(chalk.yellow(`⚠️  ${totalFailed} files still need attention`));
        }
    }

    async recoverDomain(domainName) {
        console.log(chalk.cyan(`🚀 Starting sync recovery for ${domainName}...`));

        const unsyncedFiles = await this.findUnsyncedFiles(domainName);

        if (unsyncedFiles.length === 0) {
            console.log(chalk.green(`✅ All files in ${domainName} are in sync!`));
            return;
        }

        console.log(chalk.yellow(`Found ${unsyncedFiles.length} unsynced files:`));
        unsyncedFiles.forEach(file => {
            console.log(chalk.gray(`  • ${file.relativePath} (${file.reason})`));
        });

        const result = await this.syncUnsyncedFiles(domainName, unsyncedFiles);
        
        if (result.failCount === 0) {
            console.log(chalk.green(`🎉 ${domainName} is now fully synced!`));
        } else {
            console.log(chalk.yellow(`⚠️  ${result.failCount} files in ${domainName} still need attention`));
        }
    }
}

// CLI usage
async function main() {
    const recovery = new SyncRecovery();
    
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        // Recover all domains
        await recovery.recoverAllDomains();
    } else {
        // Recover specific domain
        const domainName = args[0];
        await recovery.recoverDomain(domainName);
    }
    
    process.exit(0);
}

if (require.main === module) {
    main().catch(error => {
        console.error(chalk.red('Recovery failed:'), error.message);
        process.exit(1);
    });
}

module.exports = SyncRecovery;

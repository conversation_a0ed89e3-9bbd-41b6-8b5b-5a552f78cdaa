<?php
class AlgoFactoryAPI {
    private $baseUrl;
    private $token;
    
    public function __construct() {
        $this->baseUrl = ALGOFACTORY_API_BASE;
        $this->token = $this->getAuthToken();
    }
    
    private function getAuthToken() {
        // Try to get token from session first
        if (isset($_SESSION['algofactory_token'])) {
            return $_SESSION['algofactory_token'];
        }
        
        // If no token, try to authenticate with stored credentials
        return $this->authenticate();
    }
    
    private function authenticate() {
        $loginData = [
            'email' => ALGOFACTORY_ADMIN_EMAIL,
            'password' => ALGOFACTORY_ADMIN_PASSWORD,
            'domain_name' => str_replace(['http://', 'https://', 'www.'], '', $_SERVER['HTTP_HOST'])
        ];
        
        $response = $this->makeRequest('/auth/login', 'POST', $loginData, false);
        
        if ($response && isset($response['status']) && $response['status'] && isset($response['data']['token'])) {
            $token = $response['data']['token'];
            $_SESSION['algofactory_token'] = $token;
            return $token;
        }
        
        throw new Exception('Failed to authenticate with AlgoFactory API');
    }
    
    private function makeRequest($endpoint, $method = 'GET', $data = null, $requireAuth = true) {
        $url = $this->baseUrl . $endpoint;
        
        $headers = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        if ($requireAuth && $this->token) {
            $headers[] = 'Authorization: Bearer ' . $this->token;
        }
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => false, // For testing only
            CURLOPT_FOLLOWLOCATION => true
        ]);
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        } elseif ($method === 'PUT') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        } elseif ($method === 'DELETE') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception('CURL Error: ' . $error);
        }
        
        if ($httpCode >= 400) {
            // If unauthorized, try to re-authenticate once
            if ($httpCode === 401 && $requireAuth) {
                unset($_SESSION['algofactory_token']);
                $this->token = $this->authenticate();
                return $this->makeRequest($endpoint, $method, $data, $requireAuth);
            }
            throw new Exception('HTTP Error: ' . $httpCode);
        }
        
        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid JSON response: ' . json_last_error_msg());
        }
        
        return $decodedResponse;
    }
    
    // Strategy Methods
    public function getStrategies() {
        return $this->makeRequest('/admin/getstrategies');
    }
    
    public function getStrategy($strategyId) {
        return $this->makeRequest('/admin/getstrategyrecord', 'POST', ['strategy_id' => $strategyId]);
    }
    
    public function updateStrategy($strategyId, $data) {
        $data['strategy_id'] = $strategyId;
        return $this->makeRequest('/admin/updatestartegyinfo', 'POST', $data);
    }
    
    public function updateTradingFlag($strategyId, $flag) {
        return $this->makeRequest('/admin/updatetradingflag', 'POST', [
            'strategy_id' => $strategyId,
            'trading_flag' => $flag
        ]);
    }
    
    public function createStrategy($data) {
        return $this->makeRequest('/admin/createstrategy', 'POST', $data);
    }
    
    public function deleteStrategy($strategyId) {
        return $this->makeRequest('/admin/removestrategy', 'POST', ['strategy_id' => $strategyId]);
    }
    
    // User Methods
    public function getUsers() {
        return $this->makeRequest('/admin/getusers');
    }
    
    public function getUserBrokers() {
        return $this->makeRequest('/admin/getuserbrokers');
    }
    
    public function getStrategyUsers($strategyId) {
        return $this->makeRequest('/admin/getstrategyusers', 'POST', ['strategy_id' => $strategyId]);
    }
    
    // Position Methods
    public function getOpenPositions($strategyId) {
        return $this->makeRequest('/admin/getopenstrategyposition', 'POST', ['strategy_id' => $strategyId]);
    }
    
    public function getClosedPositions($strategyId) {
        return $this->makeRequest('/admin/getclosestrategyposition', 'POST', ['strategy_id' => $strategyId]);
    }
    
    public function closePosition($data) {
        return $this->makeRequest('/admin/exitstrategyposition', 'POST', $data);
    }
    
    public function addPositionEntry($data) {
        return $this->makeRequest('/admin/addpositionentry', 'POST', $data);
    }
    
    // Order Methods
    public function getOrders() {
        return $this->makeRequest('/admin/getorders');
    }
    
    public function getFailedOrders() {
        return $this->makeRequest('/admin/getfailedorders');
    }
    
    public function cancelOrder($brokerId, $orderId) {
        return $this->makeRequest('/broker/cancelorder', 'POST', [
            'broker_id' => $brokerId,
            'order_id' => $orderId
        ]);
    }
    
    public function executeOrder($brokerId, $orderId) {
        return $this->makeRequest('/broker/executenow', 'POST', [
            'broker_id' => $brokerId,
            'order_id' => $orderId
        ]);
    }
    
    // Broker Methods
    public function getMasterBrokers() {
        return $this->makeRequest('/admin/getmasterbroker');
    }
    
    public function getBrokerInfo($brokerId) {
        return $this->makeRequest('/broker/getbrokerinfo', 'POST', ['broker_id' => $brokerId]);
    }
    
    public function refreshBroker($brokerId) {
        return $this->makeRequest('/broker/refresh', 'POST', ['broker_id' => $brokerId]);
    }
    
    // Transaction Methods
    public function getTransactions() {
        return $this->makeRequest('/admin/gettransactions');
    }
    
    public function getBillHistory() {
        return $this->makeRequest('/admin/billhistory');
    }
    
    // Symbol Methods
    public function getSymbols($symbolType, $searchTerm) {
        return $this->makeRequest('/admin/getsymbols', 'POST', [
            'symbol_type' => $symbolType,
            'search_term' => $searchTerm
        ]);
    }
    
    // Configuration Methods
    public function getConstants() {
        return $this->makeRequest('/admin/getconstants');
    }
    
    public function getConfig() {
        return $this->makeRequest('/admin/getconfig');
    }
    
    // Test connection
    public function testConnection() {
        try {
            $response = $this->getConstants();
            return [
                'success' => true,
                'message' => 'API connection successful',
                'data' => $response
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'API connection failed: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    // Get mock data for testing when API is not available
    public function getMockStrategies() {
        return [
            'status' => true,
            'data' => [
                [
                    'strategy_id' => 1,
                    'strategy_name' => 'Momentum Strategy',
                    'description' => 'High frequency momentum trading',
                    'is_active' => true,
                    'required_amount' => 50000
                ],
                [
                    'strategy_id' => 2,
                    'strategy_name' => 'Mean Reversion',
                    'description' => 'Statistical arbitrage strategy',
                    'is_active' => true,
                    'required_amount' => 100000
                ],
                [
                    'strategy_id' => 3,
                    'strategy_name' => 'Scalping Bot',
                    'description' => 'Quick scalping trades',
                    'is_active' => false,
                    'required_amount' => 25000
                ]
            ]
        ];
    }
    
    public function getMockUsers() {
        return [
            'status' => true,
            'data' => [
                [
                    'user_id' => 1,
                    'name' => 'John Doe',
                    'email' => '<EMAIL>',
                    'phone' => '+91 9876543210',
                    'is_active' => true
                ],
                [
                    'user_id' => 2,
                    'name' => 'Jane Smith',
                    'email' => '<EMAIL>',
                    'phone' => '+91 9876543211',
                    'is_active' => true
                ],
                [
                    'user_id' => 3,
                    'name' => 'Bob Johnson',
                    'email' => '<EMAIL>',
                    'phone' => '+91 9876543212',
                    'is_active' => false
                ]
            ]
        ];
    }
    
    public function getMockOrders() {
        return [
            'status' => true,
            'data' => [
                [
                    'order_id' => 'ORD001',
                    'strategy_id' => 1,
                    'symbol' => 'RELIANCE',
                    'quantity' => 100,
                    'price' => 2500.50,
                    'side' => 'BUY',
                    'status' => 'COMPLETED',
                    'timestamp' => date('Y-m-d H:i:s')
                ],
                [
                    'order_id' => 'ORD002',
                    'strategy_id' => 2,
                    'symbol' => 'TCS',
                    'quantity' => 50,
                    'price' => 3200.75,
                    'side' => 'SELL',
                    'status' => 'PENDING',
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]
        ];
    }
}

// API Response Helper
class APIResponse {
    public static function success($data = null, $message = 'Success') {
        return [
            'success' => true,
            'message' => $message,
            'data' => $data
        ];
    }
    
    public static function error($message = 'Error', $code = 400) {
        return [
            'success' => false,
            'message' => $message,
            'code' => $code
        ];
    }
    
    public static function json($data) {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}
?>

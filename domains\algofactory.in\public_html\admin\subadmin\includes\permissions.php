<?php
class PermissionManager {
    private $pdo;
    private $userId;
    private $userPermissions = null;
    private $userRole = null;
    
    public function __construct($pdo, $userId) {
        $this->pdo = $pdo;
        $this->userId = $userId;
        $this->loadUserData();
        $this->loadPermissions();
    }
    
    private function loadUserData() {
        $stmt = $this->pdo->prepare("SELECT role FROM admin_users WHERE id = ?");
        $stmt->execute([$this->userId]);
        $user = $stmt->fetch();
        $this->userRole = $user ? $user['role'] : null;
    }
    
    private function loadPermissions() {
        $stmt = $this->pdo->prepare("
            SELECT resource_type, resource_id, permission_type 
            FROM user_permissions 
            WHERE user_id = ?
        ");
        $stmt->execute([$this->userId]);
        
        $this->userPermissions = [];
        while ($row = $stmt->fetch()) {
            $resourceType = $row['resource_type'];
            $resourceId = $row['resource_id'] ?: '*';
            $permissionType = $row['permission_type'];
            
            if (!isset($this->userPermissions[$resourceType])) {
                $this->userPermissions[$resourceType] = [];
            }
            if (!isset($this->userPermissions[$resourceType][$permissionType])) {
                $this->userPermissions[$resourceType][$permissionType] = [];
            }
            
            $this->userPermissions[$resourceType][$permissionType][] = $resourceId;
        }
    }
    
    public function canPerform($action, $resourceType, $resourceId = null) {
        // Super admin can do everything
        if ($this->isSuperAdmin()) {
            return true;
        }
        
        // Check if user has the permission
        if (!isset($this->userPermissions[$resourceType][$action])) {
            return false;
        }
        
        $allowedResources = $this->userPermissions[$resourceType][$action];
        
        // Check if user has permission for all resources (*) or specific resource
        return in_array('*', $allowedResources) || 
               ($resourceId && in_array((string)$resourceId, $allowedResources));
    }
    
    public function canView($resourceType, $resourceId = null) {
        return $this->canPerform('view', $resourceType, $resourceId);
    }
    
    public function canEdit($resourceType, $resourceId = null) {
        return $this->canPerform('edit', $resourceType, $resourceId);
    }
    
    public function canDelete($resourceType, $resourceId = null) {
        return $this->canPerform('delete', $resourceType, $resourceId);
    }
    
    public function canCreate($resourceType) {
        return $this->canPerform('create', $resourceType);
    }
    
    public function isSuperAdmin() {
        return $this->userRole === 'super_admin';
    }
    
    public function isSubAdmin() {
        return $this->userRole === 'sub_admin';
    }
    
    public function filterStrategies($strategies) {
        if ($this->isSuperAdmin()) {
            return $strategies;
        }
        
        return array_filter($strategies, function($strategy) {
            $strategyId = $strategy['strategy_id'] ?? $strategy['id'] ?? null;
            return $strategyId && $this->canView('strategy', $strategyId);
        });
    }
    
    public function filterUsers($users) {
        if ($this->isSuperAdmin()) {
            return $users;
        }
        
        return array_filter($users, function($user) {
            $userId = $user['user_id'] ?? $user['id'] ?? null;
            return $userId && $this->canView('user', $userId);
        });
    }
    
    public function filterOrders($orders) {
        if ($this->isSuperAdmin()) {
            return $orders;
        }
        
        if (!$this->canView('order')) {
            return [];
        }
        
        // If user can view orders, filter by strategy permissions
        return array_filter($orders, function($order) {
            $strategyId = $order['strategy_id'] ?? null;
            return !$strategyId || $this->canView('strategy', $strategyId);
        });
    }
    
    public function filterPositions($positions) {
        if ($this->isSuperAdmin()) {
            return $positions;
        }
        
        if (!$this->canView('position')) {
            return [];
        }
        
        // Filter by strategy permissions
        return array_filter($positions, function($position) {
            $strategyId = $position['strategy_id'] ?? null;
            return !$strategyId || $this->canView('strategy', $strategyId);
        });
    }
    
    public function getAllowedStrategies() {
        if ($this->isSuperAdmin()) {
            return ['*'];
        }
        
        $strategies = [];
        if (isset($this->userPermissions['strategy']['view'])) {
            $strategies = array_merge($strategies, $this->userPermissions['strategy']['view']);
        }
        if (isset($this->userPermissions['strategy']['edit'])) {
            $strategies = array_merge($strategies, $this->userPermissions['strategy']['edit']);
        }
        
        return array_unique($strategies);
    }
    
    public function getAllowedUsers() {
        if ($this->isSuperAdmin()) {
            return ['*'];
        }
        
        $users = [];
        if (isset($this->userPermissions['user']['view'])) {
            $users = array_merge($users, $this->userPermissions['user']['view']);
        }
        if (isset($this->userPermissions['user']['edit'])) {
            $users = array_merge($users, $this->userPermissions['user']['edit']);
        }
        
        return array_unique($users);
    }
    
    public function getPermissionSummary() {
        if ($this->isSuperAdmin()) {
            return [
                'role' => 'Super Admin',
                'strategies' => ['view' => ['*'], 'edit' => ['*'], 'delete' => ['*'], 'create' => true],
                'users' => ['view' => ['*'], 'edit' => ['*'], 'delete' => ['*']],
                'orders' => ['view' => true, 'cancel' => true],
                'positions' => ['view' => true, 'close' => true, 'add' => true]
            ];
        }
        
        $summary = [
            'role' => 'Sub Admin',
            'strategies' => [
                'view' => $this->userPermissions['strategy']['view'] ?? [],
                'edit' => $this->userPermissions['strategy']['edit'] ?? [],
                'delete' => $this->userPermissions['strategy']['delete'] ?? [],
                'create' => !empty($this->userPermissions['strategy']['create'])
            ],
            'users' => [
                'view' => $this->userPermissions['user']['view'] ?? [],
                'edit' => $this->userPermissions['user']['edit'] ?? [],
                'delete' => $this->userPermissions['user']['delete'] ?? []
            ],
            'orders' => [
                'view' => !empty($this->userPermissions['order']['view']),
                'cancel' => !empty($this->userPermissions['order']['edit'])
            ],
            'positions' => [
                'view' => !empty($this->userPermissions['position']['view']),
                'close' => !empty($this->userPermissions['position']['edit']),
                'add' => !empty($this->userPermissions['position']['create'])
            ]
        ];
        
        return $summary;
    }
    
    public function hasAnyPermission($resourceType) {
        if ($this->isSuperAdmin()) {
            return true;
        }
        
        return isset($this->userPermissions[$resourceType]) && 
               !empty($this->userPermissions[$resourceType]);
    }
    
    public function getAccessibleMenuItems() {
        $menuItems = [];
        
        // Dashboard is always accessible
        $menuItems[] = [
            'name' => 'Dashboard',
            'url' => 'dashboard.php',
            'icon' => '🏠'
        ];
        
        // Strategies
        if ($this->hasAnyPermission('strategy')) {
            $menuItems[] = [
                'name' => 'Strategies',
                'url' => 'strategies.php',
                'icon' => '🎯'
            ];
        }
        
        // Users
        if ($this->hasAnyPermission('user')) {
            $menuItems[] = [
                'name' => 'Users',
                'url' => 'users.php',
                'icon' => '👥'
            ];
        }
        
        // Orders
        if ($this->canView('order')) {
            $menuItems[] = [
                'name' => 'Orders',
                'url' => 'orders.php',
                'icon' => '📋'
            ];
        }
        
        // Positions
        if ($this->canView('position')) {
            $menuItems[] = [
                'name' => 'Positions',
                'url' => 'positions.php',
                'icon' => '📊'
            ];
        }
        
        // Super admin only items
        if ($this->isSuperAdmin()) {
            $menuItems[] = [
                'name' => 'Sub Admins',
                'url' => 'manage-sub-admins.php',
                'icon' => '👑'
            ];
            $menuItems[] = [
                'name' => 'Permissions',
                'url' => 'manage-permissions.php',
                'icon' => '🔐'
            ];
            $menuItems[] = [
                'name' => 'Activity Logs',
                'url' => 'activity-logs.php',
                'icon' => '📝'
            ];
        }
        
        return $menuItems;
    }
    
    public function validateApiAccess($endpoint, $method = 'GET', $resourceId = null) {
        // Map API endpoints to permission requirements
        $endpointPermissions = [
            'strategies' => ['resource' => 'strategy', 'action' => 'view'],
            'strategies/create' => ['resource' => 'strategy', 'action' => 'create'],
            'strategies/update' => ['resource' => 'strategy', 'action' => 'edit'],
            'strategies/delete' => ['resource' => 'strategy', 'action' => 'delete'],
            'users' => ['resource' => 'user', 'action' => 'view'],
            'users/update' => ['resource' => 'user', 'action' => 'edit'],
            'orders' => ['resource' => 'order', 'action' => 'view'],
            'orders/cancel' => ['resource' => 'order', 'action' => 'edit'],
            'positions' => ['resource' => 'position', 'action' => 'view'],
            'positions/close' => ['resource' => 'position', 'action' => 'edit']
        ];
        
        if (!isset($endpointPermissions[$endpoint])) {
            return false; // Unknown endpoint
        }
        
        $permission = $endpointPermissions[$endpoint];
        return $this->canPerform(
            $permission['action'], 
            $permission['resource'], 
            $resourceId
        );
    }
    
    public function refreshPermissions() {
        $this->loadPermissions();
    }
}
?>

<?php
// Database Configuration for <PERSON><PERSON>
define('DB_HOST', 'localhost');
define('DB_NAME', 'u123456789_algofactory'); // Replace with your actual database name
define('DB_USER', 'u123456789_admin');       // Replace with your actual database username
define('DB_PASS', 'your_database_password'); // Replace with your actual database password

// AlgoFactory API Configuration
define('ALGOFACTORY_API_BASE', 'https://bpapil1.algodelta.com/api/v1');
define('ALGOFACTORY_TOKEN_KEY', 'admin_access_token');

// Your original AlgoFactory admin credentials (for API access)
define('ALGOFACTORY_ADMIN_EMAIL', '<EMAIL>');
define('ALGOFACTORY_ADMIN_PASSWORD', 'your_admin_password');

// Session Configuration
define('SESSION_TIMEOUT', 3600); // 1 hour
define('COOKIE_DOMAIN', '.yourdomain.com'); // Replace with your domain
define('SESSION_NAME', 'algofactory_subadmin');

// Security Configuration
define('ENCRYPTION_KEY', 'your-secret-encryption-key-change-this-in-production');
define('PASSWORD_SALT', 'your-password-salt-change-this-in-production');

// Application Configuration
define('APP_NAME', 'AlgoFactory Sub Admin');
define('APP_VERSION', '1.0.0');
define('ADMIN_EMAIL', '<EMAIL>');

// Error Reporting (set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Asia/Kolkata'); // Change as needed

// Database Connection
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    );
} catch (PDOException $e) {
    // Log error instead of displaying in production
    error_log("Database connection failed: " . $e->getMessage());
    die("Database connection failed. Please contact administrator.");
}

// Helper Functions
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function generateRandomToken($length = 32) {
    return bin2hex(random_bytes($length));
}

function logActivity($userId, $action, $details = '') {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent, created_at) 
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $userId,
            $action,
            $details,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (Exception $e) {
        error_log("Failed to log activity: " . $e->getMessage());
    }
}

// Check if tables exist, if not show setup message
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'admin_users'");
    if ($stmt->rowCount() == 0) {
        $setupNeeded = true;
    } else {
        $setupNeeded = false;
    }
} catch (Exception $e) {
    $setupNeeded = true;
}

// Auto-create tables if they don't exist (for testing)
if ($setupNeeded && !isset($_GET['skip_setup'])) {
    try {
        // Create admin_users table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `admin_users` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `username` varchar(50) NOT NULL UNIQUE,
                `email` varchar(100) NOT NULL UNIQUE,
                `password_hash` varchar(255) NOT NULL,
                `role` enum('super_admin','sub_admin','viewer') NOT NULL DEFAULT 'sub_admin',
                `created_by` int(11) DEFAULT NULL,
                `is_active` tinyint(1) DEFAULT 1,
                `last_login` timestamp NULL DEFAULT NULL,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `created_by` (`created_by`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");

        // Create user_permissions table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `user_permissions` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `resource_type` enum('strategy','user','broker','order','position','general') NOT NULL,
                `resource_id` varchar(50) DEFAULT NULL,
                `permission_type` enum('view','edit','delete','create','all') NOT NULL,
                `granted_by` int(11) NOT NULL,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_permission` (`user_id`,`resource_type`,`resource_id`,`permission_type`),
                KEY `user_id` (`user_id`),
                KEY `granted_by` (`granted_by`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");

        // Create admin_sessions table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `admin_sessions` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `session_token` varchar(255) NOT NULL,
                `expires_at` timestamp NOT NULL,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `session_token` (`session_token`),
                KEY `user_id` (`user_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");

        // Create activity_logs table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `activity_logs` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) DEFAULT NULL,
                `action` varchar(100) NOT NULL,
                `details` text,
                `ip_address` varchar(45),
                `user_agent` text,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `user_id` (`user_id`),
                KEY `created_at` (`created_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");

        // Create default super admin user (for testing)
        $defaultPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO admin_users (username, email, password_hash, role) 
            VALUES ('superadmin', '<EMAIL>', ?, 'super_admin')
        ");
        $stmt->execute([$defaultPassword]);

        // Create a test sub-admin
        $testPassword = password_hash('test123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO admin_users (username, email, password_hash, role, created_by) 
            VALUES ('testuser', '<EMAIL>', ?, 'sub_admin', 1)
        ");
        $stmt->execute([$testPassword]);

        // Add some test permissions for the sub-admin
        $pdo->exec("
            INSERT IGNORE INTO user_permissions (user_id, resource_type, resource_id, permission_type, granted_by) VALUES
            (2, 'strategy', '1', 'view', 1),
            (2, 'strategy', '1', 'edit', 1),
            (2, 'strategy', '2', 'view', 1),
            (2, 'user', '1', 'view', 1),
            (2, 'user', '1', 'edit', 1),
            (2, 'order', '*', 'view', 1),
            (2, 'position', '*', 'view', 1)
        ");

        $setupMessage = "
        <div style='background: #d4edda; color: #155724; padding: 1rem; border-radius: 5px; margin: 1rem 0;'>
            <h3>✅ Database Setup Complete!</h3>
            <p><strong>Test Accounts Created:</strong></p>
            <ul>
                <li><strong>Super Admin:</strong> Username: <code>superadmin</code>, Password: <code>admin123</code></li>
                <li><strong>Sub Admin:</strong> Username: <code>testuser</code>, Password: <code>test123</code></li>
            </ul>
            <p>You can now test the login system!</p>
        </div>";

    } catch (Exception $e) {
        $setupMessage = "
        <div style='background: #f8d7da; color: #721c24; padding: 1rem; border-radius: 5px; margin: 1rem 0;'>
            <h3>❌ Database Setup Failed</h3>
            <p>Error: " . htmlspecialchars($e->getMessage()) . "</p>
            <p>Please check your database configuration in config.php</p>
        </div>";
    }
}
?>

#!/usr/bin/env node

const { program } = require('commander');
const chalk = require('chalk');
const inquirer = require('inquirer');
const HostingerDomainManager = require('./domain-manager');
const HostingerMultiDomainSync = require('./sync-watcher');

program
    .name('domain-cli')
    .description('Hostinger Multi-Domain Management CLI')
    .version('1.0.0');

program
    .command('setup')
    .description('Discover and setup domains')
    .action(async () => {
        try {
            console.log(chalk.cyan('=== Hostinger Domain Setup ===\n'));
            
            const manager = new HostingerDomainManager();
            await manager.setupDomains();
            
            console.log(chalk.green('\n✓ Domain setup completed!'));
            console.log(chalk.yellow('Next steps:'));
            console.log('1. Download domains: node domain-cli.js download --all');
            console.log('2. Start sync: npm start');
            
        } catch (error) {
            console.error(chalk.red('Setup failed:'), error.message);
            process.exit(1);
        }
    });

program
    .command('list')
    .description('List all configured domains')
    .action(async () => {
        try {
            const manager = new HostingerDomainManager();
            await manager.listDomains();
        } catch (error) {
            console.error(chalk.red('Error listing domains:'), error.message);
        }
    });

program
    .command('download')
    .description('Download domain files')
    .option('-a, --all', 'Download all domains')
    .option('-d, --domain <name>', 'Download specific domain')
    .action(async (options) => {
        try {
            const manager = new HostingerDomainManager();
            
            if (options.all) {
                console.log(chalk.blue('Downloading all domains...'));
                await manager.downloadAllDomains();
            } else if (options.domain) {
                console.log(chalk.blue(`Downloading ${options.domain}...`));
                await manager.downloadDomain(options.domain);
            } else {
                // Interactive selection
                const domains = Object.keys(manager.config.domains);
                if (domains.length === 0) {
                    console.log(chalk.yellow('No domains configured. Run setup first.'));
                    return;
                }
                
                const answers = await inquirer.prompt([{
                    type: 'checkbox',
                    name: 'domains',
                    message: 'Select domains to download:',
                    choices: domains
                }]);
                
                for (const domain of answers.domains) {
                    await manager.downloadDomain(domain);
                }
            }
            
            console.log(chalk.green('✓ Download completed!'));
            
        } catch (error) {
            console.error(chalk.red('Download failed:'), error.message);
        }
    });

program
    .command('sync')
    .description('Start file synchronization')
    .option('-d, --domain <name>', 'Sync specific domain only')
    .action(async (options) => {
        try {
            const sync = new HostingerMultiDomainSync();
            
            if (options.domain) {
                // Enable only specific domain
                Object.keys(sync.config.domains).forEach(domain => {
                    sync.config.domains[domain].enabled = domain === options.domain;
                });
                sync.saveConfig();
            }
            
            await sync.connect();
            sync.startWatchingAllDomains();
            
            console.log(chalk.green('Sync started. Press Ctrl+C to stop.'));
            
            // Keep process alive
            process.on('SIGINT', async () => {
                console.log(chalk.yellow('\nStopping sync...'));
                await sync.disconnect();
                process.exit(0);
            });
            
        } catch (error) {
            console.error(chalk.red('Sync failed:'), error.message);
            process.exit(1);
        }
    });

program
    .command('status')
    .description('Show sync status')
    .action(async () => {
        try {
            const sync = new HostingerMultiDomainSync();
            const status = sync.getStatus();
            
            console.log(chalk.cyan('=== Sync Status ==='));
            console.log(`Connected: ${status.connected ? chalk.green('Yes') : chalk.red('No')}`);
            console.log(`Enabled Domains: ${status.enabledDomains.join(', ')}`);
            console.log(`Watching: ${status.watchingDomains.join(', ')}`);
            console.log(`Active Uploads: ${status.activeUploads}`);
            
            if (Object.keys(status.queueSizes).length > 0) {
                console.log('Queue Sizes:');
                Object.entries(status.queueSizes).forEach(([domain, size]) => {
                    console.log(`  ${domain}: ${size} files`);
                });
            }
            
        } catch (error) {
            console.error(chalk.red('Error getting status:'), error.message);
        }
    });

program
    .command('enable')
    .description('Enable domain for sync')
    .argument('<domain>', 'Domain name to enable')
    .action(async (domain) => {
        try {
            const manager = new HostingerDomainManager();
            
            if (!manager.config.domains[domain]) {
                console.error(chalk.red(`Domain ${domain} not found`));
                return;
            }
            
            manager.config.domains[domain].enabled = true;
            manager.saveConfig();
            
            console.log(chalk.green(`✓ Enabled ${domain}`));
            
        } catch (error) {
            console.error(chalk.red('Error enabling domain:'), error.message);
        }
    });

program
    .command('disable')
    .description('Disable domain from sync')
    .argument('<domain>', 'Domain name to disable')
    .action(async (domain) => {
        try {
            const manager = new HostingerDomainManager();
            
            if (!manager.config.domains[domain]) {
                console.error(chalk.red(`Domain ${domain} not found`));
                return;
            }
            
            manager.config.domains[domain].enabled = false;
            manager.saveConfig();
            
            console.log(chalk.yellow(`✓ Disabled ${domain}`));
            
        } catch (error) {
            console.error(chalk.red('Error disabling domain:'), error.message);
        }
    });

program.parse();

<?php
function checkAuth() {
    // Get auth token from cookie (set by main site)
    $auth_token = $_COOKIE['auth_token'] ?? null;
    error_log("[APP] Checking auth token: " . ($auth_token ?? 'none'));

    if (!$auth_token) {
        error_log("[APP] No auth token - redirecting to main site");
        header('Location: https://algoangel.in/');
        exit();
    }

    try {
        // Verify token with main site API
        $ch = curl_init('https://algoangel.in/api/verify_token.php');
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_COOKIE => 'auth_token=' . $auth_token,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_HEADER => false,
            CURLOPT_VERBOSE => true
        ]);
        
        $response = curl_exec($ch);
        $info = curl_getinfo($ch);
        curl_close($ch);
        
        error_log("[APP] API Response: " . $response);
        error_log("[APP] API Status: " . $info['http_code']);
        
        $data = json_decode($response, true);
        
        if ($data['status'] === 'success') {
            // Token is valid on main site
            $user_email = $data['email'];
            error_log("[APP] Valid token for user: " . $user_email);
            
            // Get or create user in app database
            $db = getDbConnection();
            $stmt = $db->prepare("SELECT id, email FROM users WHERE email = ?");
            $stmt->bind_param("s", $user_email);
            $stmt->execute();
            $user = $stmt->get_result()->fetch_assoc();
            
            if ($user) {
                error_log("[APP] Found existing user: " . $user['email']);
                // Update existing user's token
                $stmt = $db->prepare("UPDATE users SET auth_token = ? WHERE id = ?");
                $stmt->bind_param("si", $auth_token, $user['id']);
                $stmt->execute();
            } else {
                error_log("[APP] Creating new user: " . $user_email);
                // Create new user in app database
                $stmt = $db->prepare("INSERT INTO users (email, auth_token, status) VALUES (?, ?, 'active')");
                $stmt->bind_param("ss", $user_email, $auth_token);
                $stmt->execute();
                $user = ['id' => $db->insert_id, 'email' => $user_email];
            }
            
            // Set session data
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_email'] = $user_email;
            error_log("[APP] Session set for user: " . $user_email);
            return true;
        }

        error_log("[APP] Invalid token response from main site");
        header('Location: https://algoangel.in/');
        exit();

    } catch (Exception $e) {
        error_log("[APP] Auth error: " . $e->getMessage());
        header('Location: https://algoangel.in/');
        exit();
    }
} 
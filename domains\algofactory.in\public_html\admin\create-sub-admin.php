<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/api-client.php';

$auth = new Auth($pdo);

// Check authentication and super admin role
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$currentUser = $auth->getCurrentUser();
if ($currentUser['role'] !== 'super_admin') {
    header('Location: sub-admin.php');
    exit;
}

$error = '';
$success = '';
$strategies = [];
$users = [];

// Fetch strategies and users from AlgoFactory API
try {
    $apiClient = new AlgoFactoryAPI();
    $strategiesResponse = $apiClient->getStrategies();
    $usersResponse = $apiClient->getUsers();
    
    if ($strategiesResponse && isset($strategiesResponse['data'])) {
        $strategies = $strategiesResponse['data'];
    }
    
    if ($usersResponse && isset($usersResponse['data'])) {
        $users = $usersResponse['data'];
    }
} catch (Exception $e) {
    $error = "Error fetching data from API: " . $e->getMessage();
}

// Handle form submission
if ($_POST && !$error) {
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    // Validation
    if (empty($username) || empty($email) || empty($password)) {
        $error = 'Please fill in all required fields.';
    } elseif ($password !== $confirmPassword) {
        $error = 'Passwords do not match.';
    } elseif (strlen($password) < 6) {
        $error = 'Password must be at least 6 characters long.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } else {
        // Build permissions array
        $permissions = [];
        
        // Strategy permissions
        foreach ($strategies as $strategy) {
            $strategyId = $strategy['strategy_id'];
            
            if (isset($_POST["strategy_view_{$strategyId}"])) {
                $permissions[] = [
                    'resource_type' => 'strategy',
                    'resource_id' => $strategyId,
                    'permission_type' => 'view'
                ];
            }
            
            if (isset($_POST["strategy_edit_{$strategyId}"])) {
                $permissions[] = [
                    'resource_type' => 'strategy',
                    'resource_id' => $strategyId,
                    'permission_type' => 'edit'
                ];
            }
        }
        
        // User permissions
        foreach ($users as $user) {
            $userId = $user['user_id'];
            
            if (isset($_POST["user_view_{$userId}"])) {
                $permissions[] = [
                    'resource_type' => 'user',
                    'resource_id' => $userId,
                    'permission_type' => 'view'
                ];
            }
            
            if (isset($_POST["user_edit_{$userId}"])) {
                $permissions[] = [
                    'resource_type' => 'user',
                    'resource_id' => $userId,
                    'permission_type' => 'edit'
                ];
            }
        }
        
        // General permissions
        if (isset($_POST['can_view_orders'])) {
            $permissions[] = [
                'resource_type' => 'order',
                'resource_id' => '*',
                'permission_type' => 'view'
            ];
        }
        
        if (isset($_POST['can_view_positions'])) {
            $permissions[] = [
                'resource_type' => 'position',
                'resource_id' => '*',
                'permission_type' => 'view'
            ];
        }
        
        try {
            $newUserId = $auth->createSubAdmin($username, $email, $password, $permissions);
            $success = "Sub admin '{$username}' created successfully with " . count($permissions) . " permissions.";
            
            // Clear form
            $_POST = [];
            
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                if (strpos($e->getMessage(), 'username') !== false) {
                    $error = 'Username already exists. Please choose a different username.';
                } else {
                    $error = 'Email already exists. Please use a different email address.';
                }
            } else {
                $error = 'Error creating sub admin: ' . $e->getMessage();
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Sub Admin - AlgoFactory</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.5rem;
        }
        
        .nav-links {
            display: flex;
            gap: 1rem;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            background: rgba(255,255,255,0.2);
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .form-card {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .form-section {
            margin-bottom: 2rem;
        }
        
        .form-section h3 {
            color: #667eea;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #eee;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .permission-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }
        
        .permission-item {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        
        .permission-item h4 {
            margin-bottom: 0.5rem;
            color: #495057;
        }
        
        .checkbox-group {
            display: flex;
            gap: 1rem;
            margin-top: 0.5rem;
        }
        
        .checkbox-group label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: normal;
            cursor: pointer;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
            margin-right: 1rem;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-error {
            background: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }
        
        .alert-success {
            background: #efe;
            color: #363;
            border: 1px solid #cfc;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .permission-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>👥 Create Sub Admin</h1>
        <div class="nav-links">
            <a href="index.php">🏠 Dashboard</a>
            <a href="manage-permissions.php">🔐 Permissions</a>
            <a href="logout.php">Logout</a>
        </div>
    </div>
    
    <div class="container">
        <?php if ($error): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>
        
        <div class="form-card">
            <form method="POST" action="">
                <div class="form-section">
                    <h3>👤 Basic Information</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="username">Username *</label>
                            <input 
                                type="text" 
                                id="username" 
                                name="username" 
                                value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                required
                                pattern="[a-zA-Z0-9_]+"
                                title="Username can only contain letters, numbers, and underscores"
                            >
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input 
                                type="email" 
                                id="email" 
                                name="email" 
                                value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                required
                            >
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="password">Password *</label>
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                required
                                minlength="6"
                            >
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password">Confirm Password *</label>
                            <input 
                                type="password" 
                                id="confirm_password" 
                                name="confirm_password" 
                                required
                                minlength="6"
                            >
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($strategies)): ?>
                <div class="form-section">
                    <h3>🎯 Strategy Permissions</h3>
                    <div class="permission-grid">
                        <?php foreach ($strategies as $strategy): ?>
                            <div class="permission-item">
                                <h4><?php echo htmlspecialchars($strategy['strategy_name']); ?></h4>
                                <p style="font-size: 0.9rem; color: #666; margin-bottom: 0.5rem;">
                                    ID: <?php echo $strategy['strategy_id']; ?>
                                </p>
                                <div class="checkbox-group">
                                    <label>
                                        <input 
                                            type="checkbox" 
                                            name="strategy_view_<?php echo $strategy['strategy_id']; ?>"
                                            <?php echo isset($_POST["strategy_view_{$strategy['strategy_id']}"]) ? 'checked' : ''; ?>
                                        >
                                        👁️ View
                                    </label>
                                    <label>
                                        <input 
                                            type="checkbox" 
                                            name="strategy_edit_<?php echo $strategy['strategy_id']; ?>"
                                            <?php echo isset($_POST["strategy_edit_{$strategy['strategy_id']}"]) ? 'checked' : ''; ?>
                                        >
                                        ✏️ Edit
                                    </label>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($users)): ?>
                <div class="form-section">
                    <h3>👤 User Account Permissions</h3>
                    <div class="permission-grid">
                        <?php foreach (array_slice($users, 0, 10) as $user): // Limit to first 10 users for demo ?>
                            <div class="permission-item">
                                <h4><?php echo htmlspecialchars($user['name'] ?? $user['username'] ?? 'User ' . $user['user_id']); ?></h4>
                                <p style="font-size: 0.9rem; color: #666; margin-bottom: 0.5rem;">
                                    ID: <?php echo $user['user_id']; ?>
                                </p>
                                <div class="checkbox-group">
                                    <label>
                                        <input 
                                            type="checkbox" 
                                            name="user_view_<?php echo $user['user_id']; ?>"
                                            <?php echo isset($_POST["user_view_{$user['user_id']}"]) ? 'checked' : ''; ?>
                                        >
                                        👁️ View
                                    </label>
                                    <label>
                                        <input 
                                            type="checkbox" 
                                            name="user_edit_<?php echo $user['user_id']; ?>"
                                            <?php echo isset($_POST["user_edit_{$user['user_id']}"]) ? 'checked' : ''; ?>
                                        >
                                        ✏️ Edit
                                    </label>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <?php if (count($users) > 10): ?>
                        <p style="color: #666; font-style: italic; margin-top: 1rem;">
                            Showing first 10 users. More users can be assigned after creation.
                        </p>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                
                <div class="form-section">
                    <h3>⚙️ General Permissions</h3>
                    <div class="checkbox-group">
                        <label>
                            <input 
                                type="checkbox" 
                                name="can_view_orders"
                                <?php echo isset($_POST['can_view_orders']) ? 'checked' : ''; ?>
                            >
                            📋 View Orders
                        </label>
                        <label>
                            <input 
                                type="checkbox" 
                                name="can_view_positions"
                                <?php echo isset($_POST['can_view_positions']) ? 'checked' : ''; ?>
                            >
                            📊 View Positions
                        </label>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 2rem;">
                    <a href="index.php" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn">Create Sub Admin</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });
        
        // Auto-check view when edit is checked
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            if (checkbox.name.includes('_edit_')) {
                checkbox.addEventListener('change', function() {
                    if (this.checked) {
                        const viewCheckbox = document.querySelector(`input[name="${this.name.replace('_edit_', '_view_')}"]`);
                        if (viewCheckbox) {
                            viewCheckbox.checked = true;
                        }
                    }
                });
            }
        });
        
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match!');
                return false;
            }
            
            // Check if at least one permission is selected
            const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            if (checkboxes.length === 0) {
                e.preventDefault();
                alert('Please select at least one permission for the sub admin.');
                return false;
            }
        });
    </script>
</body>
</html>

# AlgoFactory Sub Admin System

A complete role-based admin management system for AlgoFactory, built with PHP and MySQL for Hostinger shared hosting.

## 🚀 Quick Start

### 1. Setup Database Configuration

Edit `includes/config.php` and update your database credentials:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database_name');     // Your Hostinger database name
define('DB_USER', 'your_database_username'); // Your Hostinger database username  
define('DB_PASS', 'your_database_password'); // Your Hostinger database password
```

### 2. Configure AlgoFactory API

Update the API configuration in `includes/config.php`:

```php
define('ALGOFACTORY_ADMIN_EMAIL', '<EMAIL>');
define('ALGOFACTORY_ADMIN_PASSWORD', 'your_admin_password');
```

### 3. Upload Files

Upload all files to your Hostinger hosting in the `subadmin` folder:

```
public_html/
├── admin/
│   └── subadmin/          ← Upload here
│       ├── login.php
│       ├── super-admin-dashboard.php
│       ├── sub-admin-dashboard.php
│       ├── logout.php
│       └── includes/
│           ├── config.php
│           ├── auth.php
│           ├── permissions.php
│           └── api-client.php
```

### 4. Access the System

Visit: `https://yourdomain.com/admin/subadmin/login.php`

## 🧪 Test Accounts

The system automatically creates test accounts on first run:

### Super Admin
- **Username:** `superadmin`
- **Password:** `admin123`
- **Access:** Full system control

### Sub Admin  
- **Username:** `testuser`
- **Password:** `test123`
- **Access:** Limited permissions (Strategy 1-2, User 1, Orders view-only)

## 🎯 Features

### ✅ Super Admin Features
- Create unlimited sub-admins
- Assign granular permissions per sub-admin
- View all system activity
- Manage all strategies, users, orders, positions
- Full API access to AlgoFactory backend

### ✅ Sub Admin Features  
- Access only assigned strategies
- Manage only permitted users
- View filtered orders and positions
- Role-based dashboard
- Secure session management

## 🔐 Permission System

### Strategy Permissions
- **View:** Can see strategy details
- **Edit:** Can modify strategy settings
- **Delete:** Can remove strategies (super admin only)

### User Permissions
- **View:** Can see user accounts
- **Edit:** Can modify user settings
- **Delete:** Can deactivate users (super admin only)

### Order/Position Permissions
- **View:** Can see orders/positions
- **Edit:** Can cancel orders/close positions
- **Create:** Can place new orders/positions

## 📁 File Structure

```
subadmin/
├── login.php                    # Login page for all users
├── super-admin-dashboard.php    # Super admin dashboard
├── sub-admin-dashboard.php      # Sub admin dashboard  
├── logout.php                   # Logout handler
├── README.md                    # This file
└── includes/
    ├── config.php              # Database & API configuration
    ├── auth.php                # Authentication class
    ├── permissions.php         # Permission management class
    └── api-client.php          # AlgoFactory API client
```

## 🛠️ Database Tables

The system automatically creates these tables:

### `admin_users`
Stores super admin and sub-admin accounts

### `user_permissions`  
Stores granular permissions for each sub-admin

### `admin_sessions`
Manages secure login sessions

### `activity_logs`
Tracks all user actions for auditing

## 🔧 Configuration Options

### Security Settings
```php
// Session timeout (in seconds)
define('SESSION_TIMEOUT', 3600); // 1 hour

// Password requirements
// Minimum 6 characters (can be changed in validation)

// Encryption keys (change in production)
define('ENCRYPTION_KEY', 'your-secret-key');
define('PASSWORD_SALT', 'your-salt');
```

### API Settings
```php
// AlgoFactory API base URL
define('ALGOFACTORY_API_BASE', 'https://bpapil1.algodelta.com/api/v1');

// Your domain for API authentication
// Automatically detected from $_SERVER['HTTP_HOST']
```

## 🎨 Customization

### Changing Colors/Theme
Edit the CSS in each PHP file to match your brand colors:

```css
/* Super Admin Theme - Blue/Purple */
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

/* Sub Admin Theme - Green */  
background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
```

### Adding New Permissions
1. Add new resource type to `user_permissions` table enum
2. Update `PermissionManager` class methods
3. Add UI elements in dashboard files

## 🚨 Security Features

- ✅ **Password Hashing:** Uses PHP `password_hash()` with bcrypt
- ✅ **SQL Injection Protection:** All queries use prepared statements
- ✅ **XSS Protection:** All output is escaped with `htmlspecialchars()`
- ✅ **Session Security:** Secure session tokens with expiration
- ✅ **CSRF Protection:** Session-based validation
- ✅ **Activity Logging:** All actions are logged with IP and user agent

## 📊 Usage Examples

### Creating a Sub-Admin with Specific Permissions

1. Login as super admin (`superadmin` / `admin123`)
2. Go to "Create New Sub Admin"
3. Fill in user details
4. Select specific strategies and users to assign
5. Choose permission levels (view/edit)
6. Click "Create Sub Admin"

### Sub-Admin Experience

1. Login as sub-admin (`testuser` / `test123`)
2. See only assigned strategies and users
3. Dashboard shows filtered data based on permissions
4. Menu items appear only for accessible resources

## 🔗 Integration with AlgoFactory

The system acts as a proxy between your custom admin panel and the AlgoFactory API:

```
Your Sub-Admin → Custom Dashboard → Permission Check → AlgoFactory API
```

- All API calls go through permission validation
- Responses are filtered based on user permissions
- Original AlgoFactory backend remains unchanged

## 🐛 Troubleshooting

### Database Connection Issues
1. Check database credentials in `config.php`
2. Ensure database exists in Hostinger cPanel
3. Verify user has proper permissions

### API Connection Issues  
1. Check AlgoFactory credentials in `config.php`
2. Test API connection from super admin dashboard
3. Verify domain is whitelisted with AlgoFactory

### Permission Issues
1. Check `user_permissions` table for correct entries
2. Verify user role in `admin_users` table
3. Clear browser cache and re-login

### Session Issues
1. Check PHP session configuration
2. Verify cookie domain settings
3. Ensure proper file permissions on server

## 📞 Support

For issues or questions:

1. Check the error logs in your Hostinger cPanel
2. Review the `activity_logs` table for user actions
3. Test with the provided test accounts first
4. Verify all file permissions are set correctly

## 🔄 Updates

To update the system:

1. Backup your `config.php` file
2. Upload new files (keeping your config)
3. Check for any database schema changes
4. Test with test accounts before going live

---

**🎉 Your role-based admin system is ready!**

Visit `https://yourdomain.com/admin/subadmin/login.php` to start using it.

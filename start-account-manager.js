#!/usr/bin/env node

const HostingerAccountManager = require('./hostinger-account-manager');
const chalk = require('chalk');

async function main() {
    const manager = new HostingerAccountManager();
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
        console.log(chalk.yellow('\n🛑 Shutting down...'));
        await manager.stop();
        process.exit(0);
    });

    try {
        await manager.startCompleteManagement();
        
        // Keep process alive and show detailed status
        setInterval(() => {
            const domains = Object.keys(manager.config.domains);
            let totalQueueSize = 0;

            for (const [domainName, queue] of manager.uploadQueues) {
                totalQueueSize += queue.length;
            }

            console.log(chalk.blue(`📊 Status: ${domains.length} domains | Active: ${manager.activeUploads} | Queued: ${totalQueueSize} | Connected: ${manager.isConnected ? '✅' : '❌'}`));

            // Show queue details if there are items
            if (totalQueueSize > 0) {
                for (const [domainName, queue] of manager.uploadQueues) {
                    if (queue.length > 0) {
                        console.log(chalk.gray(`   ${domainName}: ${queue.length} items in queue`));
                    }
                }
            }

            // Auto-reconnect if disconnected
            if (!manager.isConnected) {
                console.log(chalk.yellow('🔄 Attempting to reconnect...'));
                manager.connect().catch(error => {
                    console.error(chalk.red('❌ Reconnection failed:'), error.message);
                });
            }

        }, 30000); // Status every 30 seconds
        
    } catch (error) {
        console.error(chalk.red('❌ Failed to start account management:'), error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = main;

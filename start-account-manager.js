#!/usr/bin/env node

const HostingerAccountManager = require('./hostinger-account-manager');
const chalk = require('chalk');

async function main() {
    const manager = new HostingerAccountManager();
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
        console.log(chalk.yellow('\n🛑 Shutting down...'));
        await manager.stop();
        process.exit(0);
    });

    try {
        await manager.startCompleteManagement();
        
        // Keep process alive and show status
        setInterval(() => {
            const domains = Object.keys(manager.config.domains);
            console.log(chalk.blue(`📊 Managing ${domains.length} domains | Active uploads: ${manager.activeUploads}`));
        }, 60000); // Status every minute
        
    } catch (error) {
        console.error(chalk.red('❌ Failed to start account management:'), error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = main;

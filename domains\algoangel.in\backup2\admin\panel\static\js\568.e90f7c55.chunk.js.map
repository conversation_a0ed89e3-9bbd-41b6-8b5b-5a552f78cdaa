{"version": 3, "file": "static/js/568.e90f7c55.chunk.js", "mappings": "gOAGA,MAuCA,EAvCkBA,GACIC,IAKpB,MAAMC,GAAUC,EAAAA,EAAAA,MA2BhB,OA1BAC,EAAAA,EAAAA,YAAU,KACT,MAAMC,EAAQC,aAAaC,QAAQ,uBAC7B,SAAEC,GAAaC,OAAOC,SACtBC,EAAY,WAAaH,EAAW,UAC1C,GAAKH,EAEE,CACN,MAAMO,EAAiB,CACtBC,OAAQ,OACRC,QAAS,CAAE,eAAgB,oBAC3BC,KAAMC,KAAKC,UAAU,CAAEZ,WAExBa,MAAMC,EAAAA,GAAU,eAAgBP,GAC9BQ,MAAMC,GAAaA,EAASC,SAC5BF,MAAMG,IACY,oBAAdA,EAAKC,OACRf,OAAOC,SAASe,KAAOd,EACxB,IAEAe,OAAOC,IACPC,QAAQD,MAAM,SAAUA,GACxBlB,OAAOC,SAASe,KAAOd,CAAS,GAEnC,MAlBCF,OAAOC,SAASe,KAAOd,CAkBxB,GACE,CAACT,KAEG2B,EAAAA,EAAAA,KAAC7B,EAAgB,IAAKC,GAAS,E,+LCxBxC,SAAS6B,EAAwB7B,GAChC,MAAO8B,EAAkBC,IAAuBC,EAAAA,EAAAA,UAAS,CACxDC,MAAM,EACNC,IAAK,CAAC,KAOAC,EAAYC,IAAiBJ,EAAAA,EAAAA,UALV,CACzBK,SAAU,GACVC,MAAO,GACPC,YAAY,IAKPC,EAAgBC,IACrB,MAAM,KAAEC,EAAI,MAAEC,EAAK,QAAEC,GAAYH,EAAMI,OACvCT,EAAc,IACVD,EACH,CAACO,GAA6B,aAAtBD,EAAMI,OAAOC,KAAsBF,EAAUD,GACpD,GAGII,EAAiBC,IAAsBhB,EAAAA,EAAAA,UAAS,CACtDiB,QAAQ,EACRC,IAAK,KACLC,IAAK,OAMAC,GAAWC,EAAAA,EAAAA,aAAY,sBAAuBC,IACnDC,EAAAA,EAAAA,GAAmBD,GACjBnC,MAAMe,GACFA,EAAIe,QACPO,EAAAA,EAAUC,QAAQ,GAADC,OAAIxB,EAAIyB,MACzBC,IACO1B,IAEPH,EAAoB,CAAEE,MAAM,EAAMC,IAAKA,KAChC,KAGRT,OAAOoC,IACPlC,QAAQmC,IAAI,WAAYD,GACxB9B,EAAoB,CAAEE,MAAM,EAAMC,IAAK2B,GAAI,OAoBvCE,EAAoBC,IAAyBhC,EAAAA,EAAAA,UAAS,IACvDiC,GAAaC,EAAAA,EAAAA,UAAQ,IAAM,IAAIC,KAAO,IACtCP,EAAoBA,MACzBQ,EAAAA,EAAAA,IAAmBpE,EAAMsB,KAAK+C,IAC5BlD,MAAMe,GAEFA,EAAIe,QA+EX,SAAsBqB,GACrB,IAAIC,EAAe,GACnBD,EAAUE,KAAI,CAACtB,EAAKuB,KACnBF,EAAaG,KA9Bf,SAAoCvB,EAAKD,GAExC,MAAO,CACNuB,MAAOtB,EAAM,EACbT,KAAMQ,EAAIR,KACVL,SAAUa,EAAIb,SACdC,MAAOY,EAAIZ,MACXC,WAAaW,EAAIX,YAGhBX,EAAAA,EAAAA,KAAA,SAAO+C,UAAU,8BAA6BC,SAAC,WAF/ChD,EAAAA,EAAAA,KAAA,SAAO+C,UAAU,8BAA6BC,SAAC,UAIhDC,QACCjD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CACNC,QAAQ,SACRJ,UAAU,MACVK,QAASA,IACRhC,EAAmB,CAAEC,QAAQ,EAAMC,IAAKA,EAAKC,IAAKA,IAClDyB,UAEDhD,EAAAA,EAAAA,KAAA,KAAG+C,UAAU,iCAGfzB,IAAKA,EAEP,CAKoB+B,CAA2BR,EAAOvB,GAAK,IAE1Dc,EAAsBO,EACvB,CApFIW,CAAahD,EAAIZ,MACHY,EAAIZ,KAqCbkD,KAAI,CAACtB,EAAKuB,KACjBR,EAAWkB,IAAIjC,EAAImB,GAAInB,EAAI,IArClBhB,IAEPH,EAAoB,CAAEE,MAAM,EAAMC,IAAKA,KAChC,KAGRT,OAAOoC,IACPlC,QAAQmC,IAAID,GACZ9B,EAAoB,CAAEE,MAAM,EAAMC,IAAK2B,GAAI,GAC1C,GAGJ1D,EAAAA,EAAAA,YAAU,KACTyD,GAAmB,GACjB,CAAC5D,EAAMsB,KAAK+C,KAGf,MAAMe,GAA2B/B,EAAAA,EAAAA,aAAY,gBAAgB,KAC5DgC,EAAAA,EAAAA,IAAsBtC,EAAgBG,IAAImB,IACxClD,MAAMe,IAsBT,IAA0BuC,EArBnBvC,EAAIe,QACPgB,EAAWqB,OAAOvC,EAAgBG,IAAImB,IACtCtC,EAAoB,CAAEE,MAAM,EAAMC,IAAKA,IAmBjBuC,EAlBL1B,EAAgBI,IAmBpCa,GAAuBuB,IACtB,MAAMC,EAAc,IAAID,GAExB,OADAC,EAAYC,OAAOhB,EAAO,GACnBe,CAAW,KApBhBzD,EAAoB,CAAEE,MAAM,EAAMC,IAAKA,IAExCc,EAAmB,CAAEC,QAAQ,EAAOC,IAAK,KAAMC,IAAK,MAAO,IAE3D1B,OAAOoC,IACP9B,EAAoB,CAAEE,MAAM,EAAMC,IAAK2B,IAChC6B,QAAQC,OAAO9B,QAqFzB,OACC+B,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAjB,SAAA,EACChD,EAAAA,EAAAA,KAACkE,EAAAA,EAAK,CACL7D,MAAM,EACN8D,WAAW,EACXC,KAAK,KACLrB,UAAU,qBAAoBC,UAE9BgB,EAAAA,EAAAA,MAACK,EAAAA,EAAI,CAACC,SApJazD,IACrB,MAAM0D,EAAcC,OAAOjE,EAAWE,UAChCgE,EAAWD,OAAOjE,EAAWG,OAC7BgE,EAAa,CAClBC,YAAavG,EAAMsB,KAAK+C,GACxBmC,OAAQxG,EAAMsB,KAAKkF,OACnB9D,KAAMP,EAAWO,KACjBL,SAAU8D,EACV7D,MAAO+D,EACP9D,WAAYJ,EAAWI,YAExBa,EAASqD,OAAOH,GAChB7D,EAAMiE,gBAAgB,EAwIS9B,SAAA,EAC5BhD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMa,OAAM,CAAChC,UAAU,mBAAkBC,UACzChD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMc,MAAK,CAACjC,UAAU,cAAaC,UACnChD,EAAAA,EAAAA,KAAA,MAAI+C,UAAU,OAAMC,SAAC,qBAGvBgB,EAAAA,EAAAA,MAACE,EAAAA,EAAMe,KAAI,CAAClC,UAAU,OAAMC,SAAA,EAC3BgB,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,iCAAgCC,SAAA,EAC9ChD,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,kBAAiBC,UAC/BgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAACnC,UAAU,OAAMC,SAAA,EAC3BhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAAAnC,SAAC,kBACZhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKe,QAAO,CACZrC,UAAU,QACV7B,KAAK,SACLJ,KAAK,WACLC,MAAOR,EAAWE,SAClB4E,SAAUzE,EACV0E,UAAQ,UAIXtF,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,kBAAiBC,UAC/BgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAAAlC,SAAA,EACVhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAAAnC,SAAC,aACZhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKe,QAAO,CACZrC,UAAU,QACV7B,KAAK,OACLJ,KAAK,OACLC,MAAOR,EAAWO,KAClBuE,SAAUzE,EACV0E,UAAQ,UAIXtF,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,kBAAiBC,UAC/BgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAAAlC,SAAA,EACVhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAAAnC,SAAC,cACZhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKe,QAAO,CACZrC,UAAU,QACV7B,KAAK,SACLJ,KAAK,QACLC,MAAOR,EAAWG,MAClB2E,SAAUzE,EACV0E,UAAQ,UAIXtF,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,+CAA8CC,UAC5DhD,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,UAASC,UACvBgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAACnC,UAAU,8CAA6CC,SAAA,EAClEhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKkB,MAAK,CACVxC,UAAU,kCACV7B,KAAK,SACLuB,GAAG,aACH3B,KAAK,aACLE,QAAST,EAAWI,WACpB0E,SAAUzE,KAGXZ,EAAAA,EAAAA,KAAA,SAAOwF,QAAQ,aAAazC,UAAU,MAAKC,SAAC,uBAOhDhD,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,QAAOC,UACrBhD,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,mBAAkBC,UAChChD,EAAAA,EAAAA,KAACyF,EAAAA,GAAS,CACTC,QA7GQ,CACf,CACC5E,KAAM,KACN6E,SAAWC,GAAQA,EAAI/C,MACvBgD,UAAU,EACVC,SAAU,OACVC,SAAU,OACVC,MAAM,GAEP,CACClF,KAAM,OACN6E,SAAWC,GAAQA,EAAI9E,KACvB+E,UAAU,EACVC,SAAU,QACVC,SAAU,QACVC,MAAM,GAEP,CACClF,KAAM,WACN6E,SAAWC,GAAQA,EAAInF,SACvBoF,UAAU,GAEX,CAAE/E,KAAM,QAAS6E,SAAWC,GAAQA,EAAIlF,MAAOmF,UAAU,GACzD,CAAE/E,KAAM,UAAW6E,SAAWC,GAAQA,EAAIjF,WAAYkF,UAAU,GAChE,CACC/E,KAAM,SACN6E,SAAWC,GAAQA,EAAI3C,OACvB4C,UAAU,GAEX,CAAE/E,KAAM,MAAO6E,SAAWC,GAAQA,EAAItE,IAAK2E,MAAM,IAiF1CvG,KAAMyC,EACN+D,YAAY,EACZC,kBAAmB,GACnBC,kBAAgB,EAChBC,UAAQ,EACRC,MAAM,sBAKVtC,EAAAA,EAAAA,MAACE,EAAAA,EAAMqC,OAAM,CAACxD,UAAU,qCAAoCC,SAAA,EAC3DhD,EAAAA,EAAAA,KAAA,OAAAgD,UACChD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CAACC,QAAQ,iBAAiBC,QAASA,IAAMhF,EAAMoI,UAAUxD,SAAC,aAIlEhD,EAAAA,EAAAA,KAAA,OAAAgD,UACChD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CACNhC,KAAK,SACLiC,QAAQ,kBACRJ,UAAU,YAAWC,SACrB,kBAOJ7B,EAAgBE,SAChB2C,EAAAA,EAAAA,MAACE,EAAAA,EAAK,CAAC7D,MAAM,EAAM8D,WAAW,EAAMC,KAAK,KAAKrB,UAAU,OAAMC,SAAA,EAC7DhD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMa,OAAM,CAAChC,UAAU,mBAAkBC,UACzChD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMc,MAAK,CAACjC,UAAU,cAAaC,UACnChD,EAAAA,EAAAA,KAAA,MAAI+C,UAAU,OAAMC,SAAC,4BAGvBhD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMe,KAAI,CAAClC,UAAU,OAAMC,SAAC,qCAG7BgB,EAAAA,EAAAA,MAACE,EAAAA,EAAMqC,OAAM,CAACxD,UAAU,qCAAoCC,SAAA,EAC3DhD,EAAAA,EAAAA,KAAA,OAAAgD,UACChD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CACNC,QAAQ,iBACRC,QAASA,IAAMhC,EAAmB,CAAEC,QAAQ,EAAOoB,GAAI,KAAMO,SAC7D,UAIFhD,EAAAA,EAAAA,KAAA,OAAAgD,UACChD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CACNC,QAAQ,kBACRJ,UAAU,YACVK,QAASA,IAAMI,EAAyBqB,SAAS7B,SACjD,gBAOJ9C,EAAiBG,OACjBL,EAAAA,EAAAA,KAACyG,EAAAA,EAAa,CACbnG,IAAKJ,EAAiBI,IACtBH,oBAAqBA,EACrB4B,IAAK7B,EAAiBI,QAK3B,C,4BAEA,U,qLCtVA,SAASoG,EAAoBtI,GAC5B,MAAO8B,EAAkBC,IAAuBC,EAAAA,EAAAA,UAAS,CACxDC,MAAM,EACNC,IAAK,CAAC,KAaAC,EAAYC,IAAiBJ,EAAAA,EAAAA,UAXV,CACzBU,KAAM,GACN6F,YAAa,GACblG,SAAU,GACVmG,gBAAiB,GACjBlG,MAAO,GACPmG,eAAe,EACfC,YAAY,EACZC,UAAW,GACXC,cAAe,KAKVpG,EAAgBC,IACrB,MAAM,KAAEC,EAAI,MAAEC,EAAK,QAAEC,GAAYH,EAAMI,OACvCT,EAAc,IACVD,EACH,CAACO,GAA6B,aAAtBD,EAAMI,OAAOC,KAAsBF,EAAUD,GACpD,EAMGS,GAAWC,EAAAA,EAAAA,aAAY,eAAgBC,IAC5CuF,EAAAA,EAAAA,IACCvF,EAAOZ,KAAKoG,OACZxF,EAAOiF,YAAYO,OACnBxF,EAAOqF,UACPrF,EAAOoF,WACPpF,EAAOkF,iBAENrH,MAAMe,GACFA,EAAIe,QACPjD,EAAMoI,UACNpI,EAAM+I,kBACNvF,EAAAA,EAAUC,QAAQ,GAADC,OAAIxB,EAAIyB,MAClBzB,IAEPH,EAAoB,CAAEE,MAAM,EAAMC,IAAKA,KAChC,KAGRT,OAAOoC,IACPlC,QAAQmC,IAAI,WAAYD,GACxB9B,EAAoB,CAAEE,MAAM,EAAMC,IAAK2B,GAAI,MAS9C,OACC+B,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAjB,SAAA,EACChD,EAAAA,EAAAA,KAACkE,EAAAA,EAAK,CAAC7D,MAAM,EAAM8D,WAAW,EAAMC,KAAK,KAAKrB,UAAU,OAAMC,UAC7DgB,EAAAA,EAAAA,MAACK,EAAAA,EAAI,CAACC,SARazD,IACrBW,EAASqD,OAAOtE,GAChBM,EAAMiE,gBAAgB,EAMS9B,SAAA,EAC5BhD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMa,OAAM,CAAChC,UAAU,mBAAkBC,UACzChD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMc,MAAK,CAACjC,UAAU,cAAaC,UACnChD,EAAAA,EAAAA,KAAA,MAAI+C,UAAU,OAAMC,SAAC,yBAGvBgB,EAAAA,EAAAA,MAACE,EAAAA,EAAMe,KAAI,CAAClC,UAAU,OAAMC,SAAA,EAC3BgB,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,iCAAgCC,SAAA,EAC9ChD,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,kBAAiBC,UAC/BgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAACnC,UAAU,OAAMC,SAAA,EAC3BhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAAAnC,SAAC,sBACZhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKe,QAAO,CACZrC,UAAU,QACV7B,KAAK,OACLJ,KAAK,OACLC,MAAOR,EAAWO,KAClBuE,SAAUzE,EACV0E,UAAQ,UAIXtF,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,kBAAiBC,UAC/BgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAAAlC,SAAA,EACVhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAAAnC,SAAC,kBACZhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKe,QAAO,CACZrC,UAAU,QACV7B,KAAK,OACLJ,KAAK,YACLC,MAAOR,EAAWwG,UAClB1B,SAAUzE,EACV0E,UAAQ,aAKZtB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAAAlC,SAAA,EACVhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAAAnC,SAAC,6BACZhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKe,QAAO,CACZlE,KAAK,OACLJ,KAAK,cACLC,MAAOR,EAAWoG,YAClBtB,SAAUzE,EACV0E,UAAQ,QAIVtB,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,wDAAuDC,SAAA,EACrEhD,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,kBAAiBC,UAC/BgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAACnC,UAAU,OAAMC,SAAA,EAC3BhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAAAnC,SAAC,wBACZhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKe,QAAO,CACZlE,KAAK,SACLJ,KAAK,kBACLC,MAAOR,EAAWqG,gBAClBvB,SAAUzE,EACV0E,UAAQ,UAIXtF,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,+CAA8CC,UAC5DhD,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,UAASC,UACvBgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAACnC,UAAU,8CAA6CC,SAAA,EAClEhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKkB,MAAK,CACVxC,UAAU,kCACV7B,KAAK,SACLuB,GAAG,aACH3B,KAAK,aACLE,QAAST,EAAWuG,WACpBzB,SAAUzE,KAGXZ,EAAAA,EAAAA,KAAA,SAAOwF,QAAQ,aAAazC,UAAU,MAAKC,SAAC,mCAQjDgB,EAAAA,EAAAA,MAACE,EAAAA,EAAMqC,OAAM,CAACxD,UAAU,qCAAoCC,SAAA,EAC3DhD,EAAAA,EAAAA,KAAA,OAAAgD,UACChD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CAACC,QAAQ,iBAAiBC,QAASA,IAAMhF,EAAMoI,UAAUxD,SAAC,UAIlEhD,EAAAA,EAAAA,KAAA,OAAAgD,UACChD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CACNhC,KAAK,SACLiC,QAAQ,kBACRJ,UAAU,YAAWC,SACrB,kBAOJ9C,EAAiBG,OACjBL,EAAAA,EAAAA,KAACyG,EAAAA,EAAa,CACbnG,IAAKJ,EAAiBI,IACtBH,oBAAqBA,EACrB4B,IAAK7B,EAAiBI,QAK3B,C,4BAEA,U,0PClKe,SAAS8G,EAAgBhJ,GACvC,MAAOiJ,EAAWC,IAAgBlH,EAAAA,EAAAA,WAAS,GAG3C,SAASmH,IACRnJ,EAAMoJ,mBAAmB,CAAEnH,MAAM,IACjCiH,GAAa,EACd,CACA,MAAOpH,EAAkBC,IAAuBC,EAAAA,EAAAA,UAAS,CACxDC,MAAM,EACNC,IAAK,CAAC,KAGAmH,EAAKC,IAAUtH,EAAAA,EAAAA,UAAS,IACxBuH,EAAgBC,IAAqBxH,EAAAA,EAAAA,UAAS,CACpDW,MAAO,MACP8G,MAAO,SAEDC,EAAoBC,IAAyB3H,EAAAA,EAAAA,UAAS,CAC5DW,MAAO,KACP8G,MAAO,QAGDG,EAAoBC,IAAyB7H,EAAAA,EAAAA,UAAS,CAC5DW,MAAO,SACP8G,MAAO,YAGDK,EAAuBC,IAA4B/H,EAAAA,EAAAA,UAAS,CAClEW,MAAO,MACP8G,MAAO,SAqDDO,EAAQC,IAAajI,EAAAA,EAAAA,aACrBkI,EAAcC,IAAmBnI,EAAAA,EAAAA,UAAS,IAAIoI,OAC9CC,EAAYC,IAAiBtI,EAAAA,EAAAA,aAC7BuI,EAAcC,IAAmBxI,EAAAA,EAAAA,YAElCyI,GAAoBpH,EAAAA,EAAAA,aAAY,iBAAkBqH,IAChDC,EAAAA,EAAAA,IAAcD,GACnBvJ,MAAMe,IAEFA,EAAIe,QACPkG,IACA3F,EAAAA,EAAUC,QAAQ,GAADC,OAAIxB,EAAIyB,IAAG,UAG5B6G,GAAgB,GAChBzI,EAAoB,CAAEE,MAAM,EAAMC,IAAKA,IACxC,IAEAT,OAAOoC,IACPlC,QAAQmC,IAAID,GACZ9B,EAAoB,CAAEE,MAAM,EAAMC,IAAK2B,GAAI,MAoG9C,OACC+B,EAAAA,EAAAA,MAACE,EAAAA,EAAK,CACL7D,KAAMgH,EACNlD,WAAW,EACXC,KAAK,KACLrB,UAAU,qBAAoBC,SAAA,EAE9BhD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMa,OAAM,CAAChC,UAAU,mBAAkBC,UACzChD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMc,MAAK,CAACjC,UAAU,cAAaC,UACnChD,EAAAA,EAAAA,KAAA,MAAI+C,UAAU,OAAMC,SAAC,qBAGvBhD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMe,KAAI,CAAClC,UAAU,QAAOC,UAC5BgB,EAAAA,EAAAA,MAAA,OAAAhB,SAAA,EACChD,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,MAAKC,UACnBhD,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,0CAAyCC,UACvDhD,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,iBAAgBC,UAC9BgB,EAAAA,EAAAA,MAACK,EAAAA,EAAI,CAAArB,SAAA,EACJgB,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,aAAYC,SAAA,EAC1BhD,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,WAAUC,UACxBgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAAAlC,SAAA,EACVhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAACpC,UAAU,aAAYC,SAAC,UACnChD,EAAAA,EAAAA,KAACgJ,EAAAA,GAAM,CACNC,QAjMO,CAClB,CACClI,MAAO,SACP8G,MAAO,UAER,CACC9G,MAAO,SACP8G,MAAO,UAER,CACC9G,MAAO,SACP8G,MAAO,WAuLG9G,MAAOiH,EACP3C,SAAWsC,GACVM,EAAsBN,GAEvBuB,QAAQC,EAAAA,EAAAA,IACPC,EAAAA,GACAC,EAAAA,YAKJrJ,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,WAAUC,UACxBgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAAAlC,SAAA,EACVhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAACpC,UAAU,aAAYC,SAAC,YACnChD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKe,QAAO,CACZlE,KAAK,OACLoI,YAAY,SACZvI,MAAOqH,EACP/C,SAAWpD,IAAC,IAAAsH,EAAA,OACXlB,EAAwB,QAAfkB,EAACtH,EAAEhB,OAAOF,aAAK,IAAAwI,OAAA,EAAdA,EAAgBC,cAAc,EAEzClE,UAAQ,UAIoB,WAA7B0C,EAAmBjH,OACS,WAA7BiH,EAAmBjH,SACnBf,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,WAAUC,UACxBgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAAAlC,SAAA,EACVhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAACpC,UAAU,aAAYC,SAAC,UACnChD,EAAAA,EAAAA,KAACyJ,IAAU,CACVC,SAAUpB,EACVjD,SA9DasE,IACzBpB,EAAgBoB,EAAK,EA8DT5G,UAAU,eACV6G,WAAW,qBAKf5J,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,WAAUC,UACxBgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAAAlC,SAAA,EACVhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAACpC,UAAU,aAAYC,SACH,WAA7BgF,EAAmBjH,MACjB,MACA,SAEJf,EAAAA,EAAAA,KAACqE,EAAAA,EAAKe,QAAO,CACZlE,KAAK,SACLoI,YAAY,MACZvI,MAAO0G,EACPpC,SAAWpD,GAAMyF,EAAOzF,EAAEhB,OAAOF,OACjCuE,UAAQ,SAImB,WAA7B0C,EAAmBjH,QACnBiD,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAjB,SAAA,EACChD,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,WAAUC,UACxBgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAAAlC,SAAA,EACVhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAACpC,UAAU,aAAYC,SAAC,kBAGnChD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKe,QAAO,CACZlE,KAAK,SACLoI,YAAY,eACZvI,MAAO0H,EACPpD,SAAWpD,GAAMyG,EAAczG,EAAEhB,OAAOF,OACxCuE,UAAQ,UAIXtF,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,WAAUC,UACxBgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAAAlC,SAAA,EACVhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAACpC,UAAU,aAAYC,SAAC,iBAGnChD,EAAAA,EAAAA,KAACgJ,EAAAA,GAAM,CACNC,QAxOK,CAClB,CACClI,MAAO,KACP8G,MAAO,MAER,CACC9G,MAAO,KACP8G,MAAO,OAkOK9G,MAAO+G,EACPzC,SAAWsC,GACVI,EAAsBJ,GAEvBuB,QAAQC,EAAAA,EAAAA,IACPC,EAAAA,GACAC,EAAAA,eAQNrJ,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,WAAUC,UACxBgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAAAlC,SAAA,EACVhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAACpC,UAAU,aAAYC,SAAC,YACnChD,EAAAA,EAAAA,KAACgJ,EAAAA,GAAM,CACNC,QArQI,CACf,CACClI,MAAO,MACP8G,MAAO,OAER,CACC9G,MAAO,OACP8G,MAAO,SA+PG9G,MAAO4G,EACPtC,SAAWsC,GACVC,EAAkBD,GAEnB2B,YAAY,wBACZJ,QAAQC,EAAAA,EAAAA,IACPC,EAAAA,GACAC,EAAAA,YAKJrJ,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,WAAUC,UACxBgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAAAlC,SAAA,EACVhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAACpC,UAAU,aAAYC,SAAC,aACnChD,EAAAA,EAAAA,KAACgJ,EAAAA,GAAM,CACNC,QArSW,CACtB,CACClI,MAAO,MACP8G,MAAO,OAER,CACC9G,MAAO,OACP8G,MAAO,QAER,CACC9G,MAAO,MACP8G,MAAO,QA2RG9G,MAAOmH,EACP7C,SAAWsC,GACVQ,EAAyBR,GAE1BuB,QAAQC,EAAAA,EAAAA,IACPC,EAAAA,GACAC,EAAAA,eAMLrJ,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,aAAYC,UAC1BgB,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,gBAAeC,SAAA,EAC7BhD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CACNhC,KAAK,SACL6B,UAAU,6BACVK,QAtLeyG,KACzBjC,EAAkB,CACjB7G,MAAO,MACP8G,MAAO,QAERE,EAAsB,CACrBhH,MAAO,KACP8G,MAAO,OAERI,EAAsB,CACrBlH,MAAO,SACP8G,MAAO,WAERM,EAAyB,CACxBpH,MAAO,MACP8G,MAAO,QAERH,EAAO,IACPgB,EAAc,MACdH,EAAgB,IAAIC,KAAO,EAmKUxF,SAC3B,WAGDhD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CACNH,UAAU,kBACVK,QAjQenB,IACzBA,EAAE6C,iBAG4B,WAA7BkD,EAAmBjH,OACnB0G,GACc,OAAdE,QAAc,IAAdA,GAAAA,EAAgB5G,OAChBqH,GAI6B,WAA7BJ,EAAmBjH,OACnB0G,GACc,OAAdE,QAAc,IAAdA,GAAAA,EAAgB5G,OAChBqH,GAI6B,WAA7BJ,EAAmBjH,OACnB0G,GACc,OAAdE,QAAc,IAAdA,GAAAA,EAAgB5G,OAChBqH,GACAK,GACkB,OAAlBX,QAAkB,IAAlBA,GAAAA,EAAoB/G,MAdpB6H,GAAgB,GAkBhBhH,EAAAA,EAAU9B,MAAM,yBACjB,EAqOoCkD,SAC1B,6BAUN2F,IACA3E,EAAAA,EAAAA,MAACE,EAAAA,EAAK,CACL7D,KAAMsI,EACNxE,WAAW,EACXC,KAAK,KACLrB,UAAU,OAAMC,SAAA,EAEhBhD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMa,OAAM,CAAChC,UAAU,qCAAoCC,UAC3DhD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMc,MAAK,CAAAhC,UACXhD,EAAAA,EAAAA,KAAA,OAAAgD,UACChD,EAAAA,EAAAA,KAAA,MAAI+C,UAAU,OAAMC,SAAC,mBAIxBhD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMe,KAAI,CAAClC,UAAU,OAAO+G,MAAO,CAAEC,gBAAiB,SAAU/G,UAChEhD,EAAAA,EAAAA,KAAA,SACC8J,MAAO,CACNE,MAAO,MACPC,OAAQ,SACRC,eAAgB,YACflH,UAEFgB,EAAAA,EAAAA,MAAA,SAAAhB,SAAA,EACCgB,EAAAA,EAAAA,MAAA,MAAAhB,SAAA,EACChD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,SAAC,gBAC/BhD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,SAAC,QAC/BhD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,UAC7BhD,EAAAA,EAAAA,KAAA,KAAAgD,SAAIgF,EAAmBjH,cAGzBiD,EAAAA,EAAAA,MAAA,MAAAhB,SAAA,EACChD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,SAAC,YAC/BhD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,SAAC,QAC/BhD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,UAC7BhD,EAAAA,EAAAA,KAAA,KAAAgD,SAAIoF,SAGwB,WAA7BJ,EAAmBjH,QACnBf,EAAAA,EAAAA,KAAA,MAAAgD,SACEsF,IACAtE,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAjB,SAAA,EACChD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,SAAC,UAC/BhD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,SAAC,QAC/BhD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,UAC5BoH,EAAAA,EAAAA,SAAO9B,EAAc,sBAM3BtE,EAAAA,EAAAA,MAAA,MAAAhB,SAAA,EACChD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,SACC,WAA7BgF,EAAmBjH,MAAqB,MAAQ,SAElDf,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,SAAC,QAC/BhD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,UAC7BhD,EAAAA,EAAAA,KAAA,KAAAgD,SAAIyE,SAGwB,WAA7BO,EAAmBjH,QACnBiD,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAjB,SAAA,EACCgB,EAAAA,EAAAA,MAAA,MAAAhB,SAAA,EACChD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,SAAC,kBAC/BhD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,SAAC,QAC/BhD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,UAC7BhD,EAAAA,EAAAA,KAAA,KAAAgD,SAAIyF,UAGNzE,EAAAA,EAAAA,MAAA,MAAAhB,SAAA,EACChD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,SAAC,iBAC/BhD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,SAAC,QAC/BhD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,UAC7BhD,EAAAA,EAAAA,KAAA,KAAAgD,SAAI8E,EAAmB/G,iBAK3BiD,EAAAA,EAAAA,MAAA,MAAAhB,SAAA,EACChD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,SAAC,YAC/BhD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,SAAC,QAC/BhD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,UAC7BhD,EAAAA,EAAAA,KAAA,KAAAgD,SAAI2E,EAAe5G,cAGrBiD,EAAAA,EAAAA,MAAA,MAAAhB,SAAA,EACChD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,SAAC,aAC/BhD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,SAAC,QAC/BhD,EAAAA,EAAAA,KAAA,MAAI8J,MAAO,CAAEK,MAAO,SAAUnH,UAC7BhD,EAAAA,EAAAA,KAAA,KAAAgD,SAAIkF,EAAsBnH,qBAO/BiD,EAAAA,EAAAA,MAACE,EAAAA,EAAMqC,OAAM,CAACxD,UAAU,qCAAoCC,SAAA,EAC3DhD,EAAAA,EAAAA,KAAA,OAAAgD,UACChD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CAACC,QAAQ,kBAAkBC,QA9UpBnB,IAErB,IAAIP,EAEHA,EADgC,WAA7BsG,EAAmBjH,MACb,CACRsJ,WAAY,KACZC,YAAalC,EACbzD,YAAaH,OAAOpG,EAAMqE,IAC1B8H,SAAU/F,OAAOiD,GACjB+C,YAAa7C,EAAe5G,MAC5B0J,QAASvC,EAAsBnH,OAEO,WAA7BiH,EAAmBjH,MACpB,CACRsJ,WAAY,SACZC,YAAalC,EACbsC,aAAaN,EAAAA,EAAAA,SAAO9B,EAAc,cAClCqC,aAAcnG,OAAOiE,GACrBmC,YAAa9C,EAAmB/G,MAChC4D,YAAaH,OAAOpG,EAAMqE,IAC1B8H,SAAU/F,OAAOiD,GACjB+C,YAAa7C,EAAe5G,MAC5B0J,QAASvC,EAAsBnH,OAGvB,CACRsJ,WAAY,SACZC,YAAalC,EACbsC,aAAaN,EAAAA,EAAAA,SAAO9B,EAAc,cAClC3D,YAAaH,OAAOpG,EAAMqE,IAC1B8H,SAAU/F,OAAOiD,GACjB+C,YAAa7C,EAAe5G,MAC5B0J,QAASvC,EAAsBnH,OAGjCW,GAAUmH,EAAkBhE,OAAOnD,EAAO,EA2SqBsB,SAAC,eAI1DhD,EAAAA,EAAAA,KAAA,OAAAgD,UACChD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CACNC,QAAQ,iBACRC,QAASA,IAAMwF,GAAgB,GAAO5F,SACtC,mBAQJ9C,EAAiBG,OACjBL,EAAAA,EAAAA,KAACyG,EAAAA,EAAa,CACbnG,IAAKJ,EAAiBI,IACtBH,oBAAqBA,EACrB4B,IAAK7B,EAAiBI,IAAIyB,YAK9B/B,EAAAA,EAAAA,KAACkE,EAAAA,EAAMqC,OAAM,CAACxD,UAAU,qCAAoCC,UAC3DhD,EAAAA,EAAAA,KAAA,OAAAgD,UACChD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CAACC,QAAQ,iBAAiBC,QAASmE,EAAWvE,SAAC,iBAO3D,C,wQClgBO,MAAM6H,UAAiBC,EAAAA,UAC7BC,WAAAA,CAAY3M,GACX4M,MAAM5M,GAAO,KAWd+I,gBAAkB,KACjB8D,KAAKC,SAAS,CAAEC,WAAW,KAC3BC,EAAAA,EAAAA,MACE7L,MAAMe,GACFA,EAAIe,QACP4J,KAAKC,SAAS,CAAEG,aAAc/K,EAAKgL,cAAc,IAC1ChL,IAEP2K,KAAKC,SAAS,CACbG,aAAc,KACdC,cAAc,EACdC,SAAUjL,EAAIyB,OAER,KAGRlC,OAAOoC,IACPlC,QAAQmC,IAAI,WAAYD,EAAE,IAE1BuJ,SAAQ,KACRP,KAAKC,SAAS,CAAEC,WAAW,GAAQ,GAClC,EACF,KAOFM,8BAAgC,KAC/BR,KAAKC,SAAS,CAAEQ,sBAAsB,GAAO,EAC5C,KACFC,0BAA4B,KAC3BV,KAAKC,SAAS,CAAEQ,sBAAsB,GAAQ,EA3C9CT,KAAKW,MAAQ,CACZF,sBAAsB,EACtBJ,cAAc,EACdD,aAAc,KACdF,WAAW,EACXI,SAAU,IAEXN,KAAK9D,gBAAkB8D,KAAK9D,gBAAgB0E,KAAKZ,KAClD,CA2BAa,iBAAAA,GACCb,KAAK9D,iBACN,CASA4E,MAAAA,GACC,MAAM,aAAEV,EAAY,aAAEC,EAAY,SAAEC,GAAaN,KAAKW,MAGtD,OAAKN,GAcJtH,EAAAA,EAAAA,MAAA,OAAAhB,SAAA,EACCgB,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,iBAAgBC,SAAA,EAC9BgB,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,uDAAsDC,SAAA,EACpEhD,EAAAA,EAAAA,KAAA,MAAI+C,UAAU,aAAYC,SAAC,cAC3BgB,EAAAA,EAAAA,MAAA,UACCjB,UAAU,mCACVK,QAAS6H,KAAKQ,8BAA8BzI,SAAA,CAE3C,IAAI,kBACW,WAGlBgB,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,0BAAyBC,SAAA,CACrB,OAAjBqI,IACArL,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,OAAMC,SAAEuI,GAAYA,IAEvB,OAAZF,QAAY,IAAZA,OAAY,EAAZA,EAAc3L,KAAKkD,KAAKoJ,IACxBhM,EAAAA,EAAAA,KAACiM,EAAAA,EAAa,CAEbD,SAAUA,EACVE,aAAcjB,KAAK7M,MACnB+I,gBAAiB8D,KAAK9D,iBAHjB6E,EAASvJ,YAQjBwI,KAAKW,MAAMF,uBACX1L,EAAAA,EAAAA,KAAC0G,EAAAA,EAAmB,CACnBF,QAASyE,KAAKU,0BACdxE,gBAAiB8D,KAAK9D,sBAzCxBnH,EAAAA,EAAAA,KAAA,OACC+C,UAAU,mDACV+G,MAAO,CAAEqC,OAAQ,QAASnJ,UAE1BhD,EAAAA,EAAAA,KAACoM,EAAAA,EAAO,CACPjI,UAAU,SACV2F,MAAO,CAAEE,MAAO,OAAQmC,OAAQ,WAwCrC,EAGD,SAAeE,EAAAA,EAAAA,GAASxB,G,4RCxFxB,MAAMoB,UAAsBnB,EAAAA,UAC3BC,WAAAA,CAAY3M,GACX4M,MAAM5M,GAAO,KAuBdkO,gBAAmB5M,IAClB,MAAMiF,EAAcjF,EAAK+C,GACzB7D,OAAO2N,KAAK,yBAADzK,OAA0B6C,GAAe,SAAS,EAC5D,KAEF6H,iBAAoB9M,IACnB,MAAMiF,EAAcjF,EAAK+C,GACzB7D,OAAO2N,KAAK,+BAADzK,OAAgC6C,GAAe,SAAS,EAClE,KAEF8H,wBAA0B,KACzBxB,KAAKC,SAAS,CAAEwB,kBAAmB,CAAErL,QAAQ,EAAOoB,GAAI,EAAGnB,IAAK,CAAC,IAAM,EACtE,KAEFqL,0BAA4BC,UAC3B,MAAM,KAAE9L,EAAI,QAAEE,GAAYH,EAAMI,OAChCgK,KAAKC,SAAS,CACb,CAACpK,GAAOE,IAGT,IACC,MAAM6L,QAAeC,EAAAA,EAAAA,IACpB7B,KAAK7M,MAAM4N,SAASe,cAActK,GAClCzB,GAEDiK,KAAKC,SAAS,CAAEhL,iBAAkB,CAAEG,MAAM,EAAMC,IAAKuM,IACtD,CAAE,MAAO/M,GACRmL,KAAKC,SAAS,CAAEhL,iBAAkB,CAAEG,MAAM,EAAMC,IAAKR,IACtD,GACC,KACFkN,2BAA6BJ,UAC5B,MAAM,KAAE9L,EAAI,QAAEE,GAAYH,EAAMI,OAChCgK,KAAKC,SAAS,CACb,CAACpK,GAAOE,IAGT,IACC,MAAM6L,QAAeI,EAAAA,EAAAA,IACpBhC,KAAK7M,MAAM4N,SAASe,cAActK,GAClCzB,GAEDiK,KAAKC,SAAS,CAAEhL,iBAAkB,CAAEG,MAAM,EAAMC,IAAKuM,IACtD,CAAE,MAAO/M,GACRmL,KAAKC,SAAS,CAAEhL,iBAAkB,CAAEG,MAAM,EAAMC,IAAKR,IACtD,GACC,KAEFoN,kBAAoBN,UACnB,IACC,MAAMC,QAAeM,EAAAA,EAAAA,MACrB,IAAIC,EAAc,GAClB,MAAMC,EAAUC,OAAO5L,OAAOmL,EAAOnN,MACrC,IAAK,IAAI6N,EAAI,EAAGA,EAAIF,EAAQG,OAAQD,IAAK,CACxC,IAAIjM,EAAM+L,EAAQE,GAClBH,EAAYtK,KAAK,CAAE+E,MAAOvG,EAAImM,oBAAqB1M,MAAOO,EAAImB,IAC/D,CACAwI,KAAKC,SAAS,CAAEjC,QAASmE,GAC1B,CAAE,MAAOtN,GACRmL,KAAKC,SAAS,CAAEhL,iBAAkB,CAAEG,MAAM,EAAMC,IAAKR,IACtD,GACC,KAEF4N,2BAA6Bd,MAAOnK,EAAIuJ,KAEvC,IACC,MAAMa,QAAec,EAAAA,EAAAA,IAA8BlL,GAEnDwI,KAAKC,SAAS,CAAE0C,0BAA2B,CAAEvM,QAAQ,EAAOoB,GAAI,KAC5DoK,EAAOxL,QACV2K,EAAS6B,qBAAsB,EAC/B5C,KAAKiC,oBACLtL,EAAAA,EAAUC,QAAQ,GAADC,OAAI+K,EAAO9K,OAE5BkJ,KAAKC,SAAS,CAAEhL,iBAAkB,CAAEG,MAAM,EAAMC,IAAKuM,IAEvD,CAAE,MAAO/M,GACRmL,KAAKC,SAAS,CAAEhL,iBAAkB,CAAEG,MAAM,EAAMC,IAAKR,IACtD,GACC,KAEFgO,qBAAuBlB,UACtB,IACC,MAAMC,QAAekB,EAAAA,EAAAA,IAAetL,GAEpCwI,KAAKC,SAAS,CAAE8C,oBAAqB,CAAE3M,QAAQ,EAAOoB,GAAI,KACtDoK,EAAOxL,QACVO,EAAAA,EAAUC,QAAQ,GAADC,OAAI+K,EAAO9K,MAC5BkJ,KAAK7M,MAAM+I,mBAEX8D,KAAKC,SAAS,CAAEhL,iBAAkB,CAAEG,MAAM,EAAMC,IAAKuM,IAEvD,CAAE,MAAO/M,GACRmL,KAAKC,SAAS,CAAEhL,iBAAkB,CAAEG,MAAM,EAAMC,IAAKR,IACtD,GACC,KAEFmO,4BAA8BrB,MAAOnK,EAAIuJ,KACxC,IACC,MAAMa,QAAeqB,EAAAA,EAAAA,IACpBzL,EACAwI,KAAKW,MAAMuC,2BAA2BpN,OAGnC8L,EAAOxL,QACV2K,EAAS6B,qBAAsB,EAC/B5C,KAAKC,SAAS,CAAEkD,uBAAwB,CAAE/M,QAAQ,EAAOoB,GAAI,KAC7Db,EAAAA,EAAUC,QAAQ,GAADC,OAAI+K,EAAO9K,OAE5BkJ,KAAKC,SAAS,CAAEhL,iBAAkB,CAAEG,MAAM,EAAMC,IAAKuM,KAGtD5B,KAAKC,SAAS,CAAEiD,2BAA4B,MAC7C,CAAE,MAAOrO,GACRmL,KAAKC,SAAS,CAAEhL,iBAAkB,CAAEG,MAAM,EAAMC,IAAKR,IACtD,GACC,KAEFK,oBAAsB,KACrB8K,KAAKC,SAAS,CAAEhL,iBAAkB,CAAEG,MAAM,EAAOC,IAAK,CAAC,IAAM,EAC5D,KAEFkH,mBAAqB,KACpByD,KAAKC,SAAS,CAAEmD,gBAAiB,CAAEhO,MAAM,EAAOC,IAAK,CAAC,IAAM,EAC3D,KAEFgO,0BAA6BhN,IAC5B2J,KAAKC,SAAS,CACbmD,gBAAiB,CAAEhO,MAAM,EAAMiB,IAAKA,IACnC,EACD,KAEFiN,+BAAkCjN,IACjC2J,KAAKC,SAAS,CACbsD,4BAA6B,CAAEnN,QAAQ,EAAMC,IAAKA,IACjD,EACD,KAEFmN,gCAAkC,KACjCxD,KAAKC,SAAS,CACbsD,4BAA6B,CAAEnN,QAAQ,EAAOC,IAAK,CAAC,IACnD,EACD,KAEFoN,mBAAqB9B,UACpB3K,EAAE6C,iBASF,MAAMpD,EAAS,CACdiD,YAAasG,KAAKW,MAAMc,kBAAkBjK,GAC1C3B,KAAMmK,KAAKW,MAAM9K,KACjB6F,YAAasE,KAAKW,MAAMjF,YACxBC,gBAAiBqE,KAAKW,MAAMhF,gBAC5BG,UAAWkE,KAAKW,MAAM7E,WAGvB,IACC,MAAM8F,QAAe8B,EAAAA,EAAAA,IAAmBjN,GAExCuJ,KAAKC,SAAS,CAAEwB,kBAAmB,CAAErL,QAAQ,EAAOoB,GAAI,EAAGmM,GAAI,CAAC,KAC5D/B,EAAOxL,QACVO,EAAAA,EAAUC,QAAQ,GAADC,OAAI+K,EAAO9K,MAC5BkJ,KAAK7M,MAAM+I,mBAEX8D,KAAKC,SAAS,CAAEhL,iBAAkB,CAAEG,MAAM,EAAMC,IAAKuM,IAEvD,CAAE,MAAO/M,GACRmL,KAAKC,SAAS,CAAEhL,iBAAkB,CAAEG,MAAM,EAAMC,IAAKR,IACtD,GACC,KAEF+O,oBAAuB7C,IAEtBf,KAAKC,SAAS,CAAEpK,KAAMkL,EAASlL,OAC/BmK,KAAKC,SAAS,CAAEvE,YAAaqF,EAASrF,cACtCsE,KAAKC,SAAS,CAAEnE,UAAWiF,EAASjF,YACpCkE,KAAKC,SAAS,CAAEtE,gBAAiBoF,EAASpF,kBAE1CqE,KAAKC,SAAS,CACbwB,kBAAmB,CAClBrL,QAAQ,EACRoB,GAAIuJ,EAASvJ,GACbnB,IAAK0K,IAEL,EAnNFf,KAAKiB,aAAe9N,EAAM8N,aAC1BjB,KAAKW,MAAQ,CACZkD,eAAe,EACfC,eAAgB9D,KAAK7M,MAAM4N,SAASe,cAAciC,aAClDC,eAAgBhE,KAAK7M,MAAM4N,SAASe,cAAckC,eAClD/O,iBAAkB,CAAEG,MAAM,EAAOC,IAAK,CAAC,GACvC+N,gBAAiB,CAAEhO,MAAM,EAAOC,IAAK,CAAC,GACtC0N,oBAAqB,CAAE3M,QAAQ,EAAOoB,GAAI,GAC1CiK,kBAAmB,CAAErL,QAAQ,EAAOoB,GAAI,EAAGnB,IAAK,CAAC,GACjDsM,0BAA2B,CAAEvM,QAAQ,EAAOoB,GAAI,GAChDuL,oBAAqB,CAAE3M,QAAQ,EAAOoB,GAAI,GAC1C2L,uBAAwB,CAAE/M,QAAQ,EAAOoB,GAAI,GAC7C0L,2BAA4B,KAC5BK,4BAA6B,CAAEnN,QAAQ,EAAOC,IAAK,CAAC,GACpDR,KAAM,GACN6F,YAAa,GACbC,gBAAiB,EACjBG,UAAW,GACXkC,QAAS,GAEX,CAiMA8C,MAAAA,GACyBd,KAAK7M,MAAM+I,gBAAnC,MACM6E,EAAWf,KAAK7M,MAAM4N,SAASe,cAC/B1B,EAAeJ,KAAK7M,MAAM4N,SAG1BkD,EAAc7D,EAAa8D,QAAQvM,KAAKwM,IACtC,CACNC,YAAaD,EAAKzF,KAAK2F,MAAM,EAAG,IAChCC,IAAK/K,OAAO4K,EAAKG,SAInB,IAAIC,EAAa,GACbC,EAAY,GACZC,EAAY,EAChB,IAAK,IAAIC,KAAYT,EACpBM,EAAW1M,KAAKoM,EAAYS,GAAUN,aACtCK,GAAaR,EAAYS,GAAUJ,IACnCE,EAAU3M,KAAK4M,GAEhB,MAAM,iBAAExP,EAAgB,eAAE6O,EAAc,eAAEE,GAAmBhE,KAAKW,MAC5DgE,EAAW,CAChBC,OAAQL,EACRM,SAAU,CACT,CACCjI,MAAO,WACPnI,KAAM+P,EACN1F,gBAAiB,0BACjBgG,YAAa,wBACbC,YAAa,EACbC,MAAM,KAqCT,IAAKhF,KAAKW,MAAMkD,cACf,OAAO9O,EAAAA,EAAAA,KAAAiE,EAAAA,SAAA,IAmBR,OACCD,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAjB,SAAA,EACChD,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,wBAAuBC,UACrCgB,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,qCAAoCC,SAAA,EAClDgB,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,oDAAmDC,SAAA,EACjEgB,EAAAA,EAAAA,MAAA,MAAIjB,UAAU,YAAY+G,MAAO,CAAEhE,SAAU,OAAQ9C,SAAA,EACpDgB,EAAAA,EAAAA,MAAA,QAAMjB,UAAU,OAAMC,SAAA,CAAEgJ,EAASvJ,GAAG,OAAS,KAC7CzC,EAAAA,EAAAA,KAAA,QAAAgD,SAAOgJ,EAASlL,WAEjBd,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,OAAMC,UACpBhD,EAAAA,EAAAA,KAACkQ,EAAAA,EAAc,CACdC,UAAU,OACVC,SA3BNpQ,EAAAA,EAAAA,KAACqQ,EAAAA,EAAO,CAAC5N,GAAG,UAASO,UACpBhD,EAAAA,EAAAA,KAAA,OACC8J,MAAO,CACNC,gBAAiB,QACjBI,MAAO,QACPmG,QAAS,WACTC,aAAc,EACdC,OAAQ,QACPxN,UAEFhD,EAAAA,EAAAA,KAAA,UAAAgD,SAASgJ,EAASrF,kBAkBd8J,iBAAiB,iBAAgBzN,UAEjChD,EAAAA,EAAAA,KAAA,KACC+C,UAAU,kCACV+G,MAAO,CAAE4G,SAAU,mBAKvB1Q,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,4BAA2BC,UACzCgB,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,gBAAeC,SAAA,EAC7BhD,EAAAA,EAAAA,KAAC2Q,EAAAA,GAAI,CAACjR,KAAMkQ,EAAU3G,QA5ER,CACnB2H,QAAS,CACRC,OAAQ,CACPC,WAAW,IAGbC,OAAQ,CACPC,SAAS,GAEVC,OAAQ,CACPC,MAAO,CACN,CACCC,UAAW,CACVhH,MAAO,2BAERiH,MAAO,CACNJ,SAAS,KAIZK,MAAO,CACN,CACCF,UAAW,CACVhH,MAAO,2BAERiH,MAAO,CACNJ,SAAS,OAkDoC7E,OAAQ,KACnDH,EAAS6B,qBACT7J,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,oDAAmDC,SAAA,EACjEgB,EAAAA,EAAAA,MAAA,MAAIjB,UAAU,YAAWC,SAAA,EACxBhD,EAAAA,EAAAA,KAAA,QAAM+C,UAAU,OAAMC,SAAEgJ,EAASsF,mBAChCtF,EAASuF,YAAa,QAExBvR,EAAAA,EAAAA,KAAA,UACC+C,UAAU,0BACVK,QAASA,IACR6H,KAAKC,SAAS,CACb0C,0BAA2B,CAC1BvM,QAAQ,EACRoB,GAAIuJ,EAASvJ,MAGfO,UAEDhD,EAAAA,EAAAA,KAAA,KAAG+C,UAAU,qCAIfiB,EAAAA,EAAAA,MAACd,EAAAA,EAAM,CACNC,QAAQ,OACRJ,UAAU,YACVK,QAASA,KACR6H,KAAKC,SAAS,CACbkD,uBAAwB,CACvB/M,QAAQ,EACRoB,GAAIuJ,EAASvJ,MAGfwI,KAAKiC,mBAAmB,EACvBlK,SAAA,CACF,iBACe,KACfhD,EAAAA,EAAAA,KAAA,KAAG+C,UAAU,qCAGf/C,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,4BAA2BC,UACzChD,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,oBAAmBC,UACjCgB,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,oDAAmDC,SAAA,EACjEgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAACnC,UAAU,iCAAgCC,SAAA,EACrDhD,EAAAA,EAAAA,KAAA,OACCwF,QAAQ,iBACRzC,UAAU,MACV+G,MAAO,CAAEK,MAAO,SAAUnH,SAC1B,aAGDhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKkB,MAAK,CACVxC,UAAU,6BACV7B,KAAK,SACLuB,GAAE,kBAAAX,OAAoBkK,EAASvJ,IAC/B3B,KAAK,iBACLE,QAAS+N,EACT1J,SAAU4F,KAAK0B,+BAGhBX,EAAS6B,qBACT7J,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAACnC,UAAU,0CAAyCC,SAAA,EAC9DhD,EAAAA,EAAAA,KAAA,OAAKwF,QAAQ,iBAAiBzC,UAAU,MAAKC,SAAC,oBAG9ChD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKkB,MAAK,CACVxC,UAAU,6BACV7B,KAAK,SACLuB,GAAE,kBAAAX,OAAoBkK,EAASvJ,IAC/B3B,KAAK,iBACLE,QAASiO,EACT5J,SAAU4F,KAAK+B,gCAGd,aAIPhJ,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,wDAAuDC,SAAA,EACrEgB,EAAAA,EAAAA,MAAA,SACCjB,UAAU,uBACV+G,MAAO,CACN0H,gBACC,oDACAxO,SAAA,CACF,iBACeqI,EAAaoG,gBAAiB,QAE9CzN,EAAAA,EAAAA,MAAA,SAAOjB,UAAU,0BAAyBC,SAAA,CAAC,aAC/BqI,EAAaqG,cAAe,QAExC1N,EAAAA,EAAAA,MAAA,SAAOjB,UAAU,gCAA+BC,SAAA,CAC9C,IACAgJ,EAASlF,WAAa,UAAY,gBAGrC9C,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,wDAAuDC,SAAA,EACrEgB,EAAAA,EAAAA,MAAA,UACCjB,UAAU,mCACVK,QAASA,IAAM6H,KAAKqB,gBAAgBN,GAAUhJ,SAAA,CAC9C,aACShD,EAAAA,EAAAA,KAAA,KAAG+C,UAAU,iCAAkC,QAEzDiB,EAAAA,EAAAA,MAAA,UACCjB,UAAU,0CACVK,QAASA,IAAM6H,KAAKuB,iBAAiBR,GAAUhJ,SAAA,CAC/C,SACKhD,EAAAA,EAAAA,KAAA,KAAG+C,UAAU,qCAAsC,QAEzDiB,EAAAA,EAAAA,MAAA,UACCjB,UAAU,iCACVK,QAASA,IACR6H,KAAKsD,+BAA+BvC,GACpChJ,SAAA,CACD,SACKhD,EAAAA,EAAAA,KAAA,QAAM+C,UAAU,OAAMC,SAAC,YAAU,WAGxChD,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,wDAAuDC,UACrEgB,EAAAA,EAAAA,MAAA,UACCjB,UAAU,oCACVK,QAASA,IAAM6H,KAAKqD,0BAA0BtC,GAAUhJ,SAAA,CACxD,cACY,UAIdgB,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,wDAAuDC,SAAA,EACrEgB,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,gCAA+BC,SAAA,EAC7CgB,EAAAA,EAAAA,MAAA,UACCjB,UAAU,uCACVK,QAASA,IACR6H,KAAKC,SAAS,CACb8C,oBAAqB,CACpB3M,QAAQ,EACRoB,GAAIuJ,EAASvJ,MAGfO,SAAA,CAEA,KACDhD,EAAAA,EAAAA,KAAA,KAAG+C,UAAU,mCAEdiB,EAAAA,EAAAA,MAAA,UACCjB,UAAU,gCACVK,QAASA,IAAM6H,KAAK4D,oBAAoB7C,GAAUhJ,SAAA,CAEjD,KACDhD,EAAAA,EAAAA,KAAA,KAAG+C,UAAU,8BAGf/C,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,iCAAgCC,UAC9CgB,EAAAA,EAAAA,MAAA,MAAIjB,UAAU,OAAMC,SAAA,CAAC,YAEpBgB,EAAAA,EAAAA,MAAA,QAAAhB,SAAA,CACE,KACA2O,EAAAA,EAAAA,IAAmB3F,EAASpF,oBACtB,sBAQdqE,KAAKW,MAAMgC,0BAA0BvM,SACrC2C,EAAAA,EAAAA,MAACE,EAAAA,EAAK,CAAC7D,MAAM,EAAM8D,WAAW,EAAMC,KAAK,KAAKrB,UAAU,OAAMC,SAAA,EAC7DhD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMa,OAAM,CAAChC,UAAU,mBAAkBC,UACzChD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMc,MAAK,CAACjC,UAAU,cAAaC,UACnChD,EAAAA,EAAAA,KAAA,MAAI+C,UAAU,OAAMC,SAAC,gCAGvBhD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMe,KAAI,CAAClC,UAAU,OAAMC,SAAC,kDAG7BgB,EAAAA,EAAAA,MAACE,EAAAA,EAAMqC,OAAM,CAACxD,UAAU,qCAAoCC,SAAA,EAC3DhD,EAAAA,EAAAA,KAAA,OAAAgD,UACChD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CACNC,QAAQ,iBACRC,QAASA,IACR6H,KAAKC,SAAS,CACb0C,0BAA2B,CAAEvM,QAAQ,EAAOoB,GAAI,KAEjDO,SACD,UAIFhD,EAAAA,EAAAA,KAAA,OAAAgD,UACChD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CACNC,QAAQ,kBACRJ,UAAU,YACVK,QAASA,IACR6H,KAAKyC,2BACJzC,KAAKW,MAAMgC,0BAA0BnL,GACrCuJ,GAEDhJ,SACD,gBAQJiI,KAAKW,MAAMoC,oBAAoB3M,SAC/B2C,EAAAA,EAAAA,MAACE,EAAAA,EAAK,CAAC7D,MAAM,EAAM8D,WAAW,EAAMC,KAAK,KAAKrB,UAAU,OAAMC,SAAA,EAC7DhD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMa,OAAM,CAAChC,UAAU,mBAAkBC,UACzChD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMc,MAAK,CAACjC,UAAU,cAAaC,UACnChD,EAAAA,EAAAA,KAAA,MAAI+C,UAAU,OAAMC,SAAC,yBAGvBhD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMe,KAAI,CAAClC,UAAU,OAAMC,SAAC,2CAG7BgB,EAAAA,EAAAA,MAACE,EAAAA,EAAMqC,OAAM,CAACxD,UAAU,qCAAoCC,SAAA,EAC3DhD,EAAAA,EAAAA,KAAA,OAAAgD,UACChD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CACNC,QAAQ,iBACRC,QAASA,IACR6H,KAAKC,SAAS,CACb8C,oBAAqB,CAAE3M,QAAQ,EAAOoB,GAAI,KAE3CO,SACD,UAIFhD,EAAAA,EAAAA,KAAA,OAAAgD,UACChD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CACNC,QAAQ,kBACRJ,UAAU,YACVK,QAASA,IACR6H,KAAK6C,qBACJ7C,KAAKW,MAAMoC,oBAAoBvL,GAC/BuJ,GAEDhJ,SACD,gBAQJiI,KAAKW,MAAMwC,uBAAuB/M,SAClC2C,EAAAA,EAAAA,MAACE,EAAAA,EAAK,CAAC7D,MAAM,EAAM8D,WAAW,EAAMC,KAAK,KAAKrB,UAAU,OAAMC,SAAA,EAC7DhD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMa,OAAM,CAAChC,UAAU,mBAAkBC,UACzChD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMc,MAAK,CAACjC,UAAU,cAAaC,UACnChD,EAAAA,EAAAA,KAAA,MAAI+C,UAAU,OAAMC,SAAC,6BAGvBhD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMe,KAAI,CAAClC,UAAU,QAAOC,UAC5BgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAAC0M,UAAU,qBAAoB5O,SAAA,EACzChD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAAAnC,SAAC,2BACZhD,EAAAA,EAAAA,KAACgJ,EAAAA,GAAM,CACN6I,OAAO,UACP9Q,MAAOkK,KAAKW,MAAMuC,2BAClB9I,SAAWpD,IACVgJ,KAAKC,SAAS,CAAEiD,2BAA4BlM,GAAI,EAEjDgH,QAASgC,KAAKW,MAAM3C,QACpB6I,cAAc,EACdxI,YAAY,iBACZJ,QAAQC,EAAAA,EAAAA,IACPC,EAAAA,GACAC,EAAAA,IAED/D,UAAQ,UAIXtB,EAAAA,EAAAA,MAACE,EAAAA,EAAMqC,OAAM,CAACxD,UAAU,qCAAoCC,SAAA,EAC3DhD,EAAAA,EAAAA,KAAA,OAAAgD,UACChD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CACNC,QAAQ,iBACRC,QAASA,IACR6H,KAAKC,SAAS,CACbkD,uBAAwB,CAAE/M,QAAQ,EAAOoB,GAAI,KAE9CO,SACD,UAIFhD,EAAAA,EAAAA,KAAA,OAAAgD,UACChD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CACNC,QAAQ,kBACRJ,UAAU,YACVK,QAASA,IACR6H,KAAKgD,4BACJhD,KAAKW,MAAMwC,uBAAuB3L,GAClCuJ,GAEDhJ,SACD,gBAOJiI,KAAKW,MAAM4C,4BAA4BnN,SACvCrB,EAAAA,EAAAA,KAACC,EAAAA,EAAuB,CACvBuG,QAASyE,KAAKwD,gCACd/O,KAAMuL,KAAKW,MAAM4C,4BAA4BlN,MAI9C2J,KAAKW,MAAMc,kBAAkBrL,SAC7BrB,EAAAA,EAAAA,KAACkE,EAAAA,EAAK,CAAC7D,MAAM,EAAM8D,WAAW,EAAMC,KAAK,KAAKrB,UAAU,OAAMC,UAC7DgB,EAAAA,EAAAA,MAACK,EAAAA,EAAI,CAACC,SAAU2G,KAAKyD,mBAAmB1L,SAAA,EACvChD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMa,OAAM,CAAChC,UAAU,mBAAkBC,UACzChD,EAAAA,EAAAA,KAACkE,EAAAA,EAAMc,MAAK,CAACjC,UAAU,cAAaC,UACnChD,EAAAA,EAAAA,KAAA,MAAI+C,UAAU,OAAMC,SAAC,uBAGvBgB,EAAAA,EAAAA,MAACE,EAAAA,EAAMe,KAAI,CAAClC,UAAU,OAAMC,SAAA,EAC3BgB,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,MAAKC,SAAA,EACnBhD,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,WAAUC,UACxBgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAAAlC,SAAA,EACVhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAAAnC,SAAC,sBACZhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKe,QAAO,CACZlE,KAAK,OACLJ,KAAK,OACLiR,aAAc9G,KAAKW,MAAM9K,KACzBuE,SAAWpD,IACVgJ,KAAKC,SAAS,CAAEpK,KAAMmB,EAAEhB,OAAOF,OAAQ,EAExCuE,UAAQ,UAIXtB,EAAAA,EAAAA,MAAA,OAAKjB,UAAU,WAAUC,SAAA,CACvB,KACDgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAACnC,UAAU,OAAMC,SAAA,EAC3BhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAAAnC,SAAC,wBACZhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKe,QAAO,CACZlE,KAAK,SACLJ,KAAK,kBACLiR,aAAc9G,KAAKW,MAAMhF,gBACzBvB,SAAWpD,IACVgJ,KAAKC,SAAS,CAAEtE,gBAAiB3E,EAAEhB,OAAOF,OAAQ,EAEnDuE,UAAQ,WAIXtF,EAAAA,EAAAA,KAAA,OAAK+C,UAAU,WAAUC,UACxBgB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAACnC,UAAU,OAAMC,SAAA,EAC3BhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAAAnC,SAAC,kBACZhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKe,QAAO,CACZlE,KAAK,OACLJ,KAAK,YACLiR,aAAc9G,KAAKW,MAAM7E,UACzB1B,SAAWpD,IACVgJ,KAAKC,SAAS,CAAEnE,UAAW9E,EAAEhB,OAAOF,OAAQ,EAE7CuE,UAAQ,aAMZtB,EAAAA,EAAAA,MAACK,EAAAA,EAAKa,MAAK,CAAAlC,SAAA,EACVhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKc,MAAK,CAAAnC,SAAC,6BACZhD,EAAAA,EAAAA,KAACqE,EAAAA,EAAKe,QAAO,CACZlE,KAAK,OACLJ,KAAK,cACLiR,aAAc9G,KAAKW,MAAMjF,YACzBtB,SAAWpD,IACVgJ,KAAKC,SAAS,CAAEvE,YAAa1E,EAAEhB,OAAOF,OAAQ,EAE/CuE,UAAQ,WAIXtB,EAAAA,EAAAA,MAACE,EAAAA,EAAMqC,OAAM,CAACxD,UAAU,qCAAoCC,SAAA,EAC3DhD,EAAAA,EAAAA,KAAA,OAAAgD,UACChD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CACNC,QAAQ,iBACRC,QAAS6H,KAAKwB,wBAAwBzJ,SACtC,UAIFhD,EAAAA,EAAAA,KAAA,OAAAgD,UACChD,EAAAA,EAAAA,KAACkD,EAAAA,EAAM,CACNhC,KAAK,SACLiC,QAAQ,kBACRJ,UAAU,YAAWC,SACrB,kBAQLiI,KAAKW,MAAMyC,gBAAgBhO,OAC3BL,EAAAA,EAAAA,KAACoH,EAAAA,EAAe,CACfI,mBAAoByD,KAAKzD,mBACzB/E,GAAIwI,KAAK7M,MAAM4N,SAASe,cAActK,KAGvCvC,EAAiBG,OACjBL,EAAAA,EAAAA,KAACyG,EAAAA,EAAa,CACbnG,IAAKJ,EAAiBI,IACtBH,oBAAqB8K,KAAK9K,oBAC1B4B,IAAK7B,EAAiB6B,QAK3B,EAGD,U,kIC/vBO,MAAMsH,EAAwB,CACpC2I,OAAQA,CAACC,EAAUrG,KAAK,IACpBqG,EACHlI,gBAAiB6B,EAAMsG,WAAa,OAAS,QAC7C/H,MAAOyB,EAAMsG,WAAa,QAAU,WAGzB9I,EAAsB,CAClC4I,OAAQA,CAACG,EAAMvG,KAAK,IAChBvC,EAAsB2I,OAAOG,EAAMvG,GACtC,UAAW,CACV7B,gBAAiB,YACjBI,MAAO,YAsBH,SAASiI,EAAmB5H,GAClC,IAAI6H,EAAS7H,EAAY8H,OAAO,GAO5BC,EACQ,MAAXF,GACW,MAAXA,GACW,SAAXA,GACW,SAAXA,GACW,SAAXA,EACD,MAXY,MAAXA,GACW,MAAXA,GACW,QAAXA,GACW,QAAXA,GACW,QAAXA,GAQOrS,EAAAA,EAAAA,KAAA,SAAO+C,UAAU,8BAA6BC,SAAC,QAC5CuP,GACHvS,EAAAA,EAAAA,KAAA,SAAO+C,UAAU,8BAA6BC,SAAC,SAGrDwH,IACCxK,EAAAA,EAAAA,KAAA,SAAO+C,UAAU,6BAA4BC,SAC3CwH,EAAYhB,eAKlB,CAqBO,SAASgJ,EAAmBC,GAElC,OADAA,EAASC,WAAWD,GAAU,GAAGE,QAAQ,IAC5B,GAEX3O,EAAAA,EAAAA,MAAA,QAAMjB,UAAU,cAAaC,SAAA,CAC3B,IACAyP,EAAO,KAACzS,EAAAA,EAAAA,KAAA,KAAG+C,UAAU,0BAGH,IAAX0P,GAA2B,OAAXA,GAC1BA,EAAS,GACFzO,EAAAA,EAAAA,MAAA,QAAAhB,SAAA,CAAM,IAAEyP,OAGfzO,EAAAA,EAAAA,MAAA,QAAMjB,UAAU,eAAcC,SAAA,CAC5B,IACAyP,EAAO,KAACzS,EAAAA,EAAAA,KAAA,KAAG+C,UAAU,uBAGzB,CAiBO,SAAS6P,EAAevR,EAAQwR,GACtC,MAAyB,MAArBxR,EAAOiR,OAAO,IAAmC,MAArBjR,EAAOiR,OAAO,IAE5CtS,EAAAA,EAAAA,KAAA,SACC+C,UAAU,8BACV,cAAY,UACZ,iBAAe,MACf+P,MAAOD,EAAO7P,SACd,cAI6B,MAArB3B,EAAOiR,OAAO,IAAmC,MAArBjR,EAAOiR,OAAO,IAEnDtS,EAAAA,EAAAA,KAAA,SACC+C,UAAU,6BACV,cAAY,UACZ,iBAAe,MACf+P,MAAOD,EAAO7P,SACd,cAiBDhD,EAAAA,EAAAA,KAAA,SACC+C,UAAU,2BACV,cAAY,UACZ,iBAAe,MACf+P,MAAOD,EAAO7P,SAEb3B,GAIL,CAEO,SAASsQ,EAAmBoB,GAClC,OAAIC,MAAMD,GACF,MAEJA,EAAS,IACLA,EAAOE,WAEXF,GAAU,KAAQA,EAAS,KACtBA,EAAS,KAAMJ,QAAQ,GAAK,KAEjCI,GAAU,KAAUA,EAAS,KACxBA,EAAS,KAAQJ,QAAQ,GAAK,KAEnCI,GAAU,KACLA,EAAS,KAAUJ,QAAQ,GAAK,WADzC,CAGD,C", "sources": ["app/components/higher-order/withauth.jsx", "app/components/modal/AddStrategyPricingModel.jsx", "app/components/modal/CreateStrategyModal.jsx", "app/components/modal/PlaceOrderModel.jsx", "app/user-pages/strategy/Strategy.jsx", "app/user-pages/strategy/StrategyCards.jsx", "app/user-pages/ui-helper.jsx"], "sourcesContent": ["import React, { useEffect } from \"react\";\r\nimport { useHistory } from \"react-router-dom\";\r\nimport { API_URL } from \"../../../Util/constant\";\r\nconst withAuth = (WrappedComponent) => {\r\n\tconst AuthWrapper = (props) => {\r\n\t\t// localStorage.setItem(\r\n\t\t// \t\"admin_access_token\",\r\n\t\t// \t\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************.2pJTNAozJZkRDCJcIqE4OaKIEfq2may8sSTswl637LE\"\r\n\t\t// );\r\n\t\tconst history = useHistory();\r\n\t\tuseEffect(() => {\r\n\t\t\tconst token = localStorage.getItem(\"admin_access_token\");\r\n\t\t\tconst { hostname } = window.location;\r\n\t\t\tconst login_url = \"https://\" + hostname + \"/admin/\";\r\n\t\t\tif (!token) {\r\n\t\t\t\twindow.location.href = login_url;\r\n\t\t\t} else {\r\n\t\t\t\tconst requestOptions = {\r\n\t\t\t\t\tmethod: \"POST\",\r\n\t\t\t\t\theaders: { \"Content-Type\": \"application/json\" },\r\n\t\t\t\t\tbody: JSON.stringify({ token }),\r\n\t\t\t\t};\r\n\t\t\t\tfetch(API_URL + \"/auth/verify\", requestOptions)\r\n\t\t\t\t\t.then((response) => response.json())\r\n\t\t\t\t\t.then((data) => {\r\n\t\t\t\t\t\tif (data.code === \"token_not_valid\") {\r\n\t\t\t\t\t\t\twindow.location.href = login_url;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((error) => {\r\n\t\t\t\t\t\tconsole.error(\"Error:\", error);\r\n\t\t\t\t\t\twindow.location.href = login_url;\r\n\t\t\t\t\t});\r\n\t\t\t}\r\n\t\t}, [history]);\r\n\r\n\t\treturn <WrappedComponent {...props} />;\r\n\t};\r\n\r\n\treturn AuthWrapper;\r\n};\r\n\r\nexport default withAuth;\r\n", "import React, { useEffect, useMemo, useState } from \"react\";\r\nimport { Button, Form, Modal } from \"react-bootstrap\";\r\nimport { useMutation } from \"react-query\";\r\nimport {\r\n\tAddStrategyPricing,\r\n\tGetStrategyPricing,\r\n\tRemoveStrategyPricing,\r\n} from \"../../../services/backendServices\";\r\nimport ResponseModal from \"./ResponseModal\";\r\nimport cogoToast from \"cogo-toast\";\r\nimport DataTable from \"react-data-table-component\";\r\n\r\nfunction AddStrategyPricingModal(props) {\r\n\tconst [apiResponseModal, setApiResponseModal] = useState({\r\n\t\tshow: false,\r\n\t\tres: {},\r\n\t}); // Modal : on API Response\r\n\tconst initialFormValues = {\r\n\t\tduration: \"\",\r\n\t\tprice: \"\",\r\n\t\tis_per_lot: false,\r\n\t};\r\n\tconst [formValues, setFormValues] = useState(initialFormValues);\r\n\r\n\t// Function Call on change of every field\r\n\tconst handleChange = (event) => {\r\n\t\tconst { name, value, checked } = event.target;\r\n\t\tsetFormValues({\r\n\t\t\t...formValues,\r\n\t\t\t[name]: event.target.type === \"checkbox\" ? checked : value,\r\n\t\t});\r\n\t};\r\n\r\n\tconst [showDeletePopup, setShowDeletePopup] = useState({\r\n\t\tstatus: false,\r\n\t\tobj: null,\r\n\t\tind: null,\r\n\t});\r\n\r\n\t// Function Call on change of Select Broker Field\r\n\r\n\t// API CALL to Add Broker\r\n\tconst mutation = useMutation(\"AddStrategyPricing\", (values) =>\r\n\t\tAddStrategyPricing(values)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tcogoToast.success(`${res.msg}`);\r\n\t\t\t\t\tfetchStrategyData();\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t})\r\n\t);\r\n\r\n\t// Function to Handle Form Submit\r\n\tconst handleSubmit = (event) => {\r\n\t\tconst durationVal = Number(formValues.duration);\r\n\t\tconst priceVal = Number(formValues.price);\r\n\t\tconst customForm = {\r\n\t\t\tstrategy_id: props.data.id,\r\n\t\t\torg_id: props.data.org_id,\r\n\t\t\tname: formValues.name,\r\n\t\t\tduration: durationVal,\r\n\t\t\tprice: priceVal,\r\n\t\t\tis_per_lot: formValues.is_per_lot,\r\n\t\t};\r\n\t\tmutation.mutate(customForm);\r\n\t\tevent.preventDefault();\r\n\t};\r\n\r\n\tconst [tableDataFormatted, setTableDataFormatted] = useState([]);\r\n\tconst rawUserMap = useMemo(() => new Map(), []);\r\n\tconst fetchStrategyData = () => {\r\n\t\tGetStrategyPricing(props.data.id)\r\n\t\t\t.then((res) => {\r\n\t\t\t\t// console.log(\"res\", props.data.id);\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tsetTableData(res.data); // function call\r\n\t\t\t\t\tsetRawResData(res.data);\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t});\r\n\t};\r\n\t// API CALL to Add Broker\r\n\tuseEffect(() => {\r\n\t\tfetchStrategyData();\r\n\t}, [props.data.id]);\r\n\r\n\t// API CALL to Remove Startegy\r\n\tconst RemoveUserBrokerMutation = useMutation(\"RemoveBroker\", () =>\r\n\t\tRemoveStrategyPricing(showDeletePopup.obj.id)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\trawUserMap.delete(showDeletePopup.obj.id);\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\tremoveRowByIndex(showDeletePopup.ind);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t}\r\n\t\t\t\tsetShowDeletePopup({ status: false, obj: null, ind: null });\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t\treturn Promise.reject(e); // wrap in Promise\r\n\t\t\t})\r\n\t);\r\n\r\n\tfunction setRawResData(jsonArr) {\r\n\t\tjsonArr.map((obj, index) => {\r\n\t\t\trawUserMap.set(obj.id, obj);\r\n\t\t});\r\n\t}\r\n\r\n\tfunction removeRowByIndex(index) {\r\n\t\tsetTableDataFormatted((prevData) => {\r\n\t\t\tconst updatedData = [...prevData];\r\n\t\t\tupdatedData.splice(index, 1);\r\n\t\t\treturn updatedData;\r\n\t\t});\r\n\t}\r\n\r\n\tfunction getRowFormatForBrokerTable(ind, obj) {\r\n\t\tvar pass_val = { ind: ind, obj: obj };\r\n\t\treturn {\r\n\t\t\tindex: ind + 1,\r\n\t\t\tname: obj.name,\r\n\t\t\tduration: obj.duration,\r\n\t\t\tprice: obj.price,\r\n\t\t\tis_per_lot: !obj.is_per_lot ? (\r\n\t\t\t\t<label className=\"badge badge-outline-success\"> ALL </label>\r\n\t\t\t) : (\r\n\t\t\t\t<label className=\"badge badge-outline-warning\"> LOT </label>\r\n\t\t\t),\r\n\t\t\taction: (\r\n\t\t\t\t<Button\r\n\t\t\t\t\tvariant=\"danger\"\r\n\t\t\t\t\tclassName=\"p-2\"\r\n\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\tsetShowDeletePopup({ status: true, obj: obj, ind: ind })\r\n\t\t\t\t\t}\r\n\t\t\t\t>\r\n\t\t\t\t\t<i className=\"mdi mdi-delete-forever m-0\"></i>\r\n\t\t\t\t</Button>\r\n\t\t\t),\r\n\t\t\tobj: obj,\r\n\t\t};\r\n\t}\r\n\r\n\tfunction setTableData(tableData) {\r\n\t\tvar rowTableData = [];\r\n\t\ttableData.map((obj, index) => {\r\n\t\t\trowTableData.push(getRowFormatForBrokerTable(index, obj));\r\n\t\t});\r\n\t\tsetTableDataFormatted(rowTableData);\r\n\t}\r\n\r\n\tconst columns = [\r\n\t\t{\r\n\t\t\tname: \"Id\",\r\n\t\t\tselector: (row) => row.index,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"60px\",\r\n\t\t\tminWidth: \"60px\",\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Name\",\r\n\t\t\tselector: (row) => row.name,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"200px\",\r\n\t\t\tminWidth: \"200px\",\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Duration\",\r\n\t\t\tselector: (row) => row.duration,\r\n\t\t\tsortable: true,\r\n\t\t},\r\n\t\t{ name: \"Price\", selector: (row) => row.price, sortable: true },\r\n\t\t{ name: \"Per Lot\", selector: (row) => row.is_per_lot, sortable: true },\r\n\t\t{\r\n\t\t\tname: \"Action\",\r\n\t\t\tselector: (row) => row.action,\r\n\t\t\tsortable: false,\r\n\t\t},\r\n\t\t{ name: \"obj\", selector: (row) => row.obj, omit: true },\r\n\t];\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<Modal\r\n\t\t\t\tshow={true}\r\n\t\t\t\tanimation={true}\r\n\t\t\t\tsize=\"lg\"\r\n\t\t\t\tclassName=\"mt-5 custom-modal \"\r\n\t\t\t>\r\n\t\t\t\t<Form onSubmit={handleSubmit}>\r\n\t\t\t\t\t<Modal.Header className=\"text-center py-3\">\r\n\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t<h5 className=\"mb-0\">Add Pricing</h5>\r\n\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t<Modal.Body className=\"p-10\">\r\n\t\t\t\t\t\t<div className=\"d-flex  align-items-center row\">\r\n\t\t\t\t\t\t\t<div className=\"col-sm-2 col-12\">\r\n\t\t\t\t\t\t\t\t<Form.Group className=\"mx-1\">\r\n\t\t\t\t\t\t\t\t\t<Form.Label> Duration : </Form.Label>\r\n\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"w-100\"\r\n\t\t\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\t\t\tname=\"duration\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={formValues.duration}\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div className=\"col-sm-4 col-12\">\r\n\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t<Form.Label>Name : </Form.Label>\r\n\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"w-100\"\r\n\t\t\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\t\t\tname=\"name\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={formValues.name}\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div className=\"col-sm-3 col-12\">\r\n\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t<Form.Label>Price : </Form.Label>\r\n\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"w-100\"\r\n\t\t\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\t\t\tname=\"price\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={formValues.price}\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div className=\"col-sm-2 col-12 d-flex justify-content-start\">\r\n\t\t\t\t\t\t\t\t<div className=\"row g-0\">\r\n\t\t\t\t\t\t\t\t\t<Form.Group className=\"d-flex align-items-center ml-3 ml-sm-0 mb-0\">\r\n\t\t\t\t\t\t\t\t\t\t<Form.Check\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"form-check-input mt-0 ml-1 mb-0\"\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"switch\"\r\n\t\t\t\t\t\t\t\t\t\t\tid=\"is_per_lot\"\r\n\t\t\t\t\t\t\t\t\t\t\tname=\"is_per_lot\"\r\n\t\t\t\t\t\t\t\t\t\t\tchecked={formValues.is_per_lot}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\r\n\t\t\t\t\t\t\t\t\t\t<label htmlFor=\"is_per_lot\" className=\"m-0\">\r\n\t\t\t\t\t\t\t\t\t\t\tPer Lot\r\n\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div className=\"repos\">\r\n\t\t\t\t\t\t\t<div className=\"table-responsive\">\r\n\t\t\t\t\t\t\t\t<DataTable\r\n\t\t\t\t\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\t\t\t\t\tdata={tableDataFormatted}\r\n\t\t\t\t\t\t\t\t\tpagination={false}\r\n\t\t\t\t\t\t\t\t\tpaginationPerPage={10}\r\n\t\t\t\t\t\t\t\t\thighlightOnHover\r\n\t\t\t\t\t\t\t\t\tnoHeader\r\n\t\t\t\t\t\t\t\t\ttheme=\"solarized\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Modal.Body>\r\n\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<Button variant=\"outline-danger\" onClick={() => props.onClose()}>\r\n\t\t\t\t\t\t\t\tClose\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\ttype=\"submit\"\r\n\t\t\t\t\t\t\t\tvariant=\"outline-primary\"\r\n\t\t\t\t\t\t\t\tclassName=\"mx-2 px-3\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tADD\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t</Form>\r\n\t\t\t</Modal>\r\n\t\t\t{showDeletePopup.status && (\r\n\t\t\t\t<Modal show={true} animation={true} size=\"md\" className=\"mt-5\">\r\n\t\t\t\t\t<Modal.Header className=\"text-center py-3\">\r\n\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t<h5 className=\"mb-0\">Delete User Broker</h5>\r\n\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t<Modal.Body className=\"p-10\">\r\n\t\t\t\t\t\tDo you want to remove this user\r\n\t\t\t\t\t</Modal.Body>\r\n\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\tonClick={() => setShowDeletePopup({ status: false, id: \"\" })}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tvariant=\"outline-primary\"\r\n\t\t\t\t\t\t\t\tclassName=\"mx-2 px-3\"\r\n\t\t\t\t\t\t\t\tonClick={() => RemoveUserBrokerMutation.mutate()}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tYes\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t</Modal>\r\n\t\t\t)}\r\n\t\t\t{apiResponseModal.show && (\r\n\t\t\t\t<ResponseModal\r\n\t\t\t\t\tres={apiResponseModal.res}\r\n\t\t\t\t\tsetApiResponseModal={setApiResponseModal}\r\n\t\t\t\t\tmsg={apiResponseModal.res}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t</>\r\n\t);\r\n}\r\n\r\nexport default AddStrategyPricingModal;\r\n", "import React, { useState } from \"react\";\r\nimport { Button, Form, Modal } from \"react-bootstrap\";\r\nimport { useMutation } from \"react-query\";\r\nimport { AddStrategy } from \"../../../services/backendServices\";\r\nimport ResponseModal from \"./ResponseModal\";\r\nimport cogoToast from \"cogo-toast\";\r\n\r\nfunction CreateStrategyModal(props) {\r\n\tconst [apiResponseModal, setApiResponseModal] = useState({\r\n\t\tshow: false,\r\n\t\tres: {},\r\n\t}); // Modal : on API Response\r\n\tconst initialFormValues = {\r\n\t\tname: \"\",\r\n\t\tdescription: \"\",\r\n\t\tduration: \"\",\r\n\t\trequired_amount: \"\",\r\n\t\tprice: \"\",\r\n\t\tis_copy_trade: false,\r\n\t\tis_private: false,\r\n\t\torder_key: \"\",\r\n\t\tmaster_broker: \"\",\r\n\t};\r\n\tconst [formValues, setFormValues] = useState(initialFormValues);\r\n\r\n\t// Function Call on change of every field\r\n\tconst handleChange = (event) => {\r\n\t\tconst { name, value, checked } = event.target;\r\n\t\tsetFormValues({\r\n\t\t\t...formValues,\r\n\t\t\t[name]: event.target.type === \"checkbox\" ? checked : value,\r\n\t\t});\r\n\t};\r\n\r\n\t// Function Call on change of Select Broker Field\r\n\r\n\t// API CALL to Add Broker\r\n\tconst mutation = useMutation(\"AddStrategy\", (values) =>\r\n\t\tAddStrategy(\r\n\t\t\tvalues.name.trim(),\r\n\t\t\tvalues.description.trim(),\r\n\t\t\tvalues.order_key,\r\n\t\t\tvalues.is_private,\r\n\t\t\tvalues.required_amount\r\n\t\t)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tprops.onClose();\r\n\t\t\t\t\tprops.fetchStrategies();\r\n\t\t\t\t\tcogoToast.success(`${res.msg}`);\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t})\r\n\t);\r\n\t// Function to Handle Form Submit\r\n\tconst handleSubmit = (event) => {\r\n\t\tmutation.mutate(formValues);\r\n\t\tevent.preventDefault();\r\n\t};\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<Modal show={true} animation={true} size=\"md\" className=\"mt-5\">\r\n\t\t\t\t<Form onSubmit={handleSubmit}>\r\n\t\t\t\t\t<Modal.Header className=\"text-center py-3\">\r\n\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t<h5 className=\"mb-0\">Create Strategy</h5>\r\n\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t<Modal.Body className=\"p-10\">\r\n\t\t\t\t\t\t<div className=\"d-flex  align-items-center row\">\r\n\t\t\t\t\t\t\t<div className=\"col-sm-6 col-12\">\r\n\t\t\t\t\t\t\t\t<Form.Group className=\"mx-1\">\r\n\t\t\t\t\t\t\t\t\t<Form.Label>Strategy Name : </Form.Label>\r\n\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"w-100\"\r\n\t\t\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\t\t\tname=\"name\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={formValues.name}\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div className=\"col-sm-6 col-12\">\r\n\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t<Form.Label>Order Key : </Form.Label>\r\n\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"w-100\"\r\n\t\t\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\t\t\tname=\"order_key\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={formValues.order_key}\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t<Form.Label>Strategy Description : </Form.Label>\r\n\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\tname=\"description\"\r\n\t\t\t\t\t\t\t\tvalue={formValues.description}\r\n\t\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Form.Group>\r\n\r\n\t\t\t\t\t\t<div className=\"d-flex justify-content-between align-items-center row\">\r\n\t\t\t\t\t\t\t<div className=\"col-sm-6 col-12\">\r\n\t\t\t\t\t\t\t\t<Form.Group className=\"mx-1\">\r\n\t\t\t\t\t\t\t\t\t<Form.Label>Required Amount : </Form.Label>\r\n\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\t\t\tname=\"required_amount\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={formValues.required_amount}\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div className=\"col-sm-6 col-12 d-flex justify-content-start\">\r\n\t\t\t\t\t\t\t\t<div className=\"row g-0\">\r\n\t\t\t\t\t\t\t\t\t<Form.Group className=\"d-flex align-items-center ml-3 ml-sm-0 mb-0\">\r\n\t\t\t\t\t\t\t\t\t\t<Form.Check\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"form-check-input mt-0 ml-1 mb-0\"\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"switch\"\r\n\t\t\t\t\t\t\t\t\t\t\tid=\"is_private\"\r\n\t\t\t\t\t\t\t\t\t\t\tname=\"is_private\"\r\n\t\t\t\t\t\t\t\t\t\t\tchecked={formValues.is_private}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\r\n\t\t\t\t\t\t\t\t\t\t<label htmlFor=\"is_private\" className=\"m-0\">\r\n\t\t\t\t\t\t\t\t\t\t\tPublic / Private\r\n\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Modal.Body>\r\n\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<Button variant=\"outline-danger\" onClick={() => props.onClose()}>\r\n\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\ttype=\"submit\"\r\n\t\t\t\t\t\t\t\tvariant=\"outline-primary\"\r\n\t\t\t\t\t\t\t\tclassName=\"mx-2 px-3\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tYes\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t</Form>\r\n\t\t\t</Modal>\r\n\t\t\t{apiResponseModal.show && (\r\n\t\t\t\t<ResponseModal\r\n\t\t\t\t\tres={apiResponseModal.res}\r\n\t\t\t\t\tsetApiResponseModal={setApiResponseModal}\r\n\t\t\t\t\tmsg={apiResponseModal.res}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t</>\r\n\t);\r\n}\r\n\r\nexport default CreateStrategyModal;\r\n", "import React, { useState } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON>dal, Form } from \"react-bootstrap\";\r\nimport { useMutation } from \"react-query\";\r\nimport ResponseModal from \"./ResponseModal\";\r\nimport Select from \"react-select\";\r\nimport cogoToast from \"cogo-toast\";\r\nimport { PlaceOrderApi } from \"../../../services/backendServices\";\r\nimport {\r\n\tcustomStylesForSelect,\r\n\thoverEffectOnSelect,\r\n} from \"../../user-pages/ui-helper\";\r\nimport { mergeStyles } from \"react-select/dist/react-select.cjs.prod\";\r\nimport DatePicker from \"react-datepicker\";\r\nimport \"react-datepicker/dist/react-datepicker.css\";\r\nimport { format } from \"date-fns\";\r\n\r\nexport default function PlaceOrderModal(props) {\r\n\tconst [showModal, setShowModal] = useState(true);\r\n\t// console.log(\"id \", props);\r\n\r\n\tfunction onBtnClose() {\r\n\t\tprops.setPlaceOrderModal({ show: false });\r\n\t\tsetShowModal(false);\r\n\t}\r\n\tconst [apiResponseModal, setApiResponseModal] = useState({\r\n\t\tshow: false,\r\n\t\tres: {},\r\n\t}); // Modal : on API Response\r\n\r\n\tconst [qty, setQty] = useState(1);\r\n\tconst [selectedOption, setSelectedOption] = useState({\r\n\t\tvalue: \"BUY\",\r\n\t\tlabel: \"BUY\",\r\n\t});\r\n\tconst [selectedOptionSide, setSelectedOptionSide] = useState({\r\n\t\tvalue: \"PE\",\r\n\t\tlabel: \"PE\",\r\n\t});\r\n\r\n\tconst [selectedTypeOption, setSelectedTypeOption] = useState({\r\n\t\tvalue: \"EQUITY\",\r\n\t\tlabel: \"EQUITY\",\r\n\t});\r\n\r\n\tconst [selectedProductOption, setSelectedProductOption] = useState({\r\n\t\tvalue: \"CNC\",\r\n\t\tlabel: \"CNC\",\r\n\t});\r\n\r\n\tconst optionType = [\r\n\t\t{\r\n\t\t\tvalue: \"EQUITY\",\r\n\t\t\tlabel: \"EQUITY\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tvalue: \"FUTURE\",\r\n\t\t\tlabel: \"FUTURE\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tvalue: \"OPTION\",\r\n\t\t\tlabel: \"OPTION\",\r\n\t\t},\r\n\t];\r\n\tconst productOptions = [\r\n\t\t{\r\n\t\t\tvalue: \"CNC\",\r\n\t\t\tlabel: \"CNC\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tvalue: \"NRML\",\r\n\t\t\tlabel: \"NRML\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tvalue: \"MIS\",\r\n\t\t\tlabel: \"MIS\",\r\n\t\t},\r\n\t];\r\n\r\n\tconst options = [\r\n\t\t{\r\n\t\t\tvalue: \"BUY\",\r\n\t\t\tlabel: \"BUY\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tvalue: \"SELL\",\r\n\t\t\tlabel: \"SELL\",\r\n\t\t},\r\n\t];\r\n\tconst optionSide = [\r\n\t\t{\r\n\t\t\tvalue: \"PE\",\r\n\t\t\tlabel: \"PE\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tvalue: \"CE\",\r\n\t\t\tlabel: \"CE\",\r\n\t\t},\r\n\t];\r\n\r\n\tconst [script, setScript] = useState();\r\n\tconst [selectedDate, setSelectedDate] = useState(new Date());\r\n\tconst [strikPrice, setStrikPrice] = useState();\r\n\tconst [confirmModel, setConfirmModel] = useState();\r\n\r\n\tconst placeOrderApiCall = useMutation(\"PlaceOrderApi\", (params) => {\r\n\t\treturn PlaceOrderApi(params)\r\n\t\t\t.then((res) => {\r\n\t\t\t\t// console.log(\"res\", res);\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tonBtnClose();\r\n\t\t\t\t\tcogoToast.success(`${res.msg} OK`);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// console.log(\"res.msg\", res.msg);\r\n\t\t\t\t\tsetConfirmModel(false);\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t});\r\n\t});\r\n\r\n\tconst handlePlaceOrder = (e) => {\r\n\t\te.preventDefault();\r\n\r\n\t\tif (\r\n\t\t\tselectedTypeOption.value === \"EQUITY\" &&\r\n\t\t\tqty &&\r\n\t\t\tselectedOption?.value &&\r\n\t\t\tscript\r\n\t\t) {\r\n\t\t\tsetConfirmModel(true);\r\n\t\t} else if (\r\n\t\t\tselectedTypeOption.value === \"FUTURE\" &&\r\n\t\t\tqty &&\r\n\t\t\tselectedOption?.value &&\r\n\t\t\tscript\r\n\t\t) {\r\n\t\t\tsetConfirmModel(true);\r\n\t\t} else if (\r\n\t\t\tselectedTypeOption.value === \"OPTION\" &&\r\n\t\t\tqty &&\r\n\t\t\tselectedOption?.value &&\r\n\t\t\tscript &&\r\n\t\t\tstrikPrice &&\r\n\t\t\tselectedOptionSide?.value\r\n\t\t) {\r\n\t\t\tsetConfirmModel(true);\r\n\t\t} else {\r\n\t\t\tcogoToast.error(\"Please fill all fields\");\r\n\t\t}\r\n\t};\r\n\r\n\tconst confirmOrder = (e) => {\r\n\t\t// console.log(\"order place with details\", props.id);\r\n\t\tlet values;\r\n\t\tif (selectedTypeOption.value === \"EQUITY\") {\r\n\t\t\tvalues = {\r\n\t\t\t\torder_type: \"eq\", // eq, future, option\r\n\t\t\t\tscript_name: script,\r\n\t\t\t\tstrategy_id: Number(props.id),\r\n\t\t\t\tquantity: Number(qty),\r\n\t\t\t\ttransaction: selectedOption.value,\r\n\t\t\t\tproduct: selectedProductOption.value, // \"NRML\", \"CNC\", \"MIS\"\r\n\t\t\t};\r\n\t\t} else if (selectedTypeOption.value === \"OPTION\") {\r\n\t\t\tvalues = {\r\n\t\t\t\torder_type: \"option\", // eq, future, option\r\n\t\t\t\tscript_name: script,\r\n\t\t\t\texpiry_date: format(selectedDate, \"MM-dd-yyyy\"),\r\n\t\t\t\tstrike_price: Number(strikPrice), // for option\r\n\t\t\t\toption_type: selectedOptionSide.value, // CE PE : for option\r\n\t\t\t\tstrategy_id: Number(props.id),\r\n\t\t\t\tquantity: Number(qty),\r\n\t\t\t\ttransaction: selectedOption.value,\r\n\t\t\t\tproduct: selectedProductOption.value, // \"NRML\", \"CNC\", \"MIS\"\r\n\t\t\t};\r\n\t\t} else {\r\n\t\t\tvalues = {\r\n\t\t\t\torder_type: \"future\", // eq, future, option\r\n\t\t\t\tscript_name: script,\r\n\t\t\t\texpiry_date: format(selectedDate, \"MM-dd-yyyy\"),\r\n\t\t\t\tstrategy_id: Number(props.id),\r\n\t\t\t\tquantity: Number(qty),\r\n\t\t\t\ttransaction: selectedOption.value,\r\n\t\t\t\tproduct: selectedProductOption.value, // \"NRML\", \"CNC\", \"MIS\"\r\n\t\t\t};\r\n\t\t}\r\n\t\tvalues && placeOrderApiCall.mutate(values);\r\n\t};\r\n\r\n\tconst handleClearButton = () => {\r\n\t\tsetSelectedOption({\r\n\t\t\tvalue: \"BUY\",\r\n\t\t\tlabel: \"BUY\",\r\n\t\t});\r\n\t\tsetSelectedOptionSide({\r\n\t\t\tvalue: \"PE\",\r\n\t\t\tlabel: \"PE\",\r\n\t\t});\r\n\t\tsetSelectedTypeOption({\r\n\t\t\tvalue: \"EQUITY\",\r\n\t\t\tlabel: \"EQUITY\",\r\n\t\t});\r\n\t\tsetSelectedProductOption({\r\n\t\t\tvalue: \"CNC\",\r\n\t\t\tlabel: \"CNC\",\r\n\t\t});\r\n\t\tsetQty(\"\");\r\n\t\tsetStrikPrice(null);\r\n\t\tsetSelectedDate(new Date());\r\n\t};\r\n\tconst handleDateChange = (date) => {\r\n\t\tsetSelectedDate(date);\r\n\t\t// const formattedDate = format(date, \"MM-dd-yyyy\");\r\n\t\t// console.log(\"Selected Date:\", formattedDate);\r\n\t};\r\n\r\n\treturn (\r\n\t\t<Modal\r\n\t\t\tshow={showModal}\r\n\t\t\tanimation={true}\r\n\t\t\tsize=\"lg\"\r\n\t\t\tclassName=\"mt-5 custom-modal \"\r\n\t\t>\r\n\t\t\t<Modal.Header className=\"text-center py-2\">\r\n\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t<h3 className=\"mb-0\">Place order</h3>\r\n\t\t\t\t</Modal.Title>\r\n\t\t\t</Modal.Header>\r\n\t\t\t<Modal.Body className=\"p-10 \">\r\n\t\t\t\t<div>\r\n\t\t\t\t\t<div className=\"row\">\r\n\t\t\t\t\t\t<div className=\"col-lg-12 grid-margin stretch-card mb-0\">\r\n\t\t\t\t\t\t\t<div className=\"card-body pb-1\">\r\n\t\t\t\t\t\t\t\t<Form>\r\n\t\t\t\t\t\t\t\t\t<div className=\"row scroll\">\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className=\"text-muted\">Type</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\t\t\t\toptions={optionType}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={selectedTypeOption}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(selectedOption) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetSelectedTypeOption(selectedOption)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyles={mergeStyles(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\thoverEffectOnSelect,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcustomStylesForSelect\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-4\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className=\"text-muted\">Script</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Script\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={script}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetScript(e.target.value?.toUpperCase())\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t{(selectedTypeOption.value === \"FUTURE\" ||\r\n\t\t\t\t\t\t\t\t\t\t\tselectedTypeOption.value === \"OPTION\") && (\r\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className=\"text-muted\">Date</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<DatePicker\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tselected={selectedDate}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleDateChange}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"form-control\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdateFormat=\"dd-MM-yyyy\" // Customize date format if needed\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-2\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className=\"text-muted\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedTypeOption.value === \"EQUITY\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"Qty\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"Lot\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Qty\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={qty}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => setQty(e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t{selectedTypeOption.value === \"OPTION\" && (\r\n\t\t\t\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className=\"text-muted\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tStrike Price\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Strike Price\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={strikPrice}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => setStrikPrice(e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className=\"text-muted\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tOption Side\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\toptions={optionSide}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={selectedOptionSide}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(selectedOption) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetSelectedOptionSide(selectedOption)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyles={mergeStyles(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thoverEffectOnSelect,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcustomStylesForSelect\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className=\"text-muted\">Action</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\t\t\t\toptions={options}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={selectedOption}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(selectedOption) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetSelectedOption(selectedOption)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Search for an item...\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyles={mergeStyles(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\thoverEffectOnSelect,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcustomStylesForSelect\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className=\"text-muted\">Product</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\t\t\t\toptions={productOptions}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={selectedProductOption}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(selectedOption) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetSelectedProductOption(selectedOption)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyles={mergeStyles(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\thoverEffectOnSelect,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcustomStylesForSelect\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"row scroll\">\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-6 mt-2\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"button\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"btn btn-md btn-danger mr-3\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={handleClearButton}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\tClear\r\n\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"btn btn-md mr-3\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={handlePlaceOrder}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\tPlaceOrder\r\n\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Form>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t{confirmModel && (\r\n\t\t\t\t\t\t<Modal\r\n\t\t\t\t\t\t\tshow={confirmModel}\r\n\t\t\t\t\t\t\tanimation={true}\r\n\t\t\t\t\t\t\tsize=\"md\"\r\n\t\t\t\t\t\t\tclassName=\"mt-5\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Modal.Header className=\"d-flex justify-content-center py-2\">\r\n\t\t\t\t\t\t\t\t<Modal.Title>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t<h3 className=\"mb-0\">Confirm</h3>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t\t\t<Modal.Body className=\"p-10\" style={{ backgroundColor: \"white\" }}>\r\n\t\t\t\t\t\t\t\t<table\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\twidth: \"60%\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"0 auto\",\r\n\t\t\t\t\t\t\t\t\t\tborderCollapse: \"collapse\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<tbody>\r\n\t\t\t\t\t\t\t\t\t\t<tr>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>Order Type</td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>: </td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<b>{selectedTypeOption.value}</b>\r\n\t\t\t\t\t\t\t\t\t\t\t</td>\r\n\t\t\t\t\t\t\t\t\t\t</tr>\r\n\t\t\t\t\t\t\t\t\t\t<tr>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>Script</td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>: </td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<b>{script}</b>\r\n\t\t\t\t\t\t\t\t\t\t\t</td>\r\n\t\t\t\t\t\t\t\t\t\t</tr>\r\n\t\t\t\t\t\t\t\t\t\t{selectedTypeOption.value === \"FUTURE\" && (\r\n\t\t\t\t\t\t\t\t\t\t\t<tr>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selectedDate && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>Date</td>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>: </td>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{format(selectedDate, \"dd-MM-yyyy\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</td>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t</tr>\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t<tr>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selectedTypeOption.value === \"EQUITY\" ? \"Qty\" : \"Lot\"}\r\n\t\t\t\t\t\t\t\t\t\t\t</td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>: </td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<b>{qty}</b>\r\n\t\t\t\t\t\t\t\t\t\t\t</td>\r\n\t\t\t\t\t\t\t\t\t\t</tr>\r\n\t\t\t\t\t\t\t\t\t\t{selectedTypeOption.value === \"OPTION\" && (\r\n\t\t\t\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<tr>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>Strike Price</td>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>: </td>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b>{strikPrice}</b>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</td>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</tr>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<tr>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>Option Side</td>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>: </td>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b>{selectedOptionSide.value}</b>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</td>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</tr>\r\n\t\t\t\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t<tr>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>Action</td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>: </td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<b>{selectedOption.value}</b>\r\n\t\t\t\t\t\t\t\t\t\t\t</td>\r\n\t\t\t\t\t\t\t\t\t\t</tr>\r\n\t\t\t\t\t\t\t\t\t\t<tr>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>Product</td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>: </td>\r\n\t\t\t\t\t\t\t\t\t\t\t<td style={{ color: \"black\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<b>{selectedProductOption.value}</b>\r\n\t\t\t\t\t\t\t\t\t\t\t</td>\r\n\t\t\t\t\t\t\t\t\t\t</tr>\r\n\t\t\t\t\t\t\t\t\t</tbody>\r\n\t\t\t\t\t\t\t\t</table>\r\n\t\t\t\t\t\t\t</Modal.Body>\r\n\r\n\t\t\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<Button variant=\"outline-primary\" onClick={confirmOrder}>\r\n\t\t\t\t\t\t\t\t\t\tConfirm\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => setConfirmModel(false)}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\tCancel\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t\t\t</Modal>\r\n\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t{apiResponseModal.show && (\r\n\t\t\t\t\t\t<ResponseModal\r\n\t\t\t\t\t\t\tres={apiResponseModal.res}\r\n\t\t\t\t\t\t\tsetApiResponseModal={setApiResponseModal}\r\n\t\t\t\t\t\t\tmsg={apiResponseModal.res.msg}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</div>\r\n\t\t\t</Modal.Body>\r\n\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t<div>\r\n\t\t\t\t\t<Button variant=\"outline-danger\" onClick={onBtnClose}>\r\n\t\t\t\t\t\tCancel\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</div>\r\n\t\t\t</Modal.Footer>\r\n\t\t</Modal>\r\n\t);\r\n}\r\n", "import React, { Component } from \"react\";\r\nimport \"../userpages.css\";\r\nimport { Spinner } from \"react-bootstrap\";\r\nimport StrategyCards from \"./StrategyCards\";\r\nimport { GetStrategies } from \"../../../services/backendServices\";\r\nimport withAuth from \"../../components/higher-order/withauth\";\r\nimport CreateStrategyModal from \"../../components/modal/CreateStrategyModal\";\r\n\r\nexport class Strategy extends Component {\r\n\tconstructor(props) {\r\n\t\tsuper(props);\r\n\t\tthis.state = {\r\n\t\t\tshowAddStrategyModal: false,\r\n\t\t\tisDataLoaded: false,\r\n\t\t\tstrategyData: null,\r\n\t\t\tisLoading: false,\r\n\t\t\terrorMsg: \"\",\r\n\t\t};\r\n\t\tthis.fetchStrategies = this.fetchStrategies.bind(this);\r\n\t}\r\n\r\n\tfetchStrategies = () => {\r\n\t\tthis.setState({ isLoading: true }); // set loading state when API call is made\r\n\t\tGetStrategies()\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tthis.setState({ strategyData: res, isDataLoaded: true });\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.setState({\r\n\t\t\t\t\t\tstrategyData: null,\r\n\t\t\t\t\t\tisDataLoaded: true,\r\n\t\t\t\t\t\terrorMsg: res.msg,\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t})\r\n\t\t\t.finally(() => {\r\n\t\t\t\tthis.setState({ isLoading: false }); // clear loading state when API response is received\r\n\t\t\t});\r\n\t};\r\n\r\n\t// Fetch Data on Mounting\r\n\tcomponentDidMount() {\r\n\t\tthis.fetchStrategies();\r\n\t}\r\n\r\n\thandleOpenCreateStrategyModal = () => {\r\n\t\tthis.setState({ showAddStrategyModal: true });\r\n\t};\r\n\thandleCreateStrategyModal = () => {\r\n\t\tthis.setState({ showAddStrategyModal: false });\r\n\t};\r\n\r\n\trender() {\r\n\t\tconst { strategyData, isDataLoaded, errorMsg } = this.state;\r\n\r\n\t\t// If data is not loaded Return this\r\n\t\tif (!isDataLoaded) {\r\n\t\t\treturn (\r\n\t\t\t\t<div\r\n\t\t\t\t\tclassName=\"d-flex justify-content-center align-items-center\"\r\n\t\t\t\t\tstyle={{ height: \"100%\" }}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Spinner\r\n\t\t\t\t\t\tanimation=\"border\"\r\n\t\t\t\t\t\tstyle={{ width: \"5rem\", height: \"5rem\" }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t</div>\r\n\t\t\t);\r\n\t\t}\r\n\t\treturn (\r\n\t\t\t<div>\r\n\t\t\t\t<div className=\"strategy-cards\">\r\n\t\t\t\t\t<div className=\"d-flex align-items-center justify-content-start mb-4\">\r\n\t\t\t\t\t\t<h3 className=\"page-title\">Strategy</h3>\r\n\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\tclassName=\"btn btn-primary btn-rounded ml-3\"\r\n\t\t\t\t\t\t\tonClick={this.handleOpenCreateStrategyModal}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{\" \"}\r\n\t\t\t\t\t\t\tCreate Strategy{\" \"}\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=\"row align-items-center \">\r\n\t\t\t\t\t\t{strategyData === null && (\r\n\t\t\t\t\t\t\t<div className=\"ml-2\">{errorMsg && errorMsg}</div>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t{strategyData?.data.map((strategy) => (\r\n\t\t\t\t\t\t\t<StrategyCards\r\n\t\t\t\t\t\t\t\tkey={strategy.id}\r\n\t\t\t\t\t\t\t\tstrategy={strategy}\r\n\t\t\t\t\t\t\t\tparent_props={this.props}\r\n\t\t\t\t\t\t\t\tfetchStrategies={this.fetchStrategies}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t))}\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t{this.state.showAddStrategyModal && (\r\n\t\t\t\t\t<CreateStrategyModal\r\n\t\t\t\t\t\tonClose={this.handleCreateStrategyModal}\r\n\t\t\t\t\t\tfetchStrategies={this.fetchStrategies}\r\n\t\t\t\t\t/>\r\n\t\t\t\t)}\r\n\t\t\t</div>\r\n\t\t);\r\n\t}\r\n}\r\n\r\nexport default withAuth(Strategy);\r\n", "import React, { Component } from \"react\";\r\nimport { Line } from \"react-chartjs-2\";\r\nimport {\r\n\tcustomStylesForSelect,\r\n\tgetConvertedAmount,\r\n\thoverEffectOnSelect,\r\n} from \"../ui-helper\";\r\nimport { Button, Form, Modal, OverlayTrigger, Tooltip } from \"react-bootstrap\";\r\nimport ResponseModal from \"../../components/modal/ResponseModal\";\r\nimport {\r\n\tConnectStrategyMaster,\r\n\tDeleteStrategy,\r\n\tDisconnectmasterFromoStrategy,\r\n\tGetMastersForStrategy,\r\n\tUpdateStrategyInfo,\r\n\tUpdateStrategyRejectedFlag,\r\n\tUpdateStrategyTradingFlag,\r\n} from \"../../../services/backendServices\";\r\nimport cogoToast from \"cogo-toast\";\r\nimport Select from \"react-select\";\r\nimport AddStrategyPricingModal from \"../../components/modal/AddStrategyPricingModel\";\r\nimport { mergeStyles } from \"react-select/dist/react-select.cjs.prod\";\r\nimport PlaceOrderModal from \"../../components/modal/PlaceOrderModel\";\r\n\r\nclass StrategyCards extends Component {\r\n\tconstructor(props) {\r\n\t\tsuper(props);\r\n\t\tthis.parent_props = props.parent_props;\r\n\t\tthis.state = {\r\n\t\t\tactive_status: true,\r\n\t\t\ttrading_status: this.props.strategy.strategy_info.trading_flag,\r\n\t\t\tplace_rejected: this.props.strategy.strategy_info.place_rejected,\r\n\t\t\tapiResponseModal: { show: false, res: {} },\r\n\t\t\tplaceOrderModal: { show: false, res: {} },\r\n\t\t\tdeleteStrategyModal: { status: false, id: 0 },\r\n\t\t\teditStrategyModal: { status: false, id: 0, obj: {} },\r\n\t\t\tdeleteStrategyMasterModal: { status: false, id: 0 },\r\n\t\t\tdeleteStrategyModal: { status: false, id: 0 },\r\n\t\t\tsetStrategyMasterModal: { status: false, id: 0 },\r\n\t\t\tselectedValueForBrokerName: null,\r\n\t\t\tshowAddStrategyPricingModal: { status: false, obj: {} },\r\n\t\t\tname: \"\",\r\n\t\t\tdescription: \"\",\r\n\t\t\trequired_amount: 0,\r\n\t\t\torder_key: \"\",\r\n\t\t\toptions: [],\r\n\t\t};\r\n\t}\r\n\r\n\thandleDataClick = (data) => {\r\n\t\tconst strategy_id = data.id;\r\n\t\twindow.open(`/admin/panel/strategy/${strategy_id}`, \"_blank\");\r\n\t};\r\n\r\n\thandleUsersClick = (data) => {\r\n\t\tconst strategy_id = data.id;\r\n\t\twindow.open(`/admin/panel/strategy/users/${strategy_id}`, \"_blank\");\r\n\t};\r\n\r\n\thandleEditStrategyModal = () => {\r\n\t\tthis.setState({ editStrategyModal: { status: false, id: 0, obj: {} } });\r\n\t};\r\n\r\n\thandleTradingStatusChange = async (event) => {\r\n\t\tconst { name, checked } = event.target;\r\n\t\tthis.setState({\r\n\t\t\t[name]: checked,\r\n\t\t});\r\n\t\t// console.log(\"this.props.strategy.strategy_info.id, checked\", this.props.strategy.strategy_info.id, checked)\r\n\t\ttry {\r\n\t\t\tconst result = await UpdateStrategyTradingFlag(\r\n\t\t\t\tthis.props.strategy.strategy_info.id,\r\n\t\t\t\tchecked\r\n\t\t\t);\r\n\t\t\tthis.setState({ apiResponseModal: { show: true, res: result } });\r\n\t\t} catch (error) {\r\n\t\t\tthis.setState({ apiResponseModal: { show: true, res: error } });\r\n\t\t}\r\n\t};\r\n\thandleStrategyStatusChange = async (event) => {\r\n\t\tconst { name, checked } = event.target;\r\n\t\tthis.setState({\r\n\t\t\t[name]: checked,\r\n\t\t});\r\n\t\t// console.log(\"this.props.strategy.strategy_info.id, checked\", this.props.strategy.strategy_info.id, checked)\r\n\t\ttry {\r\n\t\t\tconst result = await UpdateStrategyRejectedFlag(\r\n\t\t\t\tthis.props.strategy.strategy_info.id,\r\n\t\t\t\tchecked\r\n\t\t\t);\r\n\t\t\tthis.setState({ apiResponseModal: { show: true, res: result } });\r\n\t\t} catch (error) {\r\n\t\t\tthis.setState({ apiResponseModal: { show: true, res: error } });\r\n\t\t}\r\n\t};\r\n\r\n\tgetStrategyMaster = async () => {\r\n\t\ttry {\r\n\t\t\tconst result = await GetMastersForStrategy();\r\n\t\t\tvar options_arr = [];\r\n\t\t\tconst jsonArr = Object.values(result.data);\r\n\t\t\tfor (let i = 0; i < jsonArr.length; i++) {\r\n\t\t\t\tlet obj = jsonArr[i];\r\n\t\t\t\toptions_arr.push({ label: obj.concatenated_string, value: obj.id });\r\n\t\t\t}\r\n\t\t\tthis.setState({ options: options_arr });\r\n\t\t} catch (error) {\r\n\t\t\tthis.setState({ apiResponseModal: { show: true, res: error } });\r\n\t\t}\r\n\t};\r\n\r\n\thandleRemoveStrategyMaster = async (id, strategy) => {\r\n\t\t// console.log(\"handleRemoveStrategyMaster\");\r\n\t\ttry {\r\n\t\t\tconst result = await DisconnectmasterFromoStrategy(id);\r\n\r\n\t\t\tthis.setState({ deleteStrategyMasterModal: { status: false, id: 0 } });\r\n\t\t\tif (result.status) {\r\n\t\t\t\tstrategy.is_master_connected = false;\r\n\t\t\t\tthis.getStrategyMaster();\r\n\t\t\t\tcogoToast.success(`${result.msg}`);\r\n\t\t\t} else {\r\n\t\t\t\tthis.setState({ apiResponseModal: { show: true, res: result } });\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t\tthis.setState({ apiResponseModal: { show: true, res: error } });\r\n\t\t}\r\n\t};\r\n\r\n\thandleRemoveStrategy = async (id) => {\r\n\t\ttry {\r\n\t\t\tconst result = await DeleteStrategy(id);\r\n\r\n\t\t\tthis.setState({ deleteStrategyModal: { status: false, id: 0 } });\r\n\t\t\tif (result.status) {\r\n\t\t\t\tcogoToast.success(`${result.msg}`);\r\n\t\t\t\tthis.props.fetchStrategies();\r\n\t\t\t} else {\r\n\t\t\t\tthis.setState({ apiResponseModal: { show: true, res: result } });\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t\tthis.setState({ apiResponseModal: { show: true, res: error } });\r\n\t\t}\r\n\t};\r\n\r\n\thandleConnectStrategyMaster = async (id, strategy) => {\r\n\t\ttry {\r\n\t\t\tconst result = await ConnectStrategyMaster(\r\n\t\t\t\tid,\r\n\t\t\t\tthis.state.selectedValueForBrokerName.value\r\n\t\t\t);\r\n\r\n\t\t\tif (result.status) {\r\n\t\t\t\tstrategy.is_master_connected = true;\r\n\t\t\t\tthis.setState({ setStrategyMasterModal: { status: false, id: 0 } });\r\n\t\t\t\tcogoToast.success(`${result.msg}`);\r\n\t\t\t} else {\r\n\t\t\t\tthis.setState({ apiResponseModal: { show: true, res: result } });\r\n\t\t\t}\r\n\r\n\t\t\tthis.setState({ selectedValueForBrokerName: null });\r\n\t\t} catch (error) {\r\n\t\t\tthis.setState({ apiResponseModal: { show: true, res: error } });\r\n\t\t}\r\n\t};\r\n\r\n\tsetApiResponseModal = () => {\r\n\t\tthis.setState({ apiResponseModal: { show: false, res: {} } });\r\n\t};\r\n\r\n\tsetPlaceOrderModal = () => {\r\n\t\tthis.setState({ placeOrderModal: { show: false, res: {} } });\r\n\t};\r\n\r\n\thandleOpenPlaceOrderModal = (obj) => {\r\n\t\tthis.setState({\r\n\t\t\tplaceOrderModal: { show: true, obj: obj },\r\n\t\t});\r\n\t};\r\n\r\n\thandleOpenStrategyPricingModal = (obj) => {\r\n\t\tthis.setState({\r\n\t\t\tshowAddStrategyPricingModal: { status: true, obj: obj },\r\n\t\t});\r\n\t};\r\n\r\n\thandleCloseStrategyPricingModal = () => {\r\n\t\tthis.setState({\r\n\t\t\tshowAddStrategyPricingModal: { status: false, obj: {} },\r\n\t\t});\r\n\t};\r\n\r\n\thandleEditStrategy = async (e) => {\r\n\t\te.preventDefault();\r\n\t\t// console.log(\r\n\t\t// \t\"call edit\",\r\n\t\t// \tthis.state.name,\r\n\t\t// \tthis.state.description,\r\n\t\t// \tthis.state.order_key,\r\n\t\t// \tthis.state.required_amount\r\n\t\t// );\r\n\r\n\t\tconst values = {\r\n\t\t\tstrategy_id: this.state.editStrategyModal.id,\r\n\t\t\tname: this.state.name,\r\n\t\t\tdescription: this.state.description,\r\n\t\t\trequired_amount: this.state.required_amount,\r\n\t\t\torder_key: this.state.order_key,\r\n\t\t};\r\n\r\n\t\ttry {\r\n\t\t\tconst result = await UpdateStrategyInfo(values);\r\n\r\n\t\t\tthis.setState({ editStrategyModal: { status: false, id: 0, ob: {} } });\r\n\t\t\tif (result.status) {\r\n\t\t\t\tcogoToast.success(`${result.msg}`);\r\n\t\t\t\tthis.props.fetchStrategies();\r\n\t\t\t} else {\r\n\t\t\t\tthis.setState({ apiResponseModal: { show: true, res: result } });\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t\tthis.setState({ apiResponseModal: { show: true, res: error } });\r\n\t\t}\r\n\t};\r\n\r\n\thandleEditModelOpen = (strategy) => {\r\n\t\t// console.log(\"============\", strategy);\r\n\t\tthis.setState({ name: strategy.name });\r\n\t\tthis.setState({ description: strategy.description });\r\n\t\tthis.setState({ order_key: strategy.order_key });\r\n\t\tthis.setState({ required_amount: strategy.required_amount });\r\n\r\n\t\tthis.setState({\r\n\t\t\teditStrategyModal: {\r\n\t\t\t\tstatus: true,\r\n\t\t\t\tid: strategy.id,\r\n\t\t\t\tobj: strategy,\r\n\t\t\t},\r\n\t\t});\r\n\t};\r\n\trender() {\r\n\t\tconst fetchStrategies = this.props.fetchStrategies;\r\n\t\tconst strategy = this.props.strategy.strategy_info;\r\n\t\tconst strategyData = this.props.strategy;\r\n\r\n\t\t// let record_data = strategy.record;\r\n\t\tconst record_data = strategyData.records.map((item) => {\r\n\t\t\treturn {\r\n\t\t\t\trecord_date: item.date.slice(0, 10), // Convert the date to \"YYYY-MM-DD\" format\r\n\t\t\t\tpnl: Number(item.pnl), // Convert pnl to a floating-point number with one decimal place\r\n\t\t\t};\r\n\t\t});\r\n\r\n\t\tlet date_array = [];\r\n\t\tlet pnl_array = [];\r\n\t\tlet pnl_count = 0;\r\n\t\tfor (let rec_data in record_data) {\r\n\t\t\tdate_array.push(record_data[rec_data].record_date);\r\n\t\t\tpnl_count += record_data[rec_data].pnl;\r\n\t\t\tpnl_array.push(pnl_count);\r\n\t\t}\r\n\t\tconst { apiResponseModal, trading_status, place_rejected } = this.state;\r\n\t\tconst areaData = {\r\n\t\t\tlabels: date_array,\r\n\t\t\tdatasets: [\r\n\t\t\t\t{\r\n\t\t\t\t\tlabel: \"Strategy\",\r\n\t\t\t\t\tdata: pnl_array,\r\n\t\t\t\t\tbackgroundColor: \"rgba(50, 255, 100, 0.2)\",\r\n\t\t\t\t\tborderColor: \"rgba(50, 255, 100, 1)\",\r\n\t\t\t\t\tborderWidth: 1,\r\n\t\t\t\t\tfill: true, // 3: no fill\r\n\t\t\t\t},\r\n\t\t\t],\r\n\t\t};\r\n\r\n\t\tconst areaOptions = {\r\n\t\t\tplugins: {\r\n\t\t\t\tfiller: {\r\n\t\t\t\t\tpropagate: true,\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t\tlegend: {\r\n\t\t\t\tdisplay: false,\r\n\t\t\t},\r\n\t\t\tscales: {\r\n\t\t\t\tyAxes: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tgridLines: {\r\n\t\t\t\t\t\t\tcolor: \"rgba(204, 204, 204,0.1)\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tticks: {\r\n\t\t\t\t\t\t\tdisplay: false,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t\txAxes: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tgridLines: {\r\n\t\t\t\t\t\t\tcolor: \"rgba(204, 204, 204,0.1)\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tticks: {\r\n\t\t\t\t\t\t\tdisplay: false,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t},\r\n\t\t};\r\n\t\tif (!this.state.active_status) {\r\n\t\t\treturn <></>;\r\n\t\t}\r\n\r\n\t\tconst tooltip = () => (\r\n\t\t\t<Tooltip id=\"tooltip\">\r\n\t\t\t\t<div\r\n\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\tbackgroundColor: \"black\",\r\n\t\t\t\t\t\tcolor: \"white\",\r\n\t\t\t\t\t\tpadding: \"2px 10px\",\r\n\t\t\t\t\t\tborderRadius: 3,\r\n\t\t\t\t\t\tborder: \"none\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<strong>{strategy.description}</strong>\r\n\t\t\t\t</div>\r\n\t\t\t</Tooltip>\r\n\t\t);\r\n\r\n\t\treturn (\r\n\t\t\t<>\r\n\t\t\t\t<div className=\"col-sm-4 mt-sm-0 my-4\">\r\n\t\t\t\t\t<div className=\"card strategy_card shadow shadow-3\">\r\n\t\t\t\t\t\t<div className=\"d-flex align-items-center justify-content-between\">\r\n\t\t\t\t\t\t\t<h4 className=\"mb-0 ml-3\" style={{ maxWidth: \"80%\" }}>\r\n\t\t\t\t\t\t\t\t<span className=\"ml-2\">{strategy.id})</span>{\" \"}\r\n\t\t\t\t\t\t\t\t<span>{strategy.name}</span>\r\n\t\t\t\t\t\t\t</h4>\r\n\t\t\t\t\t\t\t<div className=\"pr-2\">\r\n\t\t\t\t\t\t\t\t<OverlayTrigger\r\n\t\t\t\t\t\t\t\t\tplacement=\"left\"\r\n\t\t\t\t\t\t\t\t\toverlay={tooltip()}\r\n\t\t\t\t\t\t\t\t\toverlayClassName=\"custom-tooltip\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"mdi mdi-information-outline m-0\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ fontSize: \"1.5em\" }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</OverlayTrigger>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div className=\"card-body px-2 pb-3 pt-0 \">\r\n\t\t\t\t\t\t\t<div className=\"col-sm-12 p-0\">\r\n\t\t\t\t\t\t\t\t<Line data={areaData} options={areaOptions} height={90} />\r\n\t\t\t\t\t\t\t\t{strategy.is_master_connected ? (\r\n\t\t\t\t\t\t\t\t\t<div className=\"d-flex align-items-center justify-content-between\">\r\n\t\t\t\t\t\t\t\t\t\t<h4 className=\"mb-0 ml-2\">\r\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"mr-2\">{strategy.master_broker_id}</span>\r\n\t\t\t\t\t\t\t\t\t\t\t{strategy.master_name}{\" \"}\r\n\t\t\t\t\t\t\t\t\t\t</h4>\r\n\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"btn btn-danger p-1 ml-2\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.setState({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdeleteStrategyMasterModal: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstatus: true,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tid: strategy.id,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<i className=\"mdi mdi-lan-disconnect m-0\"></i>\r\n\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"info\"\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"ml-1 mt-1\"\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\tthis.setState({\r\n\t\t\t\t\t\t\t\t\t\t\t\tsetStrategyMasterModal: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstatus: true,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tid: strategy.id,\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\tthis.getStrategyMaster();\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\tConnect Master{\" \"}\r\n\t\t\t\t\t\t\t\t\t\t<i className=\"mdi mdi-lan-connect m-0 ml-2\"></i>\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t<div className=\"d-flex align-items-center\">\r\n\t\t\t\t\t\t\t\t\t<div className=\"col-sm-12 g-0 p-2\">\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"d-flex align-items-center justify-content-between\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Form.Group className=\"d-flex align-items-center m-0 \">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\thtmlFor=\"trading_status\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"m-0\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tTrading\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Check\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"form-check-input mt-0 ml-1\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"switch\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tid={`trading_status_${strategy.id}`}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tname=\"trading_status\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tchecked={trading_status}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={this.handleTradingStatusChange}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t{strategy.is_master_connected ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Group className=\"d-flex align-items-center m-0 text-dark\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div htmlFor=\"place_rejected\" className=\"m-0\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tPlace Rejected\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Check\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"form-check-input mt-0 ml-1\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"switch\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tid={`place_rejected_${strategy.id}`}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tname=\"place_rejected\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tchecked={place_rejected}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={this.handleStrategyStatusChange}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div className=\"d-flex align-items-center justify-content-between p-2\">\r\n\t\t\t\t\t\t\t\t\t<label\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"badge m-0 text-white\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundImage:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"linear-gradient(315deg, #ff5723 0%, #ff2938 80%)\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\tSubscribers : {strategyData.subscription_no}{\" \"}\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t<label className=\"badge badge-primary m-0\">\r\n\t\t\t\t\t\t\t\t\t\tAllowed : {strategyData.allow_user_no}{\" \"}\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t<label className=\"badge badge-outline-dark m-0 \">\r\n\t\t\t\t\t\t\t\t\t\t{\" \"}\r\n\t\t\t\t\t\t\t\t\t\t{strategy.is_private ? \"Private\" : \"Public\"}\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div className=\"d-flex align-items-center p-2 justify-content-between\">\r\n\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"btn btn-primary btn-rounded mt-2\"\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => this.handleDataClick(strategy)}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\tRecord : <i className=\"mdi mdi-chart-areaspline m-0\" />{\" \"}\r\n\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"btn btn-secondary btn-rounded mx-2 mt-2\"\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => this.handleUsersClick(strategy)}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\tUser <i className=\"mdi mdi-account-card-details m-0\" />{\" \"}\r\n\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"btn btn-info btn-rounded  mt-2\"\r\n\t\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\t\tthis.handleOpenStrategyPricingModal(strategy)\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\tPlans<span className=\"px-1\"> ₹</span>{\" \"}\r\n\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div className=\"d-flex align-items-center p-2 justify-content-between\">\r\n\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"btn btn-warning btn-rounded  mt-2\"\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => this.handleOpenPlaceOrderModal(strategy)}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\tPlace Order{\" \"}\r\n\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t<div className=\"d-flex align-items-center justify-content-between p-1\">\r\n\t\t\t\t\t\t\t\t\t<div className=\"d-flex align-items-center p-2\">\r\n\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"btn btn-danger btn-rounded mt-2 mr-2\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.setState({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdeleteStrategyModal: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstatus: true,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tid: strategy.id,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{\" \"}\r\n\t\t\t\t\t\t\t\t\t\t\t<i className=\"mdi mdi-delete-forever m-0\" />\r\n\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"btn btn-info btn-rounded mt-2\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => this.handleEditModelOpen(strategy)}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{\" \"}\r\n\t\t\t\t\t\t\t\t\t\t\t<i className=\"mdi mdi-pencil m-0\" />\r\n\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"d-flex align-items-center mt-3\">\r\n\t\t\t\t\t\t\t\t\t\t<h4 className=\"ml-2\">\r\n\t\t\t\t\t\t\t\t\t\t\tAmount :\r\n\t\t\t\t\t\t\t\t\t\t\t<spna>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{\" \"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t{getConvertedAmount(strategy.required_amount)}\r\n\t\t\t\t\t\t\t\t\t\t\t</spna>{\" \"}\r\n\t\t\t\t\t\t\t\t\t\t</h4>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t{this.state.deleteStrategyMasterModal.status && (\r\n\t\t\t\t\t<Modal show={true} animation={true} size=\"md\" className=\"mt-5\">\r\n\t\t\t\t\t\t<Modal.Header className=\"text-center py-3\">\r\n\t\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t\t<h5 className=\"mb-0\">Delete Strategy Master</h5>\r\n\t\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t\t<Modal.Body className=\"p-10\">\r\n\t\t\t\t\t\t\tDo you want to remove this strategy master ?\r\n\t\t\t\t\t\t</Modal.Body>\r\n\t\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\tthis.setState({\r\n\t\t\t\t\t\t\t\t\t\t\tdeleteStrategyMasterModal: { status: false, id: 0 },\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tvariant=\"outline-primary\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"mx-2 px-3\"\r\n\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\tthis.handleRemoveStrategyMaster(\r\n\t\t\t\t\t\t\t\t\t\t\tthis.state.deleteStrategyMasterModal.id,\r\n\t\t\t\t\t\t\t\t\t\t\tstrategy\r\n\t\t\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tYes\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t\t</Modal>\r\n\t\t\t\t)}\r\n\r\n\t\t\t\t{this.state.deleteStrategyModal.status && (\r\n\t\t\t\t\t<Modal show={true} animation={true} size=\"md\" className=\"mt-5\">\r\n\t\t\t\t\t\t<Modal.Header className=\"text-center py-3\">\r\n\t\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t\t<h5 className=\"mb-0\">Delete Strategy</h5>\r\n\t\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t\t<Modal.Body className=\"p-10\">\r\n\t\t\t\t\t\t\tDo you want to remove this strategy ?\r\n\t\t\t\t\t\t</Modal.Body>\r\n\t\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\tthis.setState({\r\n\t\t\t\t\t\t\t\t\t\t\tdeleteStrategyModal: { status: false, id: 0 },\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tvariant=\"outline-primary\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"mx-2 px-3\"\r\n\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\tthis.handleRemoveStrategy(\r\n\t\t\t\t\t\t\t\t\t\t\tthis.state.deleteStrategyModal.id,\r\n\t\t\t\t\t\t\t\t\t\t\tstrategy\r\n\t\t\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tYes\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t\t</Modal>\r\n\t\t\t\t)}\r\n\r\n\t\t\t\t{this.state.setStrategyMasterModal.status && (\r\n\t\t\t\t\t<Modal show={true} animation={true} size=\"md\" className=\"mt-5\">\r\n\t\t\t\t\t\t<Modal.Header className=\"text-center py-3\">\r\n\t\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t\t<h5 className=\"mb-0\">Set Strategy Master</h5>\r\n\t\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t\t<Modal.Body className=\"p-10 \">\r\n\t\t\t\t\t\t\t<Form.Group controlId=\"searchableDropdown\">\r\n\t\t\t\t\t\t\t\t<Form.Label>Select Master Account</Form.Label>\r\n\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\tcolors=\"primary\"\r\n\t\t\t\t\t\t\t\t\tvalue={this.state.selectedValueForBrokerName}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\tthis.setState({ selectedValueForBrokerName: e });\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\toptions={this.state.options}\r\n\t\t\t\t\t\t\t\t\tisSearchable={true}\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"Select Account\"\r\n\t\t\t\t\t\t\t\t\tstyles={mergeStyles(\r\n\t\t\t\t\t\t\t\t\t\thoverEffectOnSelect,\r\n\t\t\t\t\t\t\t\t\t\tcustomStylesForSelect\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t</Modal.Body>\r\n\t\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\tthis.setState({\r\n\t\t\t\t\t\t\t\t\t\t\tsetStrategyMasterModal: { status: false, id: 0 },\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tvariant=\"outline-primary\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"mx-2 px-3\"\r\n\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\tthis.handleConnectStrategyMaster(\r\n\t\t\t\t\t\t\t\t\t\t\tthis.state.setStrategyMasterModal.id,\r\n\t\t\t\t\t\t\t\t\t\t\tstrategy\r\n\t\t\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tYes\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t\t</Modal>\r\n\t\t\t\t)}\r\n\t\t\t\t{this.state.showAddStrategyPricingModal.status && (\r\n\t\t\t\t\t<AddStrategyPricingModal\r\n\t\t\t\t\t\tonClose={this.handleCloseStrategyPricingModal}\r\n\t\t\t\t\t\tdata={this.state.showAddStrategyPricingModal.obj}\r\n\t\t\t\t\t/>\r\n\t\t\t\t)}\r\n\r\n\t\t\t\t{this.state.editStrategyModal.status && (\r\n\t\t\t\t\t<Modal show={true} animation={true} size=\"md\" className=\"mt-5\">\r\n\t\t\t\t\t\t<Form onSubmit={this.handleEditStrategy}>\r\n\t\t\t\t\t\t\t<Modal.Header className=\"text-center py-3\">\r\n\t\t\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t\t\t<h5 className=\"mb-0\">Edit Strategy</h5>\r\n\t\t\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t\t\t<Modal.Body className=\"p-10\">\r\n\t\t\t\t\t\t\t\t<div className=\"row\">\r\n\t\t\t\t\t\t\t\t\t<div className=\"col-sm-4\">\r\n\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t<Form.Label>Strategy Name : </Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tname=\"name\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tdefaultValue={this.state.name}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tthis.setState({ name: e.target.value });\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"col-sm-4\">\r\n\t\t\t\t\t\t\t\t\t\t{\" \"}\r\n\t\t\t\t\t\t\t\t\t\t<Form.Group className=\"mx-1\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Form.Label>Required Amount : </Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tname=\"required_amount\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tdefaultValue={this.state.required_amount}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tthis.setState({ required_amount: e.target.value });\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"col-sm-4\">\r\n\t\t\t\t\t\t\t\t\t\t<Form.Group className=\"mx-1\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Form.Label>Order Key : </Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tname=\"order_key\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tdefaultValue={this.state.order_key}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tthis.setState({ order_key: e.target.value });\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t<Form.Label>Strategy Description : </Form.Label>\r\n\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\t\t\tname=\"description\"\r\n\t\t\t\t\t\t\t\t\t\tdefaultValue={this.state.description}\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\tthis.setState({ description: e.target.value });\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t</Modal.Body>\r\n\t\t\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\t\t\tonClick={this.handleEditStrategyModal}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\ttype=\"submit\"\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"outline-primary\"\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"mx-2 px-3\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\tYes\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t\t\t</Form>\r\n\t\t\t\t\t</Modal>\r\n\t\t\t\t)}\r\n\t\t\t\t{this.state.placeOrderModal.show && (\r\n\t\t\t\t\t<PlaceOrderModal\r\n\t\t\t\t\t\tsetPlaceOrderModal={this.setPlaceOrderModal}\r\n\t\t\t\t\t\tid={this.props.strategy.strategy_info.id}\r\n\t\t\t\t\t/>\r\n\t\t\t\t)}\r\n\t\t\t\t{apiResponseModal.show && (\r\n\t\t\t\t\t<ResponseModal\r\n\t\t\t\t\t\tres={apiResponseModal.res}\r\n\t\t\t\t\t\tsetApiResponseModal={this.setApiResponseModal}\r\n\t\t\t\t\t\tmsg={apiResponseModal.msg}\r\n\t\t\t\t\t/>\r\n\t\t\t\t)}\r\n\t\t\t</>\r\n\t\t);\r\n\t}\r\n}\r\n\r\nexport default StrategyCards;\r\n", "import React from \"react\";\r\nimport { createTheme } from \"react-data-table-component\";\r\n\r\nexport const customStylesForSelect = {\r\n\toption: (provided, state) => ({\r\n\t\t...provided,\r\n\t\tbackgroundColor: state.isSelected ? \"blue\" : \"white\", // Change the background color as desired\r\n\t\tcolor: state.isSelected ? \"white\" : \"black\", // Change the text color as desired\r\n\t}),\r\n};\r\nexport const hoverEffectOnSelect = {\r\n\toption: (base, state) => ({\r\n\t\t...customStylesForSelect.option(base, state),\r\n\t\t\"&:hover\": {\r\n\t\t\tbackgroundColor: \"lightgray\", // Change the hover background color\r\n\t\t\tcolor: \"black\", // Change the hover text color\r\n\t\t},\r\n\t}),\r\n};\r\n\r\nexport const createDataTableTheme = () => {\r\n\tcreateTheme(\r\n\t\t\"solarized\",\r\n\t\t{\r\n\t\t\tbackground: {\r\n\t\t\t\tdefault: \"transparent\",\r\n\t\t\t},\r\n\t\t\taction: {\r\n\t\t\t\tbutton: \"rgba(0,0,0,.54)\",\r\n\t\t\t\thover: \"rgba(0,0,0,.08)\",\r\n\t\t\t\tdisabled: \"rgba(0,0,0,.12)\",\r\n\t\t\t},\r\n\t\t},\r\n\t\t\"dark\"\r\n\t);\r\n};\r\n\r\nexport function getTransactionType(transaction) {\r\n\tlet a_type = transaction.charAt(0);\r\n\tlet is_buy =\r\n\t\ta_type === \"B\" ||\r\n\t\ta_type === \"b\" ||\r\n\t\ta_type === \"buy\" ||\r\n\t\ta_type === \"BUY\" ||\r\n\t\ta_type === \"Buy\";\r\n\tlet is_sell =\r\n\t\ta_type === \"S\" ||\r\n\t\ta_type === \"s\" ||\r\n\t\ta_type === \"sell\" ||\r\n\t\ta_type === \"SELL\" ||\r\n\t\ta_type === \"Sell\";\r\n\tif (is_buy) {\r\n\t\treturn <label className=\"badge badge-outline-warning\">BUY</label>;\r\n\t} else if (is_sell) {\r\n\t\treturn <label className=\"badge badge-outline-primary\">SELL</label>;\r\n\t} else {\r\n\t\treturn (\r\n\t\t\ttransaction && (\r\n\t\t\t\t<label className=\"badge badge-outline-danger\">\r\n\t\t\t\t\t{transaction.toUpperCase()}\r\n\t\t\t\t</label>\r\n\t\t\t)\r\n\t\t);\r\n\t}\r\n}\r\n\r\nexport function getPnlComponent(pnlVal) {\r\n\tpnlVal = parseFloat(pnlVal || 0).toFixed(2);\r\n\tif (pnlVal < 0) {\r\n\t\treturn (\r\n\t\t\t'<span class=\"text-danger\"> ' +\r\n\t\t\tpnlVal +\r\n\t\t\t' <i class=\"mdi mdi-arrow-down\"></i></span>'\r\n\t\t);\r\n\t} else if (pnlVal === 0 || pnlVal === null) {\r\n\t\tpnlVal = 0;\r\n\t\treturn \"<span> \" + pnlVal + \"</span>\";\r\n\t}\r\n\treturn (\r\n\t\t'<span class=\"text-success\"> ' +\r\n\t\tpnlVal +\r\n\t\t' <i class=\"mdi mdi-arrow-up\"></i></span>'\r\n\t);\r\n}\r\n\r\nexport function getSubPnlComponent(pnlVal) {\r\n\tpnlVal = parseFloat(pnlVal || 0).toFixed(2);\r\n\tif (pnlVal < 0) {\r\n\t\treturn (\r\n\t\t\t<span className=\"text-danger\">\r\n\t\t\t\t{\" \"}\r\n\t\t\t\t{pnlVal} <i className=\"mdi mdi-arrow-down\"></i>\r\n\t\t\t</span>\r\n\t\t);\r\n\t} else if (pnlVal === 0 || pnlVal === null) {\r\n\t\tpnlVal = 0;\r\n\t\treturn <span> {pnlVal}</span>;\r\n\t}\r\n\treturn (\r\n\t\t<span className=\"text-success\">\r\n\t\t\t{\" \"}\r\n\t\t\t{pnlVal} <i className=\"mdi mdi-arrow-up\"></i>\r\n\t\t</span>\r\n\t);\r\n}\r\n\r\nexport function getSubTransactionType(transaction) {\r\n\tlet a_type = transaction.charAt(0);\r\n\tlet is_buy =\r\n\t\ta_type === \"B\" ||\r\n\t\ta_type === \"b\" ||\r\n\t\ta_type === \"buy\" ||\r\n\t\ta_type === \"BUY\" ||\r\n\t\ta_type === \"Buy\";\r\n\tif (is_buy) {\r\n\t\treturn '<label className=\"badge badge-outline-warning\">BUY</label>';\r\n\t} else {\r\n\t\treturn '<label className=\"badge badge-outline-primary\">SELL</label>';\r\n\t}\r\n}\r\n\r\nexport function getOrderStatus(status, reason) {\r\n\tif (status.charAt(0) === \"C\" || status.charAt(0) === \"c\") {\r\n\t\treturn (\r\n\t\t\t<label\r\n\t\t\t\tclassName=\"badge badge-outline-success\"\r\n\t\t\t\tdata-toggle=\"tooltip\"\r\n\t\t\t\tdata-placement=\"top\"\r\n\t\t\t\ttitle={reason}\r\n\t\t\t>\r\n\t\t\t\tCOMPLETED\r\n\t\t\t</label>\r\n\t\t);\r\n\t} else if (status.charAt(0) === \"R\" || status.charAt(0) === \"r\") {\r\n\t\treturn (\r\n\t\t\t<label\r\n\t\t\t\tclassName=\"badge badge-outline-danger\"\r\n\t\t\t\tdata-toggle=\"tooltip\"\r\n\t\t\t\tdata-placement=\"top\"\r\n\t\t\t\ttitle={reason}\r\n\t\t\t>\r\n\t\t\t\tREJECTED\r\n\t\t\t</label>\r\n\t\t);\r\n\t\t//} else if (status.charAt(0) === \"O\" || status.charAt(0) === \"o\") {\r\n\t\t//   return (\r\n\t\t//     <label\r\n\t\t//       className=\"badge badge-outline-secondary\"\r\n\t\t//       data-toggle=\"tooltip\"\r\n\t\t//       data-placement=\"top\"\r\n\t\t//       title={reason}\r\n\t\t//     >\r\n\t\t//       OPEN\r\n\t\t//     </label>\r\n\t\t//   );\r\n\t} else {\r\n\t\treturn (\r\n\t\t\t<label\r\n\t\t\t\tclassName=\"badge badge-outline-info\"\r\n\t\t\t\tdata-toggle=\"tooltip\"\r\n\t\t\t\tdata-placement=\"top\"\r\n\t\t\t\ttitle={reason}\r\n\t\t\t>\r\n\t\t\t\t{status}\r\n\t\t\t</label>\r\n\t\t);\r\n\t}\r\n}\r\n\r\nexport function getConvertedAmount(amount) {\r\n\tif (isNaN(amount)) {\r\n\t\treturn \"...\";\r\n\t}\r\n\tif (amount < 1000) {\r\n\t\treturn amount.toString();\r\n\t}\r\n\tif (amount >= 1000 && amount < 100000) {\r\n\t\treturn (amount / 1000).toFixed(2) + \" K\";\r\n\t}\r\n\tif (amount >= 100000 && amount < 10000000) {\r\n\t\treturn (amount / 100000).toFixed(2) + \" L\";\r\n\t}\r\n\tif (amount >= 10000000) {\r\n\t\treturn (amount / 10000000).toFixed(2) + \" Cr\";\r\n\t}\r\n}\r\n"], "names": ["WrappedComponent", "props", "history", "useHistory", "useEffect", "token", "localStorage", "getItem", "hostname", "window", "location", "login_url", "requestOptions", "method", "headers", "body", "JSON", "stringify", "fetch", "API_URL", "then", "response", "json", "data", "code", "href", "catch", "error", "console", "_jsx", "AddStrategyPricingModal", "apiResponseModal", "setApiResponseModal", "useState", "show", "res", "formValues", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "duration", "price", "is_per_lot", "handleChange", "event", "name", "value", "checked", "target", "type", "showDeletePopup", "setShowDeletePopup", "status", "obj", "ind", "mutation", "useMutation", "values", "AddStrategyPricing", "cogoToast", "success", "concat", "msg", "fetchStrategyData", "e", "log", "tableDataFormatted", "setTableDataFormatted", "rawUserMap", "useMemo", "Map", "GetStrategyPricing", "id", "tableData", "rowTableData", "map", "index", "push", "className", "children", "action", "<PERSON><PERSON>", "variant", "onClick", "getRowFormatForBrokerTable", "setTableData", "set", "RemoveUserBrokerMutation", "RemoveStrategyPricing", "delete", "prevData", "updatedData", "splice", "Promise", "reject", "_jsxs", "_Fragment", "Modal", "animation", "size", "Form", "onSubmit", "durationVal", "Number", "priceVal", "customForm", "strategy_id", "org_id", "mutate", "preventDefault", "Header", "Title", "Body", "Group", "Label", "Control", "onChange", "required", "Check", "htmlFor", "DataTable", "columns", "selector", "row", "sortable", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "wrap", "omit", "pagination", "paginationPerPage", "highlightOnHover", "<PERSON><PERSON><PERSON><PERSON>", "theme", "Footer", "onClose", "ResponseModal", "CreateStrategyModal", "description", "required_amount", "is_copy_trade", "is_private", "order_key", "master_broker", "AddStrategy", "trim", "fetchStrategies", "PlaceOrderModal", "showModal", "setShowModal", "onBtnClose", "setPlaceOrderModal", "qty", "set<PERSON><PERSON>", "selectedOption", "setSelectedOption", "label", "selectedOptionSide", "setSelectedOptionSide", "selectedTypeOption", "setSelectedTypeOption", "selectedProductOption", "setSelectedProductOption", "script", "setScript", "selectedDate", "setSelectedDate", "Date", "strikPrice", "setStrikPrice", "confirmModel", "setConfirmModel", "placeOrderApiCall", "params", "PlaceOrderApi", "Select", "options", "styles", "mergeStyles", "hoverEffectOnSelect", "customStylesForSelect", "placeholder", "_e$target$value", "toUpperCase", "DatePicker", "selected", "date", "dateFormat", "handleClearButton", "style", "backgroundColor", "width", "margin", "borderCollapse", "color", "format", "order_type", "script_name", "quantity", "transaction", "product", "expiry_date", "strike_price", "option_type", "Strategy", "Component", "constructor", "super", "this", "setState", "isLoading", "GetStrategies", "strategyData", "isDataLoaded", "errorMsg", "finally", "handleOpenCreateStrategyModal", "showAddStrategyModal", "handleCreateStrategyModal", "state", "bind", "componentDidMount", "render", "strategy", "StrategyCards", "parent_props", "height", "Spinner", "<PERSON><PERSON><PERSON>", "handleDataClick", "open", "handleUsersClick", "handleEditStrategyModal", "editStrategyModal", "handleTradingStatusChange", "async", "result", "UpdateStrategyTradingFlag", "strategy_info", "handleStrategyStatusChange", "UpdateStrategyRejectedFlag", "getStrategyMaster", "GetMastersForStrategy", "options_arr", "jsonArr", "Object", "i", "length", "concatenated_string", "handleRemoveStrategyMaster", "DisconnectmasterFromoStrategy", "deleteStrategyMasterModal", "is_master_connected", "handleRemoveStrategy", "DeleteStrategy", "deleteStrategyModal", "handleConnectStrategyMaster", "ConnectStrategyMaster", "selectedValueForBrokerName", "setStrategyMasterModal", "placeOrderModal", "handleOpenPlaceOrderModal", "handleOpenStrategyPricingModal", "showAddStrategyPricingModal", "handleCloseStrategyPricingModal", "handleEditStrategy", "UpdateStrategyInfo", "ob", "handleEditModelOpen", "active_status", "trading_status", "trading_flag", "place_rejected", "record_data", "records", "item", "record_date", "slice", "pnl", "date_array", "pnl_array", "pnl_count", "rec_data", "areaData", "labels", "datasets", "borderColor", "borderWidth", "fill", "OverlayTrigger", "placement", "overlay", "<PERSON><PERSON><PERSON>", "padding", "borderRadius", "border", "overlayClassName", "fontSize", "Line", "plugins", "filler", "propagate", "legend", "display", "scales", "yAxes", "gridLines", "ticks", "xAxes", "master_broker_id", "master_name", "backgroundImage", "subscription_no", "allow_user_no", "getConvertedAmount", "controlId", "colors", "isSearchable", "defaultValue", "option", "provided", "isSelected", "base", "getTransactionType", "a_type", "char<PERSON>t", "is_sell", "getSubPnlComponent", "pnlVal", "parseFloat", "toFixed", "getOrderStatus", "reason", "title", "amount", "isNaN", "toString"], "sourceRoot": ""}
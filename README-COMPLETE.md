# 🚀 Complete Hostinger Account Manager

**Manage your ENTIRE Hostinger account from one local folder!**

## 🎯 What This Does

✅ **Auto-discovers new websites** when you add them in Hostinger  
✅ **Downloads everything** - all domains, all files, all folders  
✅ **True bidirectional sync** - changes anywhere reflect everywhere instantly  
✅ **Complete local control** - manage like normal folders  
✅ **Real-time monitoring** - detects new domains automatically  
✅ **Instant sync** - add/edit/delete files and see changes immediately  

## 🏗️ Your Local Structure

```
algofactoryadmin/
├── domains/
│   ├── algofactory.co.in/     ← Complete website
│   ├── algoprofit.in/         ← Complete website  
│   ├── anitabhanushali.com/   ← Complete website
│   ├── mojevents.com/         ← Complete website
│   └── ... (ALL your domains)
```

## 🚀 Quick Start

### 1. Start Complete Account Management
```bash
npm start
```

This will:
- Connect to your Hostinger account
- Scan for ALL domains (including new ones)
- Download any missing domains
- Start real-time bidirectional sync
- Monitor for new domains every 30 seconds

## 🎮 How It Works

### **When You Add a New Website in Hostinger:**
1. System detects it automatically (within 30 seconds)
2. Creates local folder for the new domain
3. Downloads all files from the new website
4. Starts watching it for changes

### **When You Work Locally:**
1. Edit any file in any domain folder
2. Add new files or folders
3. Delete files or folders
4. **Changes sync to live server INSTANTLY**

### **When Changes Happen on Server:**
1. System can detect server-side changes
2. Downloads new files to local
3. Keeps everything in sync

## 📊 Real-time Features

- **File Changes**: Edit locally → Instant upload to server
- **New Files**: Create locally → Instant upload to server  
- **Deletions**: Delete locally → Instant deletion on server
- **New Domains**: Add in Hostinger → Auto-appears locally
- **Folder Operations**: Create/delete folders → Syncs instantly

## 🛠️ Commands

| Command | Description |
|---------|-------------|
| `npm start` | Start complete account management |
| `npm run manage` | Same as start |
| `npm run list` | List all managed domains |
| `npm run status` | Show sync status |

## 🔄 Workflow Examples

### **Edit a Website:**
1. Open `domains/yourdomain.com/index.html`
2. Make changes and save
3. Check your live website - changes are there!

### **Add a New Page:**
1. Create `domains/yourdomain.com/new-page.html`
2. Save the file
3. Page is instantly available on your live site

### **Delete Files:**
1. Delete any file from local domain folder
2. File is instantly removed from live server

### **Add New Website in Hostinger:**
1. Create new domain in Hostinger control panel
2. Within 30 seconds, it appears in your local `domains/` folder
3. Start editing immediately!

## 🎯 Perfect For

- **Web developers** managing multiple client sites
- **Agency owners** with many domains
- **Anyone** who wants local control over all websites
- **AI-assisted development** with instant live preview

## 🔧 Configuration

The system automatically manages your configuration in `sync-config.json`. It includes:
- All your domains with their paths
- Sync settings (1-second intervals for instant sync)
- File exclusion patterns
- Your saved SSH credentials

## 🚨 Important Notes

- **Backup first**: Always backup important sites before major changes
- **Test changes**: Test in staging before production if possible
- **Monitor console**: Watch the console for sync status and errors
- **Network dependent**: Requires stable internet for real-time sync

## 🎉 Benefits

1. **One Place Management**: All websites in one local folder
2. **Instant Changes**: See edits live immediately  
3. **Auto-Discovery**: New sites appear automatically
4. **Full Control**: Add, edit, delete anything
5. **AI-Friendly**: Perfect for working with AI coding assistants
6. **Time Saving**: No more FTP uploads or cPanel file manager

## 🔥 Advanced Usage

- Work with multiple domains simultaneously
- Use your favorite code editor on all sites
- Version control with Git on local files
- Batch operations across multiple domains
- AI-assisted coding with instant live results

**Start managing your entire Hostinger account like a local file system!**

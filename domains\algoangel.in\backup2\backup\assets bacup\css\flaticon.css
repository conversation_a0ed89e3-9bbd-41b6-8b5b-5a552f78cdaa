	/*
  	Flaticon icon font: Flaticon
  	Creation date: 26/01/2021 06:01
  	*/

@font-face {
  font-family: "Flaticon";
  src: url("../fonts/Flaticon.eot");
  src: url("../fonts/Flaticond41d.eot?#iefix") format("embedded-opentype"),
       url("../fonts/Flaticon.woff2") format("woff2"),
       url("../fonts/Flaticon.woff") format("woff"),
       url("../fonts/Flaticon.ttf") format("truetype"),
       url("../fonts/Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("../fonts/Flaticon.svg#Flaticon") format("svg");
  }
}

[class^="flaticon-"]:before, [class*=" flaticon-"]:before,
[class^="flaticon-"]:after, [class*=" flaticon-"]:after {   
  font-family: Flaticon;
  font-style: normal;
}

.flaticon-voice-recognition:before { content: "\f100"; }
.flaticon-machine-learning:before { content: "\f101"; }
.flaticon-twitter:before { content: "\f102"; }
.flaticon-youtube:before { content: "\f103"; }
.flaticon-shopping-cart:before { content: "\f104"; }
.flaticon-instagram:before { content: "\f105"; }
.flaticon-facebook:before { content: "\f106"; }
.flaticon-email:before { content: "\f107"; }
.flaticon-location:before { content: "\f108"; }
.flaticon-phone-call:before { content: "\f109"; }
.flaticon-headphones:before { content: "\f10a"; }
.flaticon-add:before { content: "\f10b"; }
.flaticon-square-with-verification-sign:before { content: "\f10c"; }
.flaticon-quote-1:before { content: "\f10d"; }
.flaticon-left-chevron:before { content: "\f10e"; }
.flaticon-right-chevron:before { content: "\f10f"; }
.flaticon-rounded-check-mark:before { content: "\f110"; }
.flaticon-square:before { content: "\f111"; }
.flaticon-loupe:before { content: "\f112"; }
.flaticon-timetable:before { content: "\f113"; }
.flaticon-comment:before { content: "\f114"; }
.flaticon-user:before { content: "\f115"; }
.flaticon-remove:before { content: "\f116"; }
.flaticon-machine-learning-1:before { content: "\f117"; }
.flaticon-processor:before { content: "\f118"; }
.flaticon-automation:before { content: "\f119"; }
.flaticon-predictive-chart:before { content: "\f11a"; }
.flaticon-deep-learning:before { content: "\f11b"; }
.flaticon-diagram:before { content: "\f11c"; }
.flaticon-machine-learning-2:before { content: "\f11d"; }
.flaticon-email-1:before { content: "\f11e"; }
.flaticon-paper-plane:before { content: "\f11f"; }
.flaticon-search:before { content: "\f120"; }
.flaticon-down-chevron:before { content: "\f121"; }
.flaticon-check-box:before { content: "\f122"; }
.flaticon-left-quote:before { content: "\f123"; }
.flaticon-star:before { content: "\f124"; }
.flaticon-star-1:before { content: "\f125"; }
.flaticon-left-arrow:before { content: "\f126"; }
.flaticon-right-arrow:before { content: "\f127"; }
.flaticon-facebook-logo:before { content: "\f128"; }
.flaticon-instagram-1:before { content: "\f129"; }
.flaticon-pinterest:before { content: "\f12a"; }
.flaticon-play-button:before { content: "\f12b"; }
.flaticon-share:before { content: "\f12c"; }
.flaticon-maps-and-flags:before { content: "\f12d"; }
.flaticon-phone-auricular:before { content: "\f12e"; }
.flaticon-email-2:before { content: "\f12f"; }
.flaticon-chevron:before { content: "\f130"; }
.flaticon-ellipsis:before { content: "\f131"; }
.flaticon-grid:before { content: "\f132"; }
.flaticon-data-mining:before { content: "\f133"; }
.flaticon-rocket:before { content: "\f134"; }
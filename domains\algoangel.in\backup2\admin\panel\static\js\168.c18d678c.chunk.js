"use strict";(self.webpackChunkadminpanel=self.webpackChunkadminpanel||[]).push([[168],{7455:(e,a,t)=>{t.a(e,(async(e,r)=>{try{t.d(a,{Z:()=>c});var s=t(2791),l=t(4880),n=t(7971),o=t(184),i=e([n]);n=(i.then?(await i)():i)[0];const c=e=>a=>{const t=(0,l.k6)();return(0,s.useEffect)((()=>{const e=localStorage.getItem("admin_access_token"),{hostname:a}=window.location,t="https://"+a+"/admin/";if(e){const a={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:e})};fetch(n.T5+"/auth/verify",a).then((e=>e.json())).then((e=>{"token_not_valid"===e.code&&(window.location.href=t)})).catch((e=>{console.error("Error:",e),window.location.href=t}))}else window.location.href=t}),[t]),(0,o.jsx)(e,{...a})};r()}catch(c){r(c)}}))},1335:(e,a,t)=>{t.a(e,(async(e,r)=>{try{t.d(a,{Z:()=>N});var s=t(2791),l=t(5742),n=t(7691),o=t(3513),i=t(1933),c=t(3360),d=t(4912),u=t(1662),m=t(4970),h=t(2240),b=t(1025),p=t(6960),x=t(8573),g=t.n(x),v=t(4536),j=t(184),f=e([n]);function N(e){const[a,t]=(0,s.useState)({show:!1,res:{}}),[r,x]=(0,s.useState)({status:!1,obj:{}}),[f,w]=(0,s.useState)(null),[N,y]=(0,s.useState)([{label:"demo",value:"demo"},{label:"lead",value:"lead"},{label:"active",value:"active"},{label:"inactive",value:"inactive"}]),[_,k]=(0,s.useState)({status:!1,obj:{}}),[C,Z]=(0,s.useState)([]),S=(0,s.useMemo)((()=>new Map),[]),[L,E]=(0,s.useState)(0);function W(e){e.map(((e,a)=>{var t,r,s,l,n,o;const i=!0===e.id_broker_attached?"attached":"empty";e.search_val=(null===(t=e.status)||void 0===t?void 0:t.toLowerCase())+" "+(null===(r=e.first_name)||void 0===r?void 0:r.toLowerCase())+" "+(null===(s=e.last_name)||void 0===s?void 0:s.toLowerCase())+" "+(null===(l=e.email)||void 0===l?void 0:l.toLowerCase())+" "+(null===(n=e.mobile_no)||void 0===n?void 0:n.toLowerCase())+" "+(null===(o=e.avilable_credit)||void 0===o?void 0:o.toLowerCase())+" "+i+" "+e.create_time.toLowerCase()+" "+e.referral_count,S.set(e.id,e)}))}function O(e){var a=[];e.map(((e,t)=>{a.push(P(t,e))})),Z(a)}(0,s.useEffect)((()=>{if(e.brokerData.data){var a;const t=Object.values(null===(a=e.brokerData)||void 0===a?void 0:a.data);O(t),W(t)}}),[e.brokerData.data]);const D=e=>{k({status:!0,obj:e}),E(e.referral_percentage),w({label:e.status,value:e.status})};function P(e,a){var t,r,s,l;return{index:e+1,username:(0,j.jsxs)("div",{children:[(0,j.jsx)("div",{children:a.email}),(0,j.jsxs)("div",{children:[a.first_name," ",a.last_name]})]}),mobile:a.mobile_no,credit:null===a.avilable_credit?0:a.avilable_credit,strategy:a.subscription_count,create_time:a.create_time.slice(0,10),referral_percentage:a.referral_percentage,referral_count:a.referral_count,status:"active"==(null===a||void 0===a||null===(t=a.status)||void 0===t?void 0:t.toLowerCase())?(0,j.jsx)("label",{className:"badge badge-outline-success",children:" Active "}):"inactive"==(null===a||void 0===a||null===(r=a.status)||void 0===r?void 0:r.toLowerCase())?(0,j.jsx)("label",{className:"badge badge-outline-danger",children:" Inactive "}):"lead"==(null===a||void 0===a||null===(s=a.status)||void 0===s?void 0:s.toLowerCase())?(0,j.jsx)("label",{className:"badge badge-outline-primary",children:" Lead "}):"demo"==(null===a||void 0===a||null===(l=a.status)||void 0===l?void 0:l.toLowerCase())?(0,j.jsx)("label",{className:"badge badge-outline-warning",children:" Demo "}):(0,j.jsx)("label",{className:"badge badge-outline-info",children:"Nill"}),broker:a.id_broker_attached?(0,j.jsx)("label",{className:"badge badge-outline-success",children:" Attached "}):(0,j.jsx)("label",{className:"badge badge-outline-danger",children:" Empty "}),demat:(0,j.jsx)(j.Fragment,{children:(0,j.jsxs)("div",{className:"d-flex align-item-center",children:[(0,j.jsx)(c.Z,{variant:"primary",className:"p-2",onClick:()=>window.open("/admin/panel/users/".concat(a.id),"_blank"),children:(0,j.jsx)("i",{className:"mdi mdi-eye btn-icon-append m-0"})}),(0,j.jsx)(c.Z,{variant:"primary",className:"p-2 ml-2",onClick:()=>D(a),children:(0,j.jsx)("i",{className:"mdi mdi-border-color m-0"})})]})}),obj:a}}const F=[{name:"Id",selector:e=>e.index,sortable:!0,maxWidth:"60px",minWidth:"60px"},{name:"Username",selector:e=>e.username,sortable:!0,maxWidth:"200px",minWidth:"200px",wrap:!0},{name:"Mobile",selector:e=>e.mobile,sortable:!0,wrap:!0},{name:"Credit",selector:e=>e.credit,maxWidth:"90px",minWidth:"90px",sortable:!0},{name:"Date",selector:e=>e.create_time,sortable:!0,maxWidth:"110px",minWidth:"110px",wrap:!0},{name:"Subscription Count",selector:e=>e.strategy,sortable:!0,maxWidth:"60px",minWidth:"60px",wrap:!0},{name:"Referral Count",selector:e=>e.referral_count,sortable:!0,maxWidth:"60px",minWidth:"60px",wrap:!0},{name:"R(%)",selector:e=>e.referral_percentage,maxWidth:"75px",minWidth:"75px",sortable:!0},{name:"Status",selector:e=>e.status,sortable:!1,maxWidth:"110px",minWidth:"110px"},{name:"Broker",selector:e=>e.broker,sortable:!0,maxWidth:"110px",minWidth:"110px",sortFunction:(e,a)=>!0===e.obj.id_broker_attached&&!1===a.obj.id_broker_attached?1:-1},{name:"Action",selector:e=>e.demat,sortable:!1},{name:"obj",selector:e=>e.obj,omit:!0}],T=g()((e=>{const a=[];for(let t of S.values())t.search_val.includes(e.toLowerCase())&&a.push(t);Z(a.map(((e,a)=>P(a,e))))}),300),A=()=>{k({status:!1,obj:{}})},U=(0,i.useMutation)("UpdateUser",(e=>(0,n.Rl)(e).then((e=>e.status?(p.Z.success("".concat(e.msg)),(0,n.Lm)().then((e=>{if(e.status){const a=Object.values(null===e||void 0===e?void 0:e.data);return O(a),W(a),e}return!1})).catch((e=>{console.log("error : ",e)})),A(),e):(t({show:!0,res:e}),!1))).catch((e=>{console.log("error : ",e),t({show:!0,res:e})})))),{isDarkTheme:R}=(0,v.F)();return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsxs)("div",{className:"table-responsive",children:[(0,j.jsx)(d.Z.Group,{children:(0,j.jsx)(d.Z.Control,{type:"text",placeholder:"Search",className:"".concat(R&&"dark-form-control"," mb-2 searchbox-style"),onChange:e=>{const a=e.target.value;T(a)},required:!0})}),(0,j.jsx)(o.ZP,{columns:F,data:C,pagination:!1,paginationPerPage:10,highlightOnHover:!0,noHeader:!0,theme:(0,m.gh)()}),a.show&&(0,j.jsx)(b.Z,{res:a.res,setApiResponseModal:t,msg:a.res.msg})]}),_.status&&(0,j.jsxs)(u.Z,{show:!0,animation:!0,size:"md",className:"".concat(R&&"dark-modal"," mt-5"),children:[(0,j.jsx)(u.Z.Header,{className:"text-center py-3",children:(0,j.jsx)(u.Z.Title,{className:"text-center",children:(0,j.jsx)("h5",{className:"mb-0",children:"Edit user"})})}),(0,j.jsxs)(d.Z,{children:[(0,j.jsx)(u.Z.Body,{className:"pt-4 ",children:(0,j.jsxs)("div",{className:"row scroll",children:[(0,j.jsx)("div",{className:"col-md-12",children:(0,j.jsxs)(d.Z.Group,{className:"mb-0",children:[(0,j.jsx)(d.Z.Label,{className:"".concat(R&&"dark-text"),children:"Select Status"}),(0,j.jsx)(l.ZP,{classNamePrefix:"react-select",className:R?"dark-select":"",onChange:e=>{w(e)},options:N,value:f,isSearchable:!0,name:"status",placeholder:"Edit user",styles:(0,h.y0)(m.b4,m.UO),required:!0})]})}),(0,j.jsx)("div",{className:"col-md-12 mt-2",children:(0,j.jsxs)(d.Z.Group,{children:[(0,j.jsx)(d.Z.Label,{className:"".concat(R&&"dark-text"," mb-0"),children:"Referral Percentage :"}),(0,j.jsx)(d.Z.Control,{className:"".concat(R&&"dark-form-control"),type:"number",placeholder:"Enter Percentage %",name:"referral_percentage",value:L,onChange:e=>E(e.target.value),required:!0})]})})]})}),(0,j.jsx)(u.Z.Footer,{className:"py-1 d-flex justify-content-center d-inline",children:(0,j.jsx)("div",{className:"row scroll",children:(0,j.jsxs)("div",{className:"col-md-4 py-2 d-flex",children:[(0,j.jsx)(c.Z,{type:"button",className:"btn btn-md btn-danger mr-3",onClick:A,children:"Cancel"}),(0,j.jsx)(c.Z,{className:"btn btn-md",onClick:e=>{e.preventDefault();const a=Number(L),t={user_id:_.obj.id,status:f.value,referral_percentage:a};U.mutate(t)},children:"Update"})]})})})]})]})]})}n=(f.then?(await f)():f)[0],r()}catch(w){r(w)}}))},4970:(e,a,t)=>{t.d(a,{Gr:()=>d,SX:()=>c,UO:()=>l,b4:()=>n,gh:()=>u,le:()=>o,nZ:()=>i});t(2791);var r=t(3513),s=t(184);const l={option:(e,a)=>({...e,backgroundColor:a.isSelected?"blue":"white",color:a.isSelected?"white":"black"})},n={option:(e,a)=>({...l.option(e,a),"&:hover":{backgroundColor:"lightgray",color:"black"}})};function o(e){let a=e.charAt(0),t="S"===a||"s"===a||"sell"===a||"SELL"===a||"Sell"===a;return"B"===a||"b"===a||"buy"===a||"BUY"===a||"Buy"===a?(0,s.jsx)("label",{className:"badge badge-outline-warning",children:"BUY"}):t?(0,s.jsx)("label",{className:"badge badge-outline-primary",children:"SELL"}):e&&(0,s.jsx)("label",{className:"badge badge-outline-danger",children:e.toUpperCase()})}function i(e){return(e=parseFloat(e||0).toFixed(2))<0?(0,s.jsxs)("span",{className:"text-danger",children:[" ",e," ",(0,s.jsx)("i",{className:"mdi mdi-arrow-down"})]}):0===e||null===e?(e=0,(0,s.jsxs)("span",{children:[" ",e]})):(0,s.jsxs)("span",{className:"text-success",children:[" ",e," ",(0,s.jsx)("i",{className:"mdi mdi-arrow-up"})]})}function c(e,a){return"C"===e.charAt(0)||"c"===e.charAt(0)?(0,s.jsx)("label",{className:"badge badge-outline-success","data-toggle":"tooltip","data-placement":"top",title:a,children:"COMPLETED"}):"R"===e.charAt(0)||"r"===e.charAt(0)?(0,s.jsx)("label",{className:"badge badge-outline-danger","data-toggle":"tooltip","data-placement":"top",title:a,children:"REJECTED"}):(0,s.jsx)("label",{className:"badge badge-outline-info","data-toggle":"tooltip","data-placement":"top",title:a,children:e})}function d(e){return isNaN(e)?"...":e<1e3?e.toString():e>=1e3&&e<1e5?(e/1e3).toFixed(2)+" K":e>=1e5&&e<1e7?(e/1e5).toFixed(2)+" L":e>=1e7?(e/1e7).toFixed(2)+" Cr":void 0}const u=()=>"true"===localStorage.getItem("isDarkTheme")?((0,r.jG)("solarized",{background:{default:"transparent"},action:{button:"rgba(0,0,0,.54)",hover:"rgba(0,0,0,.08)",disabled:"rgba(0,0,0,.12)"}},"dark"),"solarized"):((0,r.jG)("resesolarized",{background:{default:"#fff"},action:{button:"rgba(0,0,0,.54)",hover:"rgba(0,0,0,.08)",disabled:"rgba(0,0,0,.12)"}},"light"),"resesolarized")},7817:(e,a,t)=>{t.a(e,(async(e,r)=>{try{t.r(a),t.d(a,{default:()=>x});var s=t(2791),l=t(4849),n=t(1933),o=t(1025),i=(t(4129),t(7455)),c=t(1335),d=t(7691),u=t(4536),m=t(184),h=e([i,c,d]);function p(){const[e,a]=(0,s.useState)({show:!1,res:{}}),[t,r]=(0,s.useState)(null),[i,h]=((0,n.useQueryClient)(),(0,s.useState)(!1));(0,s.useEffect)((()=>{h(!0),(0,d.Lm)().then((e=>e.status?(r(e),h(!1),e):(h(!1),!1))).catch((e=>{h(!1),console.log("error : ",e)}))}),[]);const{isDarkTheme:b}=(0,u.F)();return(0,m.jsxs)("div",{children:[(0,m.jsx)("div",{className:"page-header",children:(0,m.jsx)("h3",{className:"page-title ".concat(b&&"dark-page-title"),children:"Users"})}),(0,m.jsx)("div",{className:"row",children:(0,m.jsx)("div",{className:"col-lg-12 grid-margin stretch-card",children:(0,m.jsx)("div",{className:"card ".concat(b&&"dark-theme-card"),children:(0,m.jsx)("div",{className:"card-body",children:i?(0,m.jsx)("div",{className:"d-flex justify-content-center align-items-center",style:{height:"100%"},children:(0,m.jsx)(l.Z,{animation:"border",style:{width:"5rem",height:"5rem"}})}):t&&(0,m.jsx)(c.Z,{brokerData:t})})})})}),e.show&&(0,m.jsx)(o.Z,{res:e.res,setApiResponseModal:a,msg:e.res.msg})]})}[i,c,d]=h.then?(await h)():h;const x=(0,i.Z)(p);r()}catch(b){r(b)}}))},4129:()=>{},7326:(e,a,t)=>{function r(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}t.d(a,{Z:()=>r})},1120:(e,a,t)=>{function r(e){return r=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},r(e)}t.d(a,{Z:()=>r})},136:(e,a,t)=>{t.d(a,{Z:()=>s});var r=t(9611);function s(e,a){if("function"!==typeof a&&null!==a)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(a&&a.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),a&&(0,r.Z)(e,a)}},2963:(e,a,t)=>{t.d(a,{Z:()=>l});var r=t(1002),s=t(7326);function l(e,a){if(a&&("object"===(0,r.Z)(a)||"function"===typeof a))return a;if(void 0!==a)throw new TypeError("Derived constructors may only return object or undefined");return(0,s.Z)(e)}}}]);
//# sourceMappingURL=168.c18d678c.chunk.js.map
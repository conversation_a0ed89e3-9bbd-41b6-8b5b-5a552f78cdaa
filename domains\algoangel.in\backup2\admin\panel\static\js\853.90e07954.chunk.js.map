{"version": 3, "file": "static/js/853.90e07954.chunk.js", "mappings": "4RAWA,SAASA,EAAmBC,GAC3B,MAAOC,EAAoBC,IAAyBC,EAAAA,EAAAA,UAAS,IACvDC,GAAaC,EAAAA,EAAAA,UAAQ,IAAM,IAAIC,KAAO,KACrCC,EAAcC,IAAmBL,EAAAA,EAAAA,UAAS,IAAIM,MAmBrD,SAASC,EAAaC,GACrB,IAAIC,EAAe,GACnBD,EAAUE,KAAI,CAACC,EAAKC,IACZH,EAAaI,KAAKC,EAAqBF,EAAOD,MAEtDZ,EAAsBU,EACvB,CAEA,SAASK,EAAqBC,EAAKJ,GAClC,MAAO,CACNC,MAAOG,EAAM,EACbC,KAAML,EAAIM,SACVC,eAAgBP,EAAIO,eACpBC,SAAUR,EAAIQ,SACdC,MAAOT,EAAIS,MACXC,MAAMC,EAAAA,EAAAA,IAAmBX,EAAIY,aAC7BC,WAAYb,EAAIa,WAAWC,MAAM,KAAK,GAAGC,QAAQ,IAAK,KACtDC,IAAKhB,EAAIiB,SACTjB,IAAKA,EAEP,EApCAkB,EAAAA,EAAAA,UAAS,iBAAiB,KACzBC,EAAAA,EAAAA,IAAcjC,EAAMkC,IAAIC,EAAAA,EAAAA,SAAO,IAAI1B,KAAQ,eACzC2B,MAAMC,IACN,MAAMC,EAAUC,OAAOC,OAAOH,EAAII,MAMlC,OALA/B,EAAa4B,GAEbA,EAAQzB,KAAI,CAACC,EAAKC,IACVX,EAAWsC,IAAI3B,EAAOD,KAEvBuB,CAAG,IAEVM,OAAOC,IACPC,QAAQC,IAAI,WAAYF,EAAE,MA0B7B,MAcMG,EAAU,CACf,CACC5B,KAAM,KACN6B,SAAWC,GAAQA,EAAIlC,MACvBmC,UAAU,EACVC,SAAU,OACVC,SAAU,QAEX,CACCjC,KAAM,OACN6B,SAAWC,GAAQA,EAAI9B,KACvB+B,UAAU,EACVG,MAAM,GAEP,CACClC,KAAM,SACN6B,SAAWC,GAAQA,EAAI5B,eACvB6B,UAAU,EACVG,MAAM,GAEP,CACClC,KAAM,WACN6B,SAAWC,GAAQA,EAAI3B,SACvB4B,UAAU,EACVG,MAAM,EACNF,SAAU,QACVC,SAAU,SAUX,CACCjC,KAAM,QACN6B,SAAWC,GAAQA,EAAI1B,MACvB2B,UAAU,EACVG,MAAM,EACNF,SAAU,QACVC,SAAU,SAEX,CACCjC,KAAM,OACN6B,SAAWC,GAAQA,EAAIzB,KACvB0B,UAAU,EACVG,MAAM,EACNC,aA/D6BC,CAACC,EAAGC,IAEX,MAAtBD,EAAE1C,IAAIY,aACgB,MAAtB8B,EAAE1C,IAAIY,aACgB,QAAtB8B,EAAE1C,IAAIY,aACgB,QAAtB8B,EAAE1C,IAAIY,aACgB,QAAtB8B,EAAE1C,IAAIY,YAEC,GAEC,EAsDRyB,SAAU,QACVC,SAAU,SAEX,CACCjC,KAAM,MACN6B,SAAWC,GAAQA,EAAInB,IACvBoB,UAAU,EACVG,MAAM,EACNF,SAAU,QACVC,SAAU,SAEX,CACCjC,KAAM,OACN6B,SAAWC,GAAQA,EAAItB,WACvBuB,UAAU,EACVG,MAAM,GAGP,CAAElC,KAAM,MAAO6B,SAAWC,GAAQA,EAAInC,IAAK4C,MAAM,KAqD5C,YAAEC,IAAgBC,EAAAA,EAAAA,KAExB,OACCC,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,EACCF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,yCAAwCD,SAAA,EACtDE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wBAAuBD,UACrCF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,MAAKD,SAAA,EACnBE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,QAAOD,UACrBE,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAAAH,UACJE,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAACC,UAAU,aAAaJ,UAAS,GAAAK,OAAKV,GAAe,kBAAiB,SAAQI,UAExFE,EAAAA,EAAAA,KAACK,IAAU,CACVC,SAAUhE,EACViE,SAnCiBC,IACzBjE,EAAgBiE,EAAK,EAmCbT,UAAU,eACVU,WAAW,sBAKfT,EAAAA,EAAAA,KAAA,OAAKD,UAAU,mBACfC,EAAAA,EAAAA,KAAA,OAAKD,UAAU,qBAAoBD,UAClCE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CACNnD,KAAK,MACLwC,UAAU,yBACVY,QAzCiBC,MACvB5C,EAAAA,EAAAA,IAAcjC,EAAMkC,IAAIC,EAAAA,EAAAA,SAAO5B,EAAc,eAC3C6B,MAAMC,IACN,MAAMC,EAAUC,OAAOC,OAAOH,EAAII,MAMlC,OALA/B,EAAa4B,GAEbA,EAAQzB,KAAI,CAACC,EAAKC,IACVX,EAAWsC,IAAI3B,EAAOD,KAEvBuB,CAAG,IAEVM,OAAOC,IACPC,QAAQC,IAAI,WAAYF,EAAE,GACzB,EA4B4BmB,SACzB,wBAMJE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,WAAUD,UACxBE,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAAAJ,UACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKY,QAAO,CACZtD,KAAK,OACLuD,YAAY,SACZf,UAAS,GAAAK,OAAKV,GAAe,oBAAmB,yBAChDa,SAxFgB5B,IACrB,IAAIoC,EAAapC,EAAEqC,OAAOC,MACtBC,EAAe,GACnB,IAAK,IAAID,KAAS9E,EAAWoC,SAAU,CAAC,IAAD4C,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GAEvB,QAAdP,EAAAF,EAAM9D,gBAAQ,IAAAgE,GAAdA,EAAgBQ,cAAcC,SAAmB,OAAVb,QAAU,IAAVA,OAAU,EAAVA,EAAYY,gBAC/B,QAD6CP,EACjEH,EAAM7D,sBAAc,IAAAgE,GAApBA,EACGO,cACDC,SAAmB,OAAVb,QAAU,IAAVA,OAAU,EAAVA,EAAYY,gBACT,QADuBN,EACrCJ,EAAM5D,gBAAQ,IAAAgE,GAAdA,EAAgBM,cAAcC,SAAmB,OAAVb,QAAU,IAAVA,OAAU,EAAVA,EAAYY,gBAClC,QADgDL,EACjEL,EAAMxD,mBAAW,IAAA6D,GAAjBA,EAAmBK,cAAcC,SAAmB,OAAVb,QAAU,IAAVA,OAAU,EAAVA,EAAYY,gBACtC,QADoDJ,EACpEN,EAAMvD,kBAAU,IAAA6D,GAEG,QAFHC,EAAhBD,EACG5D,MAAM,KAAK,GACZC,QAAQ,IAAK,YAAI,IAAA4D,GAFnBA,EAGGG,cACDC,SAAmB,OAAVb,QAAU,IAAVA,OAAU,EAAVA,EAAYY,gBACT,QADuBF,EACrCR,EAAMY,gBAAQ,IAAAJ,GAAdA,EAAgBE,cAAcC,SAAmB,OAAVb,QAAU,IAAVA,OAAU,EAAVA,EAAYY,gBACzC,QADuDD,EACjET,EAAMa,YAAI,IAAAJ,GAAVA,EAAYC,cAAcC,SAAmB,OAAVb,QAAU,IAAVA,OAAU,EAAVA,EAAYY,gBAC1C,OAALV,QAAK,IAALA,GAAAA,EAAO3D,MAAMyE,WAAWH,SAASb,KAEjCG,EAAanE,KAAKkE,EAEpB,CACAhF,EACCiF,EAAatE,KAAI,CAACC,EAAKC,IAAUE,EAAqBF,EAAOD,KAC7D,EAgEImF,UAAQ,YAMZhC,EAAAA,EAAAA,KAACiC,EAAAA,GAAS,CACTnD,QAASA,EACTN,KAAMxC,EACNkG,YAAU,EACVC,kBAAmB,GACnBC,SAAU1C,EACV2C,OAAOC,EAAAA,EAAAA,MACPC,kBAAgB,EAChBC,UAAQ,MAIZ,C,4BAEA,U,yMC5OA,SAASC,EAAsB1G,GAC9B,MAAM2G,GAAcC,EAAAA,EAAAA,mBACbC,EAAkBC,IAAuB3G,EAAAA,EAAAA,UAAS,CACxD4G,MAAM,EACN1E,IAAK,CAAC,KAEA2E,EAAWC,IAAgB9G,EAAAA,EAAAA,WAAS,IAE1C+G,EACAC,IACGhH,EAAAA,EAAAA,UAAS,CACZiH,QAAQ,EACRlF,GAAI,GACJmF,KAAM,EACN5C,KAAM,GACN6C,SAAS,KAEHrH,EAAoBC,IAAyBC,EAAAA,EAAAA,UAAS,IACvDC,GAAaC,EAAAA,EAAAA,UAAQ,IAAM,IAAIC,KAAO,IAEtCiH,EAAeA,MACpBC,EAAAA,EAAAA,IAAwBxH,EAAMkC,IAC5BE,MAAMC,IACN,MAAMC,EAAUC,OAAOC,OAAOH,GAM9B,OAWH,SAAsB1B,GACrB,IAAIC,EAAe,GACnBD,EAAUE,KAAI,CAACC,EAAKC,IACZH,EAAaI,KAAKC,EAAqBF,EAAOD,MAEtDZ,EAAsBU,EACvB,CAtBGF,CAAa4B,GAEbA,EAAQzB,KAAI,CAACC,EAAKC,IACVX,EAAWsC,IAAI3B,EAAOD,KAEvBuB,CAAG,IAEVM,OAAOC,IACPC,QAAQC,IAAI,WAAYF,GACxBkE,EAAoB,CAAEC,MAAM,EAAM1E,IAAKO,GAAI,GAC1C,EAmBJ,SAAS3B,EAAqBC,EAAKJ,GAClC,MAAO,CACNC,MAAOG,EAAM,EACbC,KAAML,EAAIK,KACVkG,KAAMvG,EAAIuG,KACVI,wBAAyB3G,EAAI4G,WAAWC,MAAM,EAAG,IACjDC,sBAAuB9G,EAAI+G,YAAYF,MAAM,EAAG,IAChDP,OAAStG,EAAIgH,YAGZ7D,EAAAA,EAAAA,KAAA,SAAOD,UAAU,6BAA4BD,SAAC,eAF9CE,EAAAA,EAAAA,KAAA,SAAOD,UAAU,8BAA6BD,SAAC,aAIhDuD,QAASxG,EAAIwG,SACZrD,EAAAA,EAAAA,KAAA,SAAOD,UAAU,8BAA6BD,SAAC,UAE/CE,EAAAA,EAAAA,KAAA,SAAOD,UAAU,6BAA4BD,SAAC,UAE/CgE,QACC9D,EAAAA,EAAAA,KAAA,OAAKD,UAAU,2BAA0BD,UACxCE,EAAAA,EAAAA,KAAA,UACCzC,KAAK,SACLwC,UAAU,wFACVY,QAASA,IACRuC,EAAuC,CACtCC,QAAQ,EACRlF,GAAIpB,EAAIoB,GACRmF,KAAMvG,EAAIuG,KACV5C,KAAM3D,EAAI+G,YAAYF,MAAM,EAAG,IAC/BL,QAASxG,EAAIwG,UAEdvD,UAEDE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,2CAIhBlD,IAAKA,EAEP,EArDAkB,EAAAA,EAAAA,UAAS,2BAA2B,IAAMuF,MAuD1C,MAiBMS,GAAeC,EAAAA,EAAAA,aAAY,8BAA+BzF,IAC/D0F,EAAAA,EAAAA,IAA2B1F,GACzBJ,MAAMC,GACFA,EAAI+E,QACPD,EAAuC,CACtCC,QAAQ,EACRlF,GAAI,GACJmF,KAAM,EACN5C,KAAM,GACN6C,SAAS,IAEVR,EAAoB,CAAEC,MAAM,EAAM1E,IAAKA,IACvCkF,IACOlF,IAEPyE,EAAoB,CAAEC,MAAM,EAAM1E,IAAKA,KAChC,KAGRM,OAAOC,IACPC,QAAQC,IAAI,WAAYF,GACxBkE,EAAoB,CAAEC,MAAM,EAAM1E,IAAKO,GAAI,MAkB9C,MAAMG,EAAU,CACf,CACC5B,KAAM,KACN6B,SAAWC,GAAQA,EAAIlC,MACvBmC,UAAU,EACVC,SAAU,OACVC,SAAU,QAEX,CACCjC,KAAM,OACN6B,SAAWC,GAAQA,EAAI9B,KACvB+B,UAAU,EACVG,MAAM,EACNF,SAAU,QACVC,SAAU,SAEX,CACCjC,KAAM,OACN6B,SAAWC,GAAQA,EAAIoE,KACvBnE,UAAU,EACVG,MAAM,EACNF,SAAU,QACVC,SAAU,SAEX,CACCjC,KAAM,aACN6B,SAAWC,GAAQA,EAAIwE,wBACvBvE,UAAU,EACVG,MAAM,GAEP,CACClC,KAAM,WACN6B,SAAWC,GAAQA,EAAI2E,sBACvB1E,UAAU,EACVG,MAAM,GAEP,CACClC,KAAM,SACN6B,SAAWC,GAAQA,EAAImE,OACvBlE,UAAU,EACVG,MAAM,EACNC,aAzF4B6E,CAAC3E,EAAGC,KACR,IAArBD,EAAE1C,IAAIgH,aAA4C,IAArBrE,EAAE3C,IAAIgH,WAC/B,GAEC,GAuFT,CACC3G,KAAM,UACN6B,SAAWC,GAAQA,EAAIqE,QACvBpE,UAAU,EACVG,MAAM,EACNC,aAxGgC8E,CAAC5E,EAAGC,KACf,IAAlBD,EAAE1C,IAAIwG,UAAsC,IAAlB7D,EAAE3C,IAAIwG,QAC5B,GAEC,GAsGT,CACCnG,KAAM,SACN6B,SAAWC,GAAQA,EAAI8E,OACvB7E,UAAU,EACVG,MAAM,GAEP,CAAElC,KAAM,MAAO6B,SAAWC,GAAQA,EAAInC,IAAK4C,MAAM,KAsClD2E,EAAAA,EAAAA,YAAU,KACLrB,IACHL,EAAY2B,eAAe,2BAC3BrB,GAAa,GACd,GACE,CAACD,IAEJ,MAAM,YAAErD,IAAgBC,EAAAA,EAAAA,KACxB,OACCK,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAC,UACCF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,wBAAuBD,SAAA,EACrCE,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAAAJ,UACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKY,QAAO,CACZtD,KAAK,OACLuD,YAAY,SACZf,UAAS,GAAAK,OAAKV,GAAe,oBAAmB,yBAEhDa,SApDiB5B,IACrB,IAAIoC,EAAapC,EAAEqC,OAAOC,MACtBC,EAAe,GACnB,IAAK,IAAID,KAAS9E,EAAWoC,SAAU,CAAC,IAAD+F,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACtC,MAAMpB,EAA0C,QAAnBc,EAAGrD,EAAMwC,kBAAU,IAAAa,OAAA,EAAhBA,EAAkBZ,MAAM,EAAG,IACrDC,EAAyC,QAApBY,EAAGtD,EAAM2C,mBAAW,IAAAW,OAAA,EAAjBA,EAAmBb,MAAM,EAAG,IACpDmB,EAAa5D,EAAM4C,WAAwB,UAAX,SAChCiB,EAAe7D,EAAMoC,QAAU,KAAO,OAE1B,QAAjBmB,EAAAvD,EAAM8D,mBAAW,IAAAP,GACJ,QADIC,EAAjBD,EACGzC,kBAAU,IAAA0C,GADbA,EAEG9C,cACDC,SAAmB,OAAVb,QAAU,IAAVA,OAAU,EAAVA,EAAYY,gBACA,OAAvB6B,QAAuB,IAAvBA,GAAAA,EACG7B,cACDC,SAAmB,OAAVb,QAAU,IAAVA,OAAU,EAAVA,EAAYY,gBACF,OAArBgC,QAAqB,IAArBA,GAAAA,EACGhC,cACDC,SAAmB,OAAVb,QAAU,IAAVA,OAAU,EAAVA,EAAYY,gBACd,OAATkD,QAAS,IAATA,GAAAA,EAAWlD,cAAcC,SAAmB,OAAVb,QAAU,IAAVA,OAAU,EAAVA,EAAYY,gBAClC,OAAZmD,QAAY,IAAZA,GAAAA,EAAcnD,cAAcC,SAAmB,OAAVb,QAAU,IAAVA,OAAU,EAAVA,EAAYY,gBACvC,QADqD+C,EAC/DzD,EAAMmC,YAAI,IAAAsB,GACG,QADHC,EAAVD,EACG3C,kBAAU,IAAA4C,GADbA,EAEGhD,cACDC,SAAmB,OAAVb,QAAU,IAAVA,OAAU,EAAVA,EAAYY,gBAClB,OAALV,QAAK,IAALA,GAAa,QAAR2D,EAAL3D,EAAO+D,cAAM,IAAAJ,GAAbA,EAAe7C,WAAWH,SAASb,KAEnCG,EAAanE,KAAKkE,EAEpB,CACAhF,EACCiF,EAAatE,KAAI,CAACC,EAAKC,IAAUE,EAAqBF,EAAOD,KAC7D,EAqBGmF,UAAQ,OAGVhC,EAAAA,EAAAA,KAACiC,EAAAA,GAAS,CACTnD,QAASA,EACTN,KAAMxC,EACNkG,YAAU,EACVC,kBAAmB,GACnBC,SAAU1C,EACV2C,OAAOC,EAAAA,EAAAA,MACPC,kBAAgB,EAChBC,UAAQ,IAERI,EAAiBE,OACjB9C,EAAAA,EAAAA,KAACiF,EAAAA,EAAa,CACb7G,IAAKwE,EAAiBxE,IACtByE,oBAAqBA,EACrBqC,IAAKtC,EAAiBxE,IAAI8G,MAG3BjC,EAAoCE,SACpCnD,EAAAA,EAAAA,KAACmF,EAAAA,EAAK,CAACrC,MAAM,EAAMsC,WAAW,EAAMC,KAAK,KAAKtF,UAAS,GAAAK,OAAKV,GAAe,aAAY,SAAQI,UAC9FF,EAAAA,EAAAA,MAACK,EAAAA,EAAI,CAACqF,SArJX,SAA0CC,GACzCA,EAAMC,iBACN,MAAMC,EAASC,OAAOH,EAAMvE,OAAOoC,KAAKnC,OAClCT,EA1FP,SAA2BmF,GAC1B,MAAOC,EAAMC,EAAOC,GAAOH,EAAahI,MAAM,KAE9C,MADmB,GAAAyC,OAAMyF,EAAK,KAAAzF,OAAI0F,EAAG,KAAA1F,OAAIwF,EAE1C,CAsFcG,CAAkBR,EAAMvE,OAAOR,KAAKS,OACjD,IAAI1C,EAAS,CACZyH,MAAO/C,EAAoChF,GAC3C2F,YAAapD,EACb4C,KAAMqC,EACNpC,QAASkC,EAAMvE,OAAOiF,iBAAiBC,QACvCrC,YAAY,GAGbE,EAAaoC,OAAO5H,EACrB,EAwIsDuB,SAAA,EAChDE,EAAAA,EAAAA,KAACmF,EAAAA,EAAMiB,OAAM,CAACrG,UAAU,mBAAkBD,UACzCE,EAAAA,EAAAA,KAACmF,EAAAA,EAAMkB,MAAK,CAACtG,UAAU,cAAaD,UACnCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,OAAMD,SAAC,6BAGvBF,EAAAA,EAAAA,MAACuF,EAAAA,EAAMmB,KAAI,CAACvG,UAAS,QAAAK,QAAWV,GAAe,UAAWI,SAAA,EACzDF,EAAAA,EAAAA,MAACK,EAAAA,EAAKC,MAAK,CAAAJ,SAAA,EACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKsG,MAAK,CAACxG,UAAS,GAAAK,OAAKV,GAAe,aAAcI,SAAC,cACxDE,EAAAA,EAAAA,KAACC,EAAAA,EAAKY,QAAO,CACZd,UAAS,GAAAK,OAAKV,GAAe,qBAC7BnC,KAAK,SACLL,KAAK,OACLsJ,aAAcvD,EAAoCG,KAClD7C,SAAW5B,GAAMA,EAAEqC,OAAOC,MAC1Be,UAAQ,QAGVpC,EAAAA,EAAAA,MAACK,EAAAA,EAAKC,MAAK,CAAAJ,SAAA,EACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKsG,MAAK,CAACxG,UAAS,GAAAK,OAAKV,GAAe,aAAcI,SAAC,gCACxDE,EAAAA,EAAAA,KAACC,EAAAA,EAAKY,QAAO,CACZd,UAAS,GAAAK,OAAKV,GAAe,qBAC7BnC,KAAK,OACLL,KAAK,OACLsJ,aAAcvD,EAAoCzC,KAClDD,SAAW5B,GAAMA,EAAEqC,OAAOC,MAC1Be,UAAQ,QAGVpC,EAAAA,EAAAA,MAACK,EAAAA,EAAKC,MAAK,CAACH,UAAS,4BAA8BD,SAAA,EAClDE,EAAAA,EAAAA,KAACC,EAAAA,EAAKwG,MAAK,CACV1G,UAAU,6BACVxC,KAAK,SACLU,GAAG,mBACHf,KAAK,mBACLwJ,eAAgBzD,EAAoCI,QACpD9C,SAAW5B,GAAMA,EAAEqC,OAAOC,SAE3BjB,EAAAA,EAAAA,KAAA,SAAO2G,QAAQ,mBAAmB5G,UAAS,GAAAK,OAAKV,GAAe,YAAW,QAAOI,SAAC,0BAKpFF,EAAAA,EAAAA,MAACuF,EAAAA,EAAMyB,OAAM,CAAC7G,UAAU,qCAAoCD,SAAA,EAC3DE,EAAAA,EAAAA,KAAA,OAAAF,UACCE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CACNmG,QAAQ,iBACRlG,QAASA,IACRuC,EAAuC,CACtCC,QAAQ,EACRlF,GAAI,GACJmF,KAAM,EACN5C,KAAM,GACN6C,SAAS,IAEVvD,SACD,UAIFE,EAAAA,EAAAA,KAAA,OAAAF,UACCE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CAACnD,KAAK,SAASwC,UAAU,YAAWD,SAAC,sBAWrD,C,4BAEA,U,2QC7VA,SAASgH,EAAmB/K,GAAQ,IAADgL,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAClC,MAAOzE,EAAkBC,IAAuB3G,EAAAA,EAAAA,UAAS,CACxD4G,MAAM,EACN1E,IAAK,CAAC,KAEAkJ,EAAiBC,IAAsBrL,EAAAA,EAAAA,WAAS,IAChD6G,EAAWC,IAAgB9G,EAAAA,EAAAA,WAAS,IACpCsL,EAAsBC,IAA2BvL,EAAAA,EAAAA,WAAS,IAC1DwL,EAAYC,IAAiBzL,EAAAA,EAAAA,UAAS,OAEtC0L,EAAYC,KADClF,EAAAA,EAAAA,mBACczG,EAAAA,EAAAA,WAAS,IAErC4L,EAAoB,CACzBC,SAAU,GACVC,UAAW,GACXC,MAAO,KAEDC,EAAYC,IAAiBjM,EAAAA,EAAAA,UAAS4L,IACtCM,EAASC,IAAcnM,EAAAA,EAAAA,UAAS,KAChCoM,EAA4BC,IAClCrM,EAAAA,EAAAA,UAAS,OACHsM,EAAuBC,IAA4BvM,EAAAA,EAAAA,UAAS,MAO7DwM,EAAgBnD,IACrB,MAAM,KAAErI,EAAI,MAAE+D,GAAUsE,EAAMvE,OAC9BmH,GAAeQ,IAAU,IAAWA,EAAY,CAACzL,GAAO+D,KAAS,GAGlEmD,EAAAA,EAAAA,YAAU,KACT+D,EAAcL,EAAkB,GAC9B,CAACQ,IAEJ,MA4CQ9J,KAAMoK,IAAyB7K,EAAAA,EAAAA,UAAS,uBAAuB,KACtE8K,EAAAA,EAAAA,MACE1K,MAAMC,IACN,GAAIA,EAAI+E,OAAQ,CACf,IAAI2F,EAAc,GAClB,IAAK,IAAIC,KAAO3K,EAAII,KACnBsK,EAAY/L,KAAK,CAAEiM,MAAOD,EAAK9H,MAAO8H,IAGvC,OADAV,EAAWS,GACJ1K,CACR,CACC,OAAO,CACR,IAEAM,OAAOC,IACPC,QAAQC,IAAI,WAAYF,EAAE,MAKvBsK,GAAoBjF,EAAAA,EAAAA,aAAY,aAAczF,IACnD2K,EAAAA,EAAAA,IAAU3K,GACRJ,MAAMC,GACFA,EAAI+E,QACPN,EAAoB,CAAEC,MAAM,EAAM1E,IAAKA,IACvC4E,GAAa,GACbyE,GAAyBD,GACzBe,EAA8B,MAC9BJ,EAAcL,GACd/L,EAAMoN,uBACC/K,IAEPyE,EAAoB,CAAEC,MAAM,EAAM1E,IAAKA,KAChC,KAGRM,OAAOC,IACPC,QAAQC,IAAI,WAAYF,GACxBkE,EAAoB,CAAEC,MAAM,EAAM1E,IAAKO,GAAI,MAI9C,SAASyK,EAAmBC,EAAKC,GAGhC,OAFkB,IAAIC,gBAAgBF,EAAIG,UAAUH,EAAII,QAAQ,OACrCC,IAAIJ,EAEhC,CA8HA,MAAOK,EAAUC,IAAe1N,EAAAA,EAAAA,UAAS,CAAE4G,MAAM,EAAO+G,UAAW,CAAC,IAqBpE,MAeMC,GAAqB9F,EAAAA,EAAAA,aAAY,gBAAgB,KACtD+F,EAAAA,EAAAA,IAAarE,OAAO3J,EAAMiO,SAASC,YAAYhM,KAC7CE,MAAMC,GACFA,EAAI+E,QACPpH,EAAMiO,SAASC,aAAc,EAC7B1C,GAAmB,GACnB1E,EAAoB,CAAEC,MAAM,EAAM1E,IAAKA,IAChCA,IAEPyE,EAAoB,CAAEC,MAAM,EAAM1E,IAAKA,KAChC,KAGRM,OAAOC,IACPC,QAAQC,IAAI,WAAYF,GACxBkE,EAAoB,CAAEC,MAAM,EAAM1E,IAAKO,GAAI,MAIxCuL,GAAkBlG,EAAAA,EAAAA,aAAY,2BAA4BzF,IAC/D4L,EAAAA,EAAAA,IAAwB5L,GACtBJ,MAAMC,GACFA,EAAI+E,QACPN,EAAoB,CAAEC,MAAM,EAAM1E,IAAKA,IAChCA,IAEPyE,EAAoB,CAAEC,MAAM,EAAM1E,IAAKA,KAChC,KAGRM,OAAOC,IACPC,QAAQC,IAAI,WAAYF,GACxBkE,EAAoB,CAAEC,MAAM,EAAM1E,IAAKO,GAAI,OAaxC,YAAEe,IAAgBC,EAAAA,EAAAA,KACxB,OACCC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACiC,IAA/B/D,EAAMiO,SAASC,aACfjK,EAAAA,EAAAA,KAAA,OAAKD,UAAU,MAAKD,UACnBE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,0CAAyCD,UACvDE,EAAAA,EAAAA,KAAA,OAAKD,UAAS,QAAAK,OAAUV,GAAe,mBAAoBI,UAC1DE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWD,UACzBE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gCAA+BD,UAC7CF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,MAAKD,SAAA,EACnBF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,gBAAeD,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,MAAIG,UAAU,kBAAiBD,SAAA,CACH,QADGiH,EAC7BhL,EAAMiO,SAASC,mBAAW,IAAAlD,OAAA,EAA1BA,EAA4BgB,SAAS,KAAG,IACd,QADiBf,EAC3CjL,EAAMiO,SAASC,mBAAW,IAAAjD,OAAA,EAA1BA,EAA4BoD,SAAS,IAAE,IACb,QADgBnD,EAC1ClL,EAAMiO,SAASC,mBAAW,IAAAhD,GAAa,QAAbC,EAA1BD,EAA4BoD,mBAAW,IAAAnD,OAAb,EAA1BA,EAAyCoD,kBAE3CtK,EAAAA,EAAAA,KAAA,KAAAF,SAAkB,QAAlBqH,EAAIpL,EAAMiO,gBAAQ,IAAA7C,OAAA,EAAdA,EAAgBc,YAErBjI,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gBAAeD,SACF,QAA1BsH,EAAArL,EAAMiO,SAASC,mBAAW,IAAA7C,GAA1BA,EAA4BmD,aAC5BvK,EAAAA,EAAAA,KAAA,UACCwK,UAAQ,EACRjN,KAAK,SACLwC,UAAU,wCAAuCD,SACjD,eAIDF,EAAAA,EAAAA,MAAA,UACC4K,UAAQ,EACRjN,KAAK,SACLwC,UAAU,uCAAsCD,SAAA,CAE/C,IAAI,eACQ,UAIhBE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gBAAeD,UAC7BF,EAAAA,EAAAA,MAAA,UACCrC,KAAK,SACLoD,QAASA,IAAM4G,GAAmB,GAClCxH,UAAU,+BAA8BD,SAAA,EAExCE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,mBAAqB,cAInChE,EAAMiO,SAASC,cACfjK,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gBAAeD,UAC7BF,EAAAA,EAAAA,MAACK,EAAAA,EAAKC,MAAK,CAACH,UAAS,GAAAK,OAAKV,GAAe,YAAW,mCAAkCI,SAAA,EACrFE,EAAAA,EAAAA,KAACC,EAAAA,EAAKwG,MAAK,CACV1G,UAAU,6BACVxC,KAAK,SACLU,GAAG,iBACHf,KAAK,iBACLwJ,eAC2B,QADbW,EACbtL,EAAMiO,SAASC,mBAAW,IAAA5C,OAAA,EAA1BA,EAA4BoD,aAE7BlK,SApEqBmK,UACjC,MAAM,QAAExE,GAAYX,EAAMvE,OAE1BkJ,EAAgB/D,OAAO,CACtBwE,QAAS5O,EAAM4O,QACfC,SAAU1E,GACT,KAiESlG,EAAAA,EAAAA,KAAA,SAAO2G,QAAQ,iBAAiB5G,UAAS,GAAAK,OAAKV,GAAe,YAAW,QAAOI,SAAC,+BAazFE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,MAAKD,UACnBE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,qCAAoCD,UAClDE,EAAAA,EAAAA,KAAA,OAAKD,UAAS,QAAAK,OAAUV,GAAe,mBAAoBI,UAC1DF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,YAAWD,SAAA,EACzBF,EAAAA,EAAAA,MAAA,OACCG,UAAU,mEACVY,QAASA,IAAM8G,GAAyBD,GAAsB1H,SAAA,EAE9DE,EAAAA,EAAAA,KAAA,MAAID,UAAS,cAAAK,OAAgBV,GAAe,kBAAiB,SAAQI,SAAC,6BACtEE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,8CAEbyH,GACA5H,EAAAA,EAAAA,MAACK,EAAAA,EAAI,CAACF,UAAU,OAAOuF,SAnJTC,IACrB,GAAwC,SAApC+C,EAA2BrH,OAlJhC,WAEC,MAAMzC,EAAO,CACZqM,UAFa3C,EAAW4C,QAGxBC,aAAcC,EAAAA,GACdC,cAAe,OACfC,MAAO,UAIFC,EAAY,IAAI5B,gBAAgB/K,GAAMuD,WACtCsH,EAAG,GAAAjJ,OAFM,+CAEM,KAAAA,OAAI+K,GACzBvM,QAAQC,IAAIwK,GAEZ,MAAM+B,EAAQC,OAAOC,KAAKjC,EAAK,SAAU,4BAGnCkC,EAAaC,aAAY,KAC9B,GAAIJ,EAAMK,OACTC,cAAcH,QAEd,IACC,MAAMlC,EAAM+B,EAAMO,SAASC,KAE3B,GADAhN,QAAQC,IAAIwK,GACRA,EAAIzH,SAAS,aAAc,CAC9B,IAAIiK,EAAYzC,EAAmBC,EAAK,aACpCyC,EAAW5D,EACf4D,EAASC,OAASF,EAClBC,EAASE,eAAgB,EACzBF,EAASzB,YAAc/B,EAA2BrH,MAClD6K,EAASnB,QAAU5O,EAAM4O,QACzB1B,EAAkB9C,OAAO2F,GACzBJ,cAAcH,GACdH,EAAMa,OACP,CACD,CAAE,MAAOC,GAER,CAEF,GACE,IACJ,CA0GEC,QACM,GAAwC,iBAApC7D,EAA2BrH,OAzGvC,WACC,IAAI6J,EAAU5C,EAAW4C,QACzB,MAMMK,EAAY,IAAI5B,gBANT,CACZ6C,EAAG,EACHtB,QAASA,IAIkC/I,WACtCsH,EAAG,GAAAjJ,OAFM,yCAEM,KAAAA,OAAI+K,GACzBvM,QAAQC,IAAIwK,GAEZ,MAAM+B,EAAQC,OAAOC,KAAKjC,EAAK,SAAU,4BAGnCkC,EAAaC,aAAY,KAC9B,GAAIJ,EAAMK,OACTC,cAAcH,QAEd,IACC,MAAMlC,EAAM+B,EAAMO,SAASC,KAE3B,GADAhN,QAAQC,IAAIwK,GACRA,EAAIzH,SAAS,iBAAkB,CAClC,IAAIiK,EAAYzC,EAAmBC,EAAK,iBACpCyC,EAAW5D,EACf4D,EAASC,OAASF,EAClBC,EAASE,eAAgB,EACzBF,EAASzB,YAAc/B,EAA2BrH,MAClD6K,EAASnB,QAAU5O,EAAM4O,QACzB1B,EAAkB9C,OAAO2F,GACzBJ,cAAcH,GACdH,EAAMa,OACP,CACD,CAAE,MAAOC,GAER,CAEF,GACE,IACJ,CAmEEG,QACM,GAAwC,aAApC/D,EAA2BrH,OAlEvC,WACC,IAAI6J,EAAU5C,EAAW4C,QACzB,MAKMK,EAAY,IAAI5B,gBALT,CACZ+C,QAASxB,IAIkC/I,WACtCsH,EAAG,GAAAjJ,OAFM,4BAEM,KAAAA,OAAI+K,GACzBvM,QAAQC,IAAIwK,GAEZ,MAAM+B,EAAQC,OAAOC,KAAKjC,EAAK,SAAU,4BAGnCkC,EAAaC,aAAY,KAC9B,GAAIJ,EAAMK,OACTC,cAAcH,QAEd,IACC,MAAMlC,EAAM+B,EAAMO,SAASC,KAE3B,GADAhN,QAAQC,IAAIwK,GACRA,EAAIzH,SAAS,QAAS,CACzB,IAAIiK,EAAYzC,EAAmBC,EAAK,QACpCyC,EAAW5D,EACf4D,EAASC,OAASF,EAClBC,EAASE,eAAgB,EACzBF,EAASzB,YAAc/B,EAA2BrH,MAClD6K,EAASnB,QAAU5O,EAAM4O,QACzB1B,EAAkB9C,OAAO2F,GACzBJ,cAAcH,GACdH,EAAMa,OACP,CACD,CAAE,MAAOC,GAER,CAEF,GACE,IACJ,CA6BEK,OACM,CACN,IAAIT,EAAW5D,EACf4D,EAASzB,YAAc/B,EAA2BrH,MAClD6K,EAASnB,QAAUjF,OAAO3J,EAAM4O,SAChC1B,EAAkB9C,OAAO+B,EAC1B,CACA3C,EAAMC,gBAAgB,EAsI+B1F,SAAA,EAC7CF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,aAAYD,SAAA,EAC1BE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,WAAUD,UACxBF,EAAAA,EAAAA,MAACK,EAAAA,EAAKC,MAAK,CAACH,UAAU,OAAMD,SAAA,EAC3BE,EAAAA,EAAAA,KAACC,EAAAA,EAAKsG,MAAK,CAACxG,UAAS,GAAAK,OAAKV,EAAc,YAAc,cAAeI,SAAC,mBACtEE,EAAAA,EAAAA,KAACwM,EAAAA,GACA,CACAC,gBAAgB,eAChB1M,UAAWL,EAAc,cAAgB,GACzCuB,MAAOqH,EACP/H,SAxZ8BmM,IAC1CnE,EAA8BmE,GAC9BjE,EAAyBiE,EAAezL,MAAM,EAuZlCmH,QAASA,EACTuE,cAAc,EACd7L,YAAY,gBACZ8L,QAAQC,EAAAA,EAAAA,IACPC,EAAAA,GACAC,EAAAA,IAED/K,UAAQ,UAIXhC,EAAAA,EAAAA,KAAA,OAAKD,UAAU,WAAUD,UACxBF,EAAAA,EAAAA,MAACK,EAAAA,EAAKC,MAAK,CAAAJ,SAAA,EACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKsG,MAAK,CAACxG,UAAS,GAAAK,OAAKV,EAAc,YAAc,cAAeI,SAAC,cAGtEE,EAAAA,EAAAA,KAACC,EAAAA,EAAKY,QAAO,CACZd,UAAS,GAAAK,OAAKV,GAAe,qBAC7BnC,KAAK,OACLuD,YAAY,iBACZ5D,KAAK,WACL+D,MAAOiH,EAAWH,SAClBxH,SAAUmI,EACV1G,UAAQ,UAIXhC,EAAAA,EAAAA,KAAA,OAAKD,UAAU,WAAUD,UACxBF,EAAAA,EAAAA,MAACK,EAAAA,EAAKC,MAAK,CAAAJ,SAAA,EACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKsG,MAAK,CAACxG,UAAS,GAAAK,OAAKV,EAAc,YAAc,cAAeI,SAAC,gBAGtEE,EAAAA,EAAAA,KAACC,EAAAA,EAAKY,QAAO,CACZd,UAAS,GAAAK,OAAKV,GAAe,qBAC7BnC,KAAK,MACLyP,QAAQ,YACRC,MAAM,kCACNnM,YAAY,mBACZ5D,KAAK,YACLgQ,UAAU,KACVC,UAAU,KACVlM,MAAOiH,EAAWF,UAClBzH,SAAUmI,EACV1G,UAAQ,UAIXhC,EAAAA,EAAAA,KAAA,OAAKD,UAAU,WAAUD,UACxBF,EAAAA,EAAAA,MAACK,EAAAA,EAAKC,MAAK,CAAAJ,SAAA,EACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKsG,MAAK,CAACxG,UAAS,GAAAK,OAAKV,EAAc,YAAc,cAAeI,SAAC,WACtEE,EAAAA,EAAAA,KAACC,EAAAA,EAAKY,QAAO,CACZd,UAAS,GAAAK,OAAKV,GAAe,qBAC7BnC,KAAK,QACLuD,YAAY,cACZ5D,KAAK,QACL+D,MAAOiH,EAAWD,MAClB1H,SAAUmI,EACV1G,UAAQ,aAKZhC,EAAAA,EAAAA,KAAA,OAAK/B,GAAG,uBAAsB6B,SAxbZsN,MAC1B,MAAMC,EAAS7E,GAAgD,KAC/D,GAAI6E,EAAQ,CACX,MAAMC,EAAe1E,EAAqBpK,KAAK6O,GACzCE,EAAejP,OAAOkP,QAAQF,GAClCG,QAAOC,IAAA,IAAEC,EAAGC,GAAMF,EAAA,OAAKE,EAAM9K,IAAI,IACjClG,KAAIiR,IAAA,IAAEC,EAAWF,GAAMC,EAAA,MAvBEE,EAACH,EAAOE,KAEnClO,EAAAA,EAAAA,MAACK,EAAAA,EAAKC,MAAK,CAAAJ,SAAA,EACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKsG,MAAK,CAACxG,UAAS,GAAAK,OAAKV,EAAc,YAAc,cAAeI,SAAE8N,EAAM9M,eAC7Ed,EAAAA,EAAAA,KAACC,EAAAA,EAAKY,QAAO,CACZd,UAAS,GAAAK,OAAKV,GAAe,qBAC7BnC,KAAK,OACLuD,YAAa8M,EAAM9M,YACnB5D,KAAM4Q,EACN7M,MAAOiH,EAAW4F,GAClBvN,SAAUmI,EACV1G,UAAQ,OAYoB+L,CAAqBH,EAAOE,EAAU,IAE9DE,EAAO,GACb,IAAK,IAAIC,EAAI,EAAGA,EAAIV,EAAaW,OAAQD,GAAK,EAAG,CAChD,MAAME,EAAYZ,EAAa7J,MAAMuK,EAAGA,EAAI,GAC5CD,EAAKjR,MACJiD,EAAAA,EAAAA,KAAA,OAAaD,UAAU,MAAKD,SAC1BqO,EAAUvR,KAAI,CAACgR,EAAO9Q,KACtBkD,EAAAA,EAAAA,KAAA,OAAiBD,UAAU,4BAA2BD,SACpD8N,GADQ9Q,MAFFmR,GAQZ,CACA,OAAOD,CACR,CACA,OAAO,IAAI,EAia6BZ,MAChCpN,EAAAA,EAAAA,KAAA,OAAKD,UAAU,aAAYD,UAC1BF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,gBAAeD,SAAA,EAC7BE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CACNnD,KAAK,SACLwC,UAAU,6BACVY,QAASA,KACR4H,EAA8B,MAC9BE,EAAyB,KAAK,EAC7B3I,SACF,WAGDE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CAACnD,KAAK,SAASwC,UAAU,aAAYD,SAAC,gBAM7C,eAMTE,EAAAA,EAAAA,KAACoO,EAAAA,EAA2B,CAC3BtL,KAAM6G,EAAS7G,KACf+G,UAAWF,EAASE,UACpBwE,iBAAkBpF,IAElB3B,IACA1H,EAAAA,EAAAA,MAACuF,EAAAA,EAAK,CAACrC,MAAM,EAAMsC,WAAW,EAAMC,KAAK,KAAKtF,UAAS,GAAAK,OAAKV,GAAe,aAAY,SAAQI,SAAA,EAC9FE,EAAAA,EAAAA,KAACmF,EAAAA,EAAMiB,OAAM,CAACrG,UAAU,mBAAkBD,UACzCE,EAAAA,EAAAA,KAACmF,EAAAA,EAAMkB,MAAK,CAACtG,UAAU,cAAaD,UACnCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,OAAMD,SAAC,+BAGvBE,EAAAA,EAAAA,KAACmF,EAAAA,EAAMmB,KAAI,CAACvG,UAAS,QAAAK,OAAUV,GAAe,UAAWI,SAAC,0CAG1DF,EAAAA,EAAAA,MAACuF,EAAAA,EAAMyB,OAAM,CAAC7G,UAAU,qCAAoCD,SAAA,EAC3DE,EAAAA,EAAAA,KAAA,OAAAF,UACCE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CACNmG,QAAQ,iBACRlG,QAASA,IAAM4G,GAAmB,GAAOzH,SACzC,UAIFE,EAAAA,EAAAA,KAAA,OAAAF,UACCE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CACNmG,QAAQ,kBACR9G,UAAU,YACVY,QAASA,IAAMmJ,EAAmB3D,SAASrG,SAC3C,gBAOJ8C,EAAiBE,OACjB9C,EAAAA,EAAAA,KAACiF,EAAAA,EAAa,CACb7G,IAAKwE,EAAiBxE,IACtByE,oBAAqBA,EACrBqC,IAAKtC,EAAiBxE,IAAI8G,QAK/B,E,8BAEA,SAAeoJ,EAAAA,EAAAA,GAASxH,G,2KCnkBxB,SAASyH,EAAYxS,GACpB,MAAM2G,GAAcC,EAAAA,EAAAA,mBACb3G,EAAoBC,IAAyBC,EAAAA,EAAAA,UAAS,IACvDC,GAAaC,EAAAA,EAAAA,UAAQ,IAAM,IAAIC,KAAO,IAEtCmS,EAAuBA,MAC5BC,EAAAA,EAAAA,IAAmB1S,EAAMkC,IACvBE,MAAMC,IACN,GAAIA,EAAI+E,OAAQ,CACf,MAAM9E,EAAUC,OAAOC,OAAOH,EAAII,MAMlC,OAcJ,SAAsB9B,GACrB,IAAIC,EAAe,GACnBD,EAAUE,KAAI,CAACC,EAAKC,IACZH,EAAaI,KAAKC,EAAqBF,EAAOD,MAEtDZ,EAAsBU,EACvB,CAzBIF,CAAa4B,GAEbA,EAAQzB,KAAI,CAACC,EAAKC,IACVX,EAAWsC,IAAI3B,EAAOD,KAEvBuB,CACR,CACC,OAAO,CACR,IAEAM,OAAOC,IACPC,QAAQC,IAAI,WAAYF,EAAE,GACzB,EAeJ,SAAS3B,EAAqBC,EAAKJ,GAClC,MAAO,CACNC,MAAOG,EAAM,EACb+H,OAAQnI,EAAImI,OACZ0J,YAAa7R,EAAI6R,YACjBC,SAAU9R,EAAI8R,SAAShR,MAAM,KAAK,GAAGC,QAAQ,IAAK,KAClDf,IAAKA,EAEP,EApBAuH,EAAAA,EAAAA,YAAU,KACToK,GAAsB,GACpB,KAoFHpK,EAAAA,EAAAA,YAAU,KACLrI,EAAMgH,YACTL,EAAY2B,eAAe,oBAC3BtI,EAAMiH,cAAa,GACpB,GACE,CAACjH,EAAMgH,YAEV,MAAM,YAAErD,IAAgBC,EAAAA,EAAAA,KACxB,OACCK,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAC,UACCF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,wBAAuBD,SAAA,EACrCE,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAAAJ,UACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKY,QAAO,CACZd,UAAS,GAAAK,OAAKV,GAAe,oBAAmB,yBAChDnC,KAAK,OACLuD,YAAY,SACZP,SAjDiB5B,IACrB,IAAIoC,EAAapC,EAAEqC,OAAOC,MACtBC,EAAe,GAEnB,GACuB,KAAtBH,EAAW6N,QACI,OAAf7N,QACe8N,IAAf9N,EAGAG,EAAe4N,MAAMC,KAAK5S,EAAWoC,eAGrC,IAAK,IAAI0C,KAAS9E,EAAWoC,SAAU,CAAC,IAADyQ,EAAAC,EAAAC,GAEpB,QAAjBF,EAAA/N,EAAMyN,mBAAW,IAAAM,GAAjBA,EAAmBrN,cAAcC,SAASb,EAAWY,gBACvC,QADqDsN,EACnEhO,EAAM0N,gBAAQ,IAAAM,GAEK,QAFLC,EAAdD,EACGtR,MAAM,KAAK,GACZC,QAAQ,IAAK,YAAI,IAAAsR,GAFnBA,EAGGvN,cACDC,SAASb,EAAWY,gBACjB,OAALV,QAAK,IAALA,GAAAA,EAAO+D,OAAOjD,WAAWH,SAASb,KAElCG,EAAanE,KAAKkE,EAEpB,CAGDhF,EACCiF,EAAatE,KAAI,CAACC,EAAKC,IAAUE,EAAqBF,EAAOD,KAC7D,EAoBGmF,UAAQ,OAGVhC,EAAAA,EAAAA,KAACiC,EAAAA,GAAS,CACTnD,QArFY,CACf,CACC5B,KAAM,KACN6B,SAAWC,GAAQA,EAAIlC,MACvBmC,UAAU,EACVC,SAAU,OACVC,SAAU,QAEX,CACCjC,KAAM,SACN6B,SAAWC,GAAQA,EAAIgG,OACvB/F,UAAU,EACVG,MAAM,EACNF,SAAU,QACVC,SAAU,SAEX,CACCjC,KAAM,cACN6B,SAAWC,GAAQA,EAAI0P,YACvBzP,UAAU,EACVG,MAAM,GAEP,CACClC,KAAM,OACN6B,SAAWC,GAAQA,EAAI2P,SACvB1P,UAAU,EACVG,MAAM,GAEP,CAAElC,KAAM,MAAO6B,SAAWC,GAAQA,EAAInC,IAAK4C,MAAM,IA0D9CjB,KAAMxC,EACNkG,YAAU,EACVC,kBAAmB,GACnBC,SAAU1C,EACV2C,OAAOC,EAAAA,EAAAA,MACPC,kBAAgB,EAChBC,UAAQ,QAKb,C,4BAEA,U,0TCnIA,SAAS2M,EAASpT,GAAQ,IAAD2R,EAAA0B,EACxB,MAAM,QAAEzE,IAAY0E,EAAAA,EAAAA,OACbC,EAAoBC,IAAyBrT,EAAAA,EAAAA,WAAS,IACtDsT,EAAwBC,IAA6BvT,EAAAA,EAAAA,WAAS,IAC9D6G,EAAWC,IAAgB9G,EAAAA,EAAAA,WAAS,IACpC8N,EAAU0F,IAAexT,EAAAA,EAAAA,UAAS,CAAC,IACnC0G,EAAkBC,IAAuB3G,EAAAA,EAAAA,UAAS,CACxD4G,MAAM,EACN1E,IAAK,CAAC,KAGAuR,EAAaC,IAAkB1T,EAAAA,EAAAA,UAAS,aAKxC2T,EAAcC,IAAmB5T,EAAAA,EAAAA,WAAS,GAM3C6T,GAAW/L,EAAAA,EAAAA,aAAY,aAAczF,IAC1CyR,EAAAA,EAAAA,IAAUzR,GACRJ,MAAMC,GACFA,EAAI+E,QACPH,GAAa,GACbuM,GAAsB,GACtB1M,EAAoB,CAAEC,MAAM,EAAM1E,IAAKA,IAChCA,IAEPyE,EAAoB,CAAEC,MAAM,EAAM1E,IAAKA,KAChC,KAGRM,OAAOC,IACPC,QAAQC,IAAI,WAAYF,GACxBkE,EAAoB,CAAEC,MAAM,EAAM1E,IAAKO,GAAI,MAiB9C,MAAMsR,GAAwBjM,EAAAA,EAAAA,aAAY,qBAAsBzF,IAC/D2R,EAAAA,EAAAA,IAAkB3R,GAChBJ,MAAMC,GACFA,EAAI+E,QACP2M,GAAgB,GAChBL,GAA0B,GAC1B5M,EAAoB,CAAEC,MAAM,EAAM1E,IAAKA,IAChCA,IAEPyE,EAAoB,CAAEC,MAAM,EAAM1E,IAAKA,KAChC,KAGRM,OAAOC,IACPC,QAAQC,IAAI,WAAYF,GACxBkE,EAAoB,CAAEC,MAAM,EAAM1E,IAAKO,GAAI,OA4B9CyF,EAAAA,EAAAA,YAAU,KACLuG,IACHwF,EAAAA,EAAAA,IAAexF,GACbxM,MAAMC,KACFA,EAAI+E,SACPuM,EAAYtR,EAAII,MAETJ,KAKRM,OAAOC,IACPC,QAAQC,IAAI,WAAYF,EAAE,GAE7B,GACE,IAEH,MAAM,YAAEe,IAAgBC,EAAAA,EAAAA,KACxB,OACCC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACCE,EAAAA,EAAAA,KAAA,OAAKD,UAAS,QAAAK,OAAUV,GAAe,kBAAiB,SAAQI,UAC/DE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,iBAAgBD,UAC9BF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,iDAAgDD,SAAA,EAC9DF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,4BAA2BD,SAAA,EACzCF,EAAAA,EAAAA,MAAA,UACCG,UAAU,sBACVY,QAASA,IAAM0K,OAAOY,QAAQnM,SAAA,CAE7B,KACDE,EAAAA,EAAAA,KAAA,KACCD,UAAU,8BACVqQ,MAAO,CAAEC,SAAU,UACd,QAEPrQ,EAAAA,EAAAA,KAAA,UACCD,UAAU,6CACVY,QAASA,IAAM4O,GAAsB,GAAMzP,SAC3C,gBAGDE,EAAAA,EAAAA,KAAA,UACCD,UAAU,6CACVY,QAASA,IAAM8O,GAA0B,GAAM3P,SAC/C,uBAIFE,EAAAA,EAAAA,KAAA,MAAID,UAAU,0BAAyBD,SACf,QADe4N,EACrB,QADqB0B,EAC5B,OAARpF,QAAQ,IAARA,OAAQ,EAARA,EAAU/B,aAAK,IAAAmH,EAAAA,EAAI,UAAE,IAAA1B,OAAA,EAAtBA,EAAyB/L,uBAK7BqI,IACAhK,EAAAA,EAAAA,KAAC8G,EAAAA,EAAkB,CAClBkD,SAAUA,EACVW,QAASA,EACTxB,qBA1EyBA,MAC5BgH,EAAAA,EAAAA,IAAexF,GACbxM,MAAMC,KACFA,EAAI+E,SACPuM,EAAYtR,EAAII,MAETJ,KAKRM,OAAOC,IACPC,QAAQC,IAAI,WAAYF,EAAE,GACzB,KAiEFqB,EAAAA,EAAAA,KAAA,OAAKD,UAAU,qBAAoBD,UAClCE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,qCAAoCD,UAClDE,EAAAA,EAAAA,KAAA,OAAKD,UAAS,QAAAK,OAAUV,GAAe,mBAAoBI,UAC1DE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWD,UACzBF,EAAAA,EAAAA,MAAC0Q,EAAAA,EAAI,CACJC,iBAAiB,WACjBtS,GAAG,mBACH8B,UAAS,GAAAK,OAAKV,GAAe,gBAAe,SAC5C8Q,MAAI,EACJC,SAAO,EACPC,UAAWf,EACXgB,SA9JkBC,IACxBhB,EAAegB,EAAS,EA6JQ9Q,SAAA,EAE1BE,EAAAA,EAAAA,KAAC6Q,EAAAA,EAAG,CAACD,SAAS,WAAW3D,MAAM,WAAW6D,aAAY,GAAA1Q,OAAKV,GAAe,iBAAkBI,SAC1F6K,IAAW3K,EAAAA,EAAAA,KAACyC,EAAAA,EAAqB,CAACxE,GAAI0M,OAExC3K,EAAAA,EAAAA,KAAC6Q,EAAAA,EAAG,CAACD,SAAS,SAAS3D,MAAM,SAAS6D,aAAY,GAAA1Q,OAAKV,GAAe,iBAAkBI,SACtF6K,IAAW3K,EAAAA,EAAAA,KAAClE,EAAAA,EAAkB,CAACmC,GAAI0M,OAErC3K,EAAAA,EAAAA,KAAC6Q,EAAAA,EAAG,CAACD,SAAS,SAAS3D,MAAM,SAAS6D,aAAY,GAAA1Q,OAAKV,GAAe,iBAAkBI,SACtF6K,IACA3K,EAAAA,EAAAA,KAAC+Q,EAAAA,EAAmB,CACnB9S,GAAI0M,EACJ5H,UAAWA,EACXC,aAAcA,OAIjBhD,EAAAA,EAAAA,KAAC6Q,EAAAA,EAAG,CAACD,SAAS,WAAW3D,MAAM,WAAW6D,aAAY,GAAA1Q,OAAKV,GAAe,iBAAkBI,SAC1F6K,IACA3K,EAAAA,EAAAA,KAACgR,EAAAA,EAAY,CACZ/S,GAAI0M,EACJ5H,UAAWA,EACXC,aAAcA,OAIjBhD,EAAAA,EAAAA,KAAC6Q,EAAAA,EAAG,CAACD,SAAS,UAAU3D,MAAM,UAAU6D,aAAY,GAAA1Q,OAAKV,GAAe,iBAAkBI,SACxF6K,IACA3K,EAAAA,EAAAA,KAACuO,EAAAA,EAAW,CACXtQ,GAAI0M,EACJ5H,UAAWA,EACXC,aAAcA,iBASrBsM,IACAtP,EAAAA,EAAAA,KAACmF,EAAAA,EAAK,CAACrC,MAAM,EAAMsC,WAAW,EAAMC,KAAK,KAAKtF,UAAS,GAAAK,OAAKV,GAAe,aAAY,SAAQI,UAC9FF,EAAAA,EAAAA,MAACK,EAAAA,EAAI,CAACqF,SA3KV,SAA+BC,GAC9BA,EAAMC,iBACN,MAAMyL,EAAYvL,OAAOH,EAAMvE,OAAOgE,OAAO/D,OAC7C,IAAI1C,EAAS,CACZoM,QAASA,EACT3F,OAAQiM,EACRvC,YAAanJ,EAAMvE,OAAO0N,YAAYzN,MACtCiQ,aAAa,GAEdnB,EAAS5J,OAAO5H,EACjB,EAiK0CuB,SAAA,EACrCE,EAAAA,EAAAA,KAACmF,EAAAA,EAAMiB,OAAM,CAACrG,UAAU,mBAAkBD,UACzCE,EAAAA,EAAAA,KAACmF,EAAAA,EAAMkB,MAAK,CAACtG,UAAU,cAAaD,UACnCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,OAAMD,SAAC,oBAGvBF,EAAAA,EAAAA,MAACuF,EAAAA,EAAMmB,KAAI,CAACvG,UAAS,QAAAK,OAAUV,GAAe,UAAWI,SAAA,EACxDE,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAAAJ,UACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKY,QAAO,CACZd,UAAS,GAAAK,OAAKV,GAAe,qBAC7BnC,KAAK,SACLL,KAAK,SACL4D,YAAY,SACZP,SAAW5B,GAAMA,EAAEqC,OAAOC,MAC1Be,UAAQ,OAGVhC,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAAAJ,UACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKY,QAAO,CACZd,UAAS,GAAAK,OAAKV,GAAe,qBAC7BnC,KAAK,OACLL,KAAK,cACL4D,YAAY,cACZP,SAAW5B,GAAMA,EAAEqC,OAAOC,MAC1Be,UAAQ,UAIXpC,EAAAA,EAAAA,MAACuF,EAAAA,EAAMyB,OAAM,CAAC7G,UAAU,qCAAoCD,SAAA,EAC3DE,EAAAA,EAAAA,KAAA,OAAAF,UACCE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CACNmG,QAAQ,iBACRlG,QAASA,IAAM4O,GAAsB,GAAOzP,SAC5C,UAIFE,EAAAA,EAAAA,KAAA,OAAAF,UACCE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CAACnD,KAAK,SAASwC,UAAU,YAAWD,SAAC,kBAQhD0P,IACAxP,EAAAA,EAAAA,KAACmF,EAAAA,EAAK,CAACrC,MAAM,EAAMsC,WAAW,EAAMC,KAAK,KAAKtF,UAAS,GAAAK,OAAKV,GAAe,aAAY,SAAQI,UAC9FF,EAAAA,EAAAA,MAACK,EAAAA,EAAI,CAACqF,SA5LV,SAAmCC,GAClCA,EAAMC,iBACN,IAAIjH,EAAS,CACZoM,QAASA,EACTwG,aAAc5L,EAAMvE,OAAOoQ,SAASnQ,OAErCgP,EAAsB9J,OAAO5H,EAC9B,EAqL8CuB,SAAA,EACzCE,EAAAA,EAAAA,KAACmF,EAAAA,EAAMiB,OAAM,CAACrG,UAAU,mBAAkBD,UACzCE,EAAAA,EAAAA,KAACmF,EAAAA,EAAMkB,MAAK,CAACtG,UAAU,cAAaD,UACnCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,OAAMD,SAAC,wBAGvBE,EAAAA,EAAAA,KAACmF,EAAAA,EAAMmB,KAAI,CAACvG,UAAS,QAAAK,OAAUV,GAAe,UAAWI,UACxDE,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAAAJ,UACVF,EAAAA,EAAAA,MAACyR,EAAAA,EAAU,CAAAvR,SAAA,EACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKY,QAAO,CACZd,UAAS,GAAAK,OAAKV,GAAe,qBAC7BnC,KAAMsS,EAAe,OAAS,WAC9B/O,YAAY,iBACZ5D,KAAK,WACLqD,SAAW5B,GAAMA,EAAEqC,OAAOC,MAC1Be,UAAQ,KAEThC,EAAAA,EAAAA,KAACqR,EAAAA,EAAWC,OAAM,CAAAxR,UACjBE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CAACC,QArQU4Q,KAC1BzB,GAAiBD,EAAa,EAoQc/P,SAClC+P,GACA7P,EAAAA,EAAAA,KAAA,KAAGD,UAAU,iBAEbC,EAAAA,EAAAA,KAAA,KAAGD,UAAU,gCAOnBH,EAAAA,EAAAA,MAACuF,EAAAA,EAAMyB,OAAM,CAAC7G,UAAU,qCAAoCD,SAAA,EAC3DE,EAAAA,EAAAA,KAAA,OAAAF,UACCE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CACNmG,QAAQ,iBACRlG,QAASA,IAAM8O,GAA0B,GAAO3P,SAChD,UAIFE,EAAAA,EAAAA,KAAA,OAAAF,UACCE,EAAAA,EAAAA,KAACU,EAAAA,EAAM,CAACnD,KAAK,SAASwC,UAAU,YAAWD,SAAC,oBAQhD8C,EAAiBE,OACjB9C,EAAAA,EAAAA,KAACiF,EAAAA,EAAa,CACb7G,IAAKwE,EAAiBxE,IACtByE,oBAAqBA,EACrBqC,IAAKtC,EAAiBxE,QAK3B,E,oCAEA,U,2KC1UA,SAAS2S,EAAoBhV,GAC5B,MAAM2G,GAAcC,EAAAA,EAAAA,mBACb3G,EAAoBC,IAAyBC,EAAAA,EAAAA,UAAS,IACvDC,GAAaC,EAAAA,EAAAA,UAAQ,IAAM,IAAIC,KAAO,IAEtCmS,EAAuBA,MAC5BgD,EAAAA,EAAAA,IAAczV,EAAMkC,IAClBE,MAAMC,IACN,GAAIA,EAAI+E,OAAQ,CACf,MAAM9E,EAAUC,OAAOC,OAAOH,EAAII,MAMlC,OAcJ,SAAsB9B,GACrB,IAAIC,EAAe,GACnBD,EAAUE,KAAI,CAACC,EAAKC,IACZH,EAAaI,KAAKC,EAAqBF,EAAOD,MAEtDZ,EAAsBU,EACvB,CAzBIF,CAAa4B,GAEbA,EAAQzB,KAAI,CAACC,EAAKC,IACVX,EAAWsC,IAAI5B,EAAIoB,GAAIpB,KAExBuB,CACR,CACC,OAAO,CACR,IAEAM,OAAOC,IACPC,QAAQC,IAAI,WAAYF,EAAE,GACzB,EAeJ,SAAS3B,EAAqBC,EAAKJ,GAClC,MAAO,CACNC,MAAOG,EAAM,EACb+H,OAAQnI,EAAImI,OACZ0J,YAAa7R,EAAI6R,YACjBC,SAAU9R,EAAI8R,SAAShR,MAAM,KAAK,GAAGC,QAAQ,IAAK,KAClDf,IAAKA,EAEP,EApBAuH,EAAAA,EAAAA,YAAU,KACToK,GAAsB,GACpB,KAwEHpK,EAAAA,EAAAA,YAAU,KACLrI,EAAMgH,YACTL,EAAY2B,eAAe,oBAC3BtI,EAAMiH,cAAa,GACpB,GACE,CAACjH,EAAMgH,YAEV,MAAM,YAAErD,IAAgBC,EAAAA,EAAAA,KACxB,OACCK,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAC,UACCF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,wBAAuBD,SAAA,EACrCE,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAAAJ,UACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKY,QAAO,CACZd,UAAS,GAAAK,OAAKV,GAAe,oBAAmB,yBAChDnC,KAAK,OACLuD,YAAY,SACZP,SArCiB5B,IACrB,IAAIoC,EAAapC,EAAEqC,OAAOC,MACtBC,EAAe,GACnB,IAAK,IAAID,KAAS9E,EAAWoC,SAAU,CAAC,IAADyQ,EAAAC,EAAAC,GAEpB,QAAjBF,EAAA/N,EAAMyN,mBAAW,IAAAM,GAAjBA,EAAmBrN,cAAcC,SAAmB,OAAVb,QAAU,IAAVA,OAAU,EAAVA,EAAYY,gBACxC,QADsDsN,EACpEhO,EAAM0N,gBAAQ,IAAAM,GAEK,QAFLC,EAAdD,EACGtR,MAAM,KAAK,GACZC,QAAQ,IAAK,YAAI,IAAAsR,GAFnBA,EAGGvN,cACDC,SAAmB,OAAVb,QAAU,IAAVA,OAAU,EAAVA,EAAYY,gBAClB,OAALV,QAAK,IAALA,GAAAA,EAAO+D,OAAOjD,WAAWH,SAASb,KAElCG,EAAanE,KAAKkE,EAEpB,CACAhF,EACCiF,EAAatE,KAAI,CAACC,EAAKC,IAAUE,EAAqBF,EAAOD,KAC7D,EAoBGmF,UAAQ,OAGVhC,EAAAA,EAAAA,KAACiC,EAAAA,GAAS,CACTnD,QAzEY,CACf,CACC5B,KAAM,KACN6B,SAAWC,GAAQA,EAAIlC,MACvBmC,UAAU,EACVC,SAAU,OACVC,SAAU,QAEX,CACCjC,KAAM,SACN6B,SAAWC,GAAQA,EAAIgG,OACvB/F,UAAU,EACVG,MAAM,EACNF,SAAU,QACVC,SAAU,SAEX,CACCjC,KAAM,cACN6B,SAAWC,GAAQA,EAAI0P,YACvBzP,UAAU,EACVG,MAAM,GAEP,CACClC,KAAM,OACN6B,SAAWC,GAAQA,EAAI2P,SACvB1P,UAAU,EACVG,MAAM,GAEP,CAAElC,KAAM,MAAO6B,SAAWC,GAAQA,EAAInC,IAAK4C,MAAM,IA8C9CjB,KAAMxC,EACNkG,YAAU,EACVC,kBAAmB,GACnBC,SAAU1C,EACV2C,OAAOC,EAAAA,EAAAA,MACPC,kBAAgB,EAChBC,UAAQ,QAKb,C,4BAEA,U,0KCzIA,SAASwO,EAAajV,IACD4G,EAAAA,EAAAA,kBAApB,MACO3G,EAAoBC,IAAyBC,EAAAA,EAAAA,UAAS,IACvDC,GAAaC,EAAAA,EAAAA,UAAQ,IAAM,IAAIC,KAAO,IAEtCmS,EAAuBA,MAC5BiD,EAAAA,EAAAA,IAAoB1V,EAAMkC,IACxBE,MAAMC,IACN,GAAIA,EAAI+E,OAAQ,CACf,MAAM9E,EAAUC,OAAOC,OAAOH,EAAII,MAMlC,OAcJ,SAAsB9B,GACrB,IAAIC,EAAe,GACnBD,EAAUE,KAAI,CAACC,EAAKC,IACZH,EAAaI,KAAKC,EAAqBF,EAAOD,MAEtDZ,EAAsBU,EACvB,CAzBIF,CAAa4B,GAEbA,EAAQzB,KAAI,CAACC,EAAKC,IACVX,EAAWsC,IAAI3B,EAAOD,KAEvBuB,CACR,CACC,OAAO,CACR,IAEAM,OAAOC,IACPC,QAAQC,IAAI,WAAYF,EAAE,GACzB,EAeJ,SAAS3B,EAAqBC,EAAKJ,GAClC,MAAO,CACNC,MAAOG,EAAM,EACbgL,MAAOpL,EAAIoL,MACXyJ,WAAY7U,EAAI6U,WAChBC,UAAW9U,EAAI8U,UACf3J,UAAWnL,EAAImL,UACfnL,IAAKA,EAEP,EArBAuH,EAAAA,EAAAA,YAAU,KACToK,GAAsB,GACpB,KA8FHpK,EAAAA,EAAAA,YAAU,KACLrI,EAAMgH,WACThH,EAAMiH,cAAa,EACpB,GACE,CAACjH,EAAMgH,YAGV,MAAM,YAAErD,IAAgBC,EAAAA,EAAAA,KAExB,OACCK,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAC,UACCF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,wBAAuBD,SAAA,EACrCE,EAAAA,EAAAA,KAACC,EAAAA,EAAKC,MAAK,CAAAJ,UACVE,EAAAA,EAAAA,KAACC,EAAAA,EAAKY,QAAO,CACZtD,KAAK,OACLuD,YAAY,SACZf,UAAS,GAAAK,OAAKV,GAAe,oBAAmB,yBAChDa,SArDiB5B,IACrB,IAAIoC,EAAapC,EAAEqC,OAAOC,MACtBC,EAAe,GAEnB,GACuB,KAAtBH,EAAW6N,QACI,OAAf7N,QACe8N,IAAf9N,EAGAG,EAAe4N,MAAMC,KAAK5S,EAAWoC,eAGrC,IAAK,IAAI0C,KAAS9E,EAAWoC,SAAU,CAAC,IAADqT,EAAAC,EAAAC,EAAAC,GAE1B,QAAXH,EAAA3Q,EAAMgH,aAAK,IAAA2J,GAAXA,EAAajQ,cAAcC,SAASb,EAAWY,gBAC/B,QAD6CkQ,EAC7D5Q,EAAMyQ,kBAAU,IAAAG,GAAhBA,EAAkBlQ,cAAcC,SAASb,EAAWY,gBAC/C,OAALV,QAAK,IAALA,GACY,QADP6Q,EAAL7Q,EAAO0Q,UACL5P,kBAAU,IAAA+P,GADZA,EAEGnQ,cACDC,SAAmB,OAAVb,QAAU,IAAVA,OAAU,EAAVA,EAAYY,gBAClB,OAALV,QAAK,IAALA,GACY,QADP8Q,EAAL9Q,EAAO+G,UACLjG,kBAAU,IAAAgQ,GADZA,EAEGpQ,cACDC,SAAmB,OAAVb,QAAU,IAAVA,OAAU,EAAVA,EAAYY,iBAEvBT,EAAanE,KAAKkE,EAEpB,CAGDhF,EACCiF,EAAatE,KAAI,CAACC,EAAKC,IAAUE,EAAqBF,EAAOD,KAC7D,EAqBGmF,UAAQ,OAGVhC,EAAAA,EAAAA,KAACiC,EAAAA,GAAS,CACTnD,QA/FY,CACf,CACC5B,KAAM,KACN6B,SAAWC,GAAQA,EAAIlC,MACvBmC,UAAU,EACVC,SAAU,OACVC,SAAU,QAEX,CACCjC,KAAM,QACN6B,SAAWC,GAAQA,EAAIiJ,MACvBhJ,UAAU,EACVG,MAAM,EACNF,SAAU,QACVC,SAAU,SAEX,CACCjC,KAAM,aACN6B,SAAWC,GAAQA,EAAI0S,WACvBzS,UAAU,EACVG,MAAM,GAEP,CACClC,KAAM,YACN6B,SAAWC,GAAQA,EAAI2S,UACvB1S,UAAU,EACVG,MAAM,GAEP,CACClC,KAAM,YACN6B,SAAWC,GAAQA,EAAIgJ,UACvB/I,UAAU,EACVG,MAAM,GAEP,CAAElC,KAAM,MAAO6B,SAAWC,GAAQA,EAAInC,IAAK4C,MAAM,IA8D9CjB,KAAMxC,EACNkG,YAAU,EACVC,kBAAmB,GACnBC,SAAU1C,EACV2C,OAAOC,EAAAA,EAAAA,MACPC,kBAAgB,EAChBC,UAAQ,QAKb,C,4BAEA,U,+HCnKIwP,EAAY,CAAC,WAAY,OAAQ,gBAAiB,YAAa,MAK/DC,GAAmBC,EAAAA,EAAAA,GAAmB,sBACtCC,GAAoBD,EAAAA,EAAAA,GAAmB,uBACvCE,GAAiBF,EAAAA,EAAAA,GAAmB,mBAAoB,CAC1DG,UAAW,SAuBThB,EAA0BiB,EAAAA,YAAiB,SAAU5E,EAAM6E,GAC7D,IAAIC,EAAW9E,EAAK8E,SAChBnN,EAAOqI,EAAKrI,KACZoN,EAAgB/E,EAAK+E,cACrB1S,EAAY2N,EAAK3N,UACjB2S,EAAUhF,EAAKiF,GACfN,OAAwB,IAAZK,EAAqB,MAAQA,EACzC3W,GAAQ6W,EAAAA,EAAAA,GAA8BlF,EAAMsE,GAGhD,OADAQ,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,eACpBF,EAAAA,cAAoBD,GAAWS,EAAAA,EAAAA,GAAS,CAC1DP,IAAKA,GACJxW,EAAO,CACRgE,UAAWgT,IAAWhT,EAAWyS,EAAUnN,GAAQmN,EAAW,IAAMnN,EAAMoN,GAAiB,oBAE/F,IACApB,EAAW2B,YAAc,aACzB3B,EAAW4B,KAAOb,EAClBf,EAAW6B,MAhCW,SAAyBnX,GAC7C,OAAoBuW,EAAAA,cAAoBF,EAAgB,KAAmBE,EAAAA,cAAoB,SAASQ,EAAAA,EAAAA,GAAS,CAC/GvV,KAAM,SACLxB,IACL,EA6BAsV,EAAW8B,SAvCc,SAA4BpX,GACnD,OAAoBuW,EAAAA,cAAoBF,EAAgB,KAAmBE,EAAAA,cAAoB,SAASQ,EAAAA,EAAAA,GAAS,CAC/GvV,KAAM,YACLxB,IACL,EAoCAsV,EAAWC,OAASW,EACpBZ,EAAW+B,QAAUjB,EACrB,S", "sources": ["app/components/table/UserInfoOrderTable.jsx", "app/components/table/UserInfoStrategyTable.jsx", "app/user-pages/users/AddBrokerComponent.jsx", "app/user-pages/users/UserEarning.jsx", "app/user-pages/users/UserInfo.jsx", "app/user-pages/users/UserInfoWalletTable.jsx", "app/user-pages/users/UserReferral.jsx", "../node_modules/react-bootstrap/esm/InputGroup.js"], "sourcesContent": ["import React, { useMemo, useState } from \"react\";\r\nimport DataTable from \"react-data-table-component\";\r\nimport { getTheme, getTransactionType } from \"../../user-pages/ui-helper\";\r\nimport { GetUserOrders } from \"../../../services/backendServices\";\r\nimport { useQuery } from \"react-query\";\r\nimport { Form, Button } from \"react-bootstrap\";\r\nimport DatePicker from \"react-datepicker\";\r\nimport \"react-datepicker/dist/react-datepicker.css\";\r\nimport { format } from \"date-fns\";\r\nimport { useTheme } from \"../../context/ThemeContext\";\r\n\r\nfunction UserInfoOrderTable(props) {\r\n\tconst [tableDataFormatted, setTableDataFormatted] = useState([]);\r\n\tconst rawUserMap = useMemo(() => new Map(), []);\r\n\tconst [selectedDate, setSelectedDate] = useState(new Date());\r\n\r\n\t// API CALL for GetUserOrders\r\n\tuseQuery(\"GetUserOrders\", () =>\r\n\t\tGetUserOrders(props.id, format(new Date(), \"MM-dd-yyyy\"))\r\n\t\t\t.then((res) => {\r\n\t\t\t\tconst jsonArr = Object.values(res.data);\r\n\t\t\t\tsetTableData(jsonArr);\r\n\t\t\t\t// Setting row data in rawUserMap with key value\r\n\t\t\t\tjsonArr.map((obj, index) => {\r\n\t\t\t\t\treturn rawUserMap.set(index, obj);\r\n\t\t\t\t});\r\n\t\t\t\treturn res;\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t})\r\n\t);\r\n\r\n\tfunction setTableData(tableData) {\r\n\t\tvar rowTableData = [];\r\n\t\ttableData.map((obj, index) => {\r\n\t\t\treturn rowTableData.push(getRowFormatForTable(index, obj));\r\n\t\t});\r\n\t\tsetTableDataFormatted(rowTableData);\r\n\t}\r\n\r\n\tfunction getRowFormatForTable(ind, obj) {\r\n\t\treturn {\r\n\t\t\tindex: ind + 1,\r\n\t\t\tname: obj.ref_name,\r\n\t\t\ttrading_symbol: obj.trading_symbol,\r\n\t\t\texchange: obj.exchange,\r\n\t\t\tprice: obj.price,\r\n\t\t\ttype: getTransactionType(obj.action_type),\r\n\t\t\torder_time: obj.order_time.split(\".\")[0].replace(\"T\", \" \"),\r\n\t\t\tqty: obj.quantity,\r\n\t\t\tobj: obj,\r\n\t\t};\r\n\t}\r\n\r\n\tconst actionTypeSortFunction = (a, b) => {\r\n\t\tif (\r\n\t\t\ta.obj.action_type === \"B\" ||\r\n\t\t\ta.obj.action_type === \"b\" ||\r\n\t\t\ta.obj.action_type === \"buy\" ||\r\n\t\t\ta.obj.action_type === \"BUY\" ||\r\n\t\t\ta.obj.action_type === \"Buy\"\r\n\t\t) {\r\n\t\t\treturn 1;\r\n\t\t} else {\r\n\t\t\treturn -1;\r\n\t\t}\r\n\t};\r\n\r\n\tconst columns = [\r\n\t\t{\r\n\t\t\tname: \"Id\",\r\n\t\t\tselector: (row) => row.index,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"65px\",\r\n\t\t\tminWidth: \"65px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Name\",\r\n\t\t\tselector: (row) => row.name,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Symbol\",\r\n\t\t\tselector: (row) => row.trading_symbol,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Exchange\",\r\n\t\t\tselector: (row) => row.exchange,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t\tmaxWidth: \"130px\",\r\n\t\t\tminWidth: \"130px\",\r\n\t\t},\r\n\t\t// {\r\n\t\t// \tname: \"S Qty\",\r\n\t\t// \tselector: (row) => row.order_qty,\r\n\t\t// \tsortable: true,\r\n\t\t// \twrap: true,\r\n\t\t// \tmaxWidth: \"100px\",\r\n\t\t// \tminWidth: \"100px\",\r\n\t\t// },\r\n\t\t{\r\n\t\t\tname: \"Price\",\r\n\t\t\tselector: (row) => row.price,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t\tmaxWidth: \"120px\",\r\n\t\t\tminWidth: \"120px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Type\",\r\n\t\t\tselector: (row) => row.type,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t\tsortFunction: actionTypeSortFunction,\r\n\t\t\tmaxWidth: \"100px\",\r\n\t\t\tminWidth: \"100px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Qty\",\r\n\t\t\tselector: (row) => row.qty,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t\tmaxWidth: \"100px\",\r\n\t\t\tminWidth: \"100px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Time\",\r\n\t\t\tselector: (row) => row.order_time,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\r\n\t\t{ name: \"obj\", selector: (row) => row.obj, omit: true },\r\n\t];\r\n\r\n\tconst handleSearch = (e) => {\r\n\t\tvar search_val = e.target.value;\r\n\t\tvar filteredData = [];\r\n\t\tfor (let value of rawUserMap.values()) {\r\n\t\t\tif (\r\n\t\t\t\tvalue.ref_name?.toLowerCase().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.trading_symbol\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.exchange?.toLowerCase().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.action_type?.toLowerCase().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.order_time\r\n\t\t\t\t\t?.split(\".\")[0]\r\n\t\t\t\t\t.replace(\"T\", \" \")\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.order_id?.toLowerCase().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.emsg?.toLowerCase().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue?.price.toString().includes(search_val)\r\n\t\t\t) {\r\n\t\t\t\tfilteredData.push(value);\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetTableDataFormatted(\r\n\t\t\tfilteredData.map((obj, index) => getRowFormatForTable(index, obj))\r\n\t\t);\r\n\t};\r\n\r\n\tconst handleDateChange = (date) => {\r\n\t\tsetSelectedDate(date);\r\n\t\t// const formattedDate = format(date, \"MM-dd-yyyy\");\r\n\t\t// console.log(\"Selected Date:\", formattedDate);\r\n\t};\r\n\r\n\tconst handleShowOrder = () => {\r\n\t\tGetUserOrders(props.id, format(selectedDate, \"MM-dd-yyyy\"))\r\n\t\t\t.then((res) => {\r\n\t\t\t\tconst jsonArr = Object.values(res.data);\r\n\t\t\t\tsetTableData(jsonArr);\r\n\t\t\t\t// Setting row data in rawUserMap with key value\r\n\t\t\t\tjsonArr.map((obj, index) => {\r\n\t\t\t\t\treturn rawUserMap.set(index, obj);\r\n\t\t\t\t});\r\n\t\t\t\treturn res;\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t});\r\n\t};\r\n\r\n\tconst { isDarkTheme } = useTheme()\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<div className=\"row d-flex justify-content-between g-0\">\r\n\t\t\t\t<div className=\"col-md-6 mb-md-0 mb-2\">\r\n\t\t\t\t\t<div className=\"row\">\r\n\t\t\t\t\t\t<div className=\"col-6\">\r\n\t\t\t\t\t\t\t<Form>\r\n\t\t\t\t\t\t\t\t<Form.Group controlId=\"datePicker\" className={`${isDarkTheme && \"dark-datepicker\"} mb-0`}>\r\n\t\t\t\t\t\t\t\t\t{/* Select Date:&nbsp; &nbsp; */}\r\n\t\t\t\t\t\t\t\t\t<DatePicker\r\n\t\t\t\t\t\t\t\t\t\tselected={selectedDate}\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleDateChange}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"form-control\"\r\n\t\t\t\t\t\t\t\t\t\tdateFormat=\"dd-MM-yyyy\" // Customize date format if needed\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t</Form>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div className=\"ml-md-2 ml-0 \"></div>\r\n\t\t\t\t\t\t<div className=\"col-5 mt-0 mt-md-1\">\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\ttype=\"btn\"\r\n\t\t\t\t\t\t\t\tclassName=\"btn btn-md btn-primary\"\r\n\t\t\t\t\t\t\t\tonClick={handleShowOrder}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tShow Orders\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"col-md-5\">\r\n\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\tplaceholder=\"Search\"\r\n\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"} mb-2 searchbox-style`}\r\n\t\t\t\t\t\t\tonChange={handleSearch}\r\n\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Form.Group>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t\t<DataTable\r\n\t\t\t\tcolumns={columns}\r\n\t\t\t\tdata={tableDataFormatted}\r\n\t\t\t\tpagination\r\n\t\t\t\tpaginationPerPage={10}\r\n\t\t\t\tstriped={!isDarkTheme}\r\n\t\t\t\ttheme={getTheme()}\r\n\t\t\t\thighlightOnHover\r\n\t\t\t\tnoHeader\r\n\t\t\t/>\r\n\t\t</>\r\n\t);\r\n}\r\n\r\nexport default UserInfoOrderTable;\r\n", "import React, { useEffect, useMemo, useState } from \"react\";\r\nimport DataTable from \"react-data-table-component\";\r\nimport { useMutation, useQuery } from \"react-query\";\r\nimport {\r\n\tGetStrategySubscription,\r\n\tUpdateStrategySubscription,\r\n} from \"../../../services/backendServices\";\r\nimport ResponseModal from \"../modal/ResponseModal\";\r\nimport { Button, Form, Modal } from \"react-bootstrap\";\r\nimport { useQueryClient } from \"react-query\";\r\nimport { useTheme } from \"../../context/ThemeContext\";\r\nimport { getTheme } from \"../../user-pages/ui-helper\";\r\n\r\nfunction UserInfoStrategyTable(props) {\r\n\tconst queryClient = useQueryClient();\r\n\tconst [apiResponseModal, setApiResponseModal] = useState({\r\n\t\tshow: false,\r\n\t\tres: {},\r\n\t}); // Modal : on API Response\r\n\tconst [fetchData, setFetchData] = useState(false);\r\n\tconst [\r\n\t\tshowUpdateStrategySubscriptionPopup,\r\n\t\tsetShowUpdateStrategySubscriptionPopup,\r\n\t] = useState({\r\n\t\tstatus: false,\r\n\t\tid: \"\",\r\n\t\tlots: 0,\r\n\t\tdate: \"\",\r\n\t\ttrading: false,\r\n\t});\r\n\tconst [tableDataFormatted, setTableDataFormatted] = useState([]);\r\n\tconst rawUserMap = useMemo(() => new Map(), []);\r\n\r\n\tconst FetchApiData = () => {\r\n\t\tGetStrategySubscription(props.id)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tconst jsonArr = Object.values(res);\r\n\t\t\t\tsetTableData(jsonArr);\r\n\t\t\t\t// Setting row data in rawUserMap with key value\r\n\t\t\t\tjsonArr.map((obj, index) => {\r\n\t\t\t\t\treturn rawUserMap.set(index, obj);\r\n\t\t\t\t});\r\n\t\t\t\treturn res;\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t});\r\n\t};\r\n\r\n\t// API CALL for GetStrategySubscription\r\n\tuseQuery(\"GetStrategySubscription\", () => FetchApiData());\r\n\r\n\tfunction setTableData(tableData) {\r\n\t\tvar rowTableData = [];\r\n\t\ttableData.map((obj, index) => {\r\n\t\t\treturn rowTableData.push(getRowFormatForTable(index, obj));\r\n\t\t});\r\n\t\tsetTableDataFormatted(rowTableData);\r\n\t}\r\n\tfunction convertDateFormat(originalDate) {\r\n\t\tconst [year, month, day] = originalDate.split(\"-\");\r\n\t\tconst formattedDate = `${month}-${day}-${year}`;\r\n\t\treturn formattedDate;\r\n\t}\r\n\r\n\tfunction getRowFormatForTable(ind, obj) {\r\n\t\treturn {\r\n\t\t\tindex: ind + 1,\r\n\t\t\tname: obj.name,\r\n\t\t\tlots: obj.lots,\r\n\t\t\tsubscription_start_date: obj.start_date.slice(0, 10),\r\n\t\t\tsubscription_end_date: obj.expiry_date.slice(0, 10),\r\n\t\t\tstatus: !obj.is_expired ? (\r\n\t\t\t\t<label className=\"badge badge-outline-success\"> Active </label>\r\n\t\t\t) : (\r\n\t\t\t\t<label className=\"badge badge-outline-danger\"> Expired </label>\r\n\t\t\t),\r\n\t\t\ttrading: obj.trading ? (\r\n\t\t\t\t<label className=\"badge badge-outline-success\"> ON </label>\r\n\t\t\t) : (\r\n\t\t\t\t<label className=\"badge badge-outline-danger\"> OFF </label>\r\n\t\t\t),\r\n\t\t\taction: (\r\n\t\t\t\t<div className=\"d-flex align-item-center\">\r\n\t\t\t\t\t<button\r\n\t\t\t\t\t\ttype=\"button\"\r\n\t\t\t\t\t\tclassName=\"btn btn-primary btn-icon-text btnUpdateStrategySubscription d-flex align-items-center\"\r\n\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\tsetShowUpdateStrategySubscriptionPopup({\r\n\t\t\t\t\t\t\t\tstatus: true,\r\n\t\t\t\t\t\t\t\tid: obj.id,\r\n\t\t\t\t\t\t\t\tlots: obj.lots,\r\n\t\t\t\t\t\t\t\tdate: obj.expiry_date.slice(0, 10),\r\n\t\t\t\t\t\t\t\ttrading: obj.trading,\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<i className=\"mdi mdi-pencil btn-icon-append m-0\"></i>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</div>\r\n\t\t\t),\r\n\t\t\tobj: obj,\r\n\t\t};\r\n\t}\r\n\r\n\tconst tradingStatusSortFunction = (a, b) => {\r\n\t\tif (a.obj.trading === true && b.obj.trading === false) {\r\n\t\t\treturn 1;\r\n\t\t} else {\r\n\t\t\treturn -1;\r\n\t\t}\r\n\t};\r\n\r\n\tconst isExpiredSortFunction = (a, b) => {\r\n\t\tif (a.obj.is_expired === true && b.obj.is_expired === false) {\r\n\t\t\treturn 1;\r\n\t\t} else {\r\n\t\t\treturn -1;\r\n\t\t}\r\n\t};\r\n\r\n\t// API CALL to Edit Strategy Record\r\n\tconst editMutation = useMutation(\"UpdateStrategySubscription\", (values) =>\r\n\t\tUpdateStrategySubscription(values)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tsetShowUpdateStrategySubscriptionPopup({\r\n\t\t\t\t\t\tstatus: false,\r\n\t\t\t\t\t\tid: \"\",\r\n\t\t\t\t\t\tlots: 0,\r\n\t\t\t\t\t\tdate: \"\",\r\n\t\t\t\t\t\ttrading: false,\r\n\t\t\t\t\t});\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\tFetchApiData();\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t})\r\n\t);\r\n\tfunction handleUpdateStrategySubscription(event) {\r\n\t\tevent.preventDefault();\r\n\t\tconst lotVal = Number(event.target.lots.value);\r\n\t\tconst date = convertDateFormat(event.target.date.value);\r\n\t\tvar values = {\r\n\t\t\tss_id: showUpdateStrategySubscriptionPopup.id,\r\n\t\t\texpiry_date: date,\r\n\t\t\tlots: lotVal,\r\n\t\t\ttrading: event.target.strategy_trading.checked,\r\n\t\t\tis_expired: false,\r\n\t\t};\r\n\r\n\t\teditMutation.mutate(values);\r\n\t}\r\n\r\n\tconst columns = [\r\n\t\t{\r\n\t\t\tname: \"Id\",\r\n\t\t\tselector: (row) => row.index,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"65px\",\r\n\t\t\tminWidth: \"65px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Name\",\r\n\t\t\tselector: (row) => row.name,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t\tmaxWidth: \"220px\",\r\n\t\t\tminWidth: \"220px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Lots\",\r\n\t\t\tselector: (row) => row.lots,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t\tmaxWidth: \"100px\",\r\n\t\t\tminWidth: \"100px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Start Date\",\r\n\t\t\tselector: (row) => row.subscription_start_date,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"End Date\",\r\n\t\t\tselector: (row) => row.subscription_end_date,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Status\",\r\n\t\t\tselector: (row) => row.status,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t\tsortFunction: isExpiredSortFunction,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Trading\",\r\n\t\t\tselector: (row) => row.trading,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t\tsortFunction: tradingStatusSortFunction,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Action\",\r\n\t\t\tselector: (row) => row.action,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{ name: \"obj\", selector: (row) => row.obj, omit: true },\r\n\t];\r\n\r\n\tconst handleSearch = (e) => {\r\n\t\tvar search_val = e.target.value;\r\n\t\tvar filteredData = [];\r\n\t\tfor (let value of rawUserMap.values()) {\r\n\t\t\tconst subscription_start_date = value.start_date?.slice(0, 10);\r\n\t\t\tconst subscription_end_date = value.expiry_date?.slice(0, 10);\r\n\t\t\tconst statusVal = !value.is_expired ? \"Active\" : \"Expired\";\r\n\t\t\tconst tradingValue = value.trading ? \"ON\" : \"OFF\";\r\n\t\t\tif (\r\n\t\t\t\tvalue.strategy_id\r\n\t\t\t\t\t?.toString()\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tsubscription_start_date\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tsubscription_end_date\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tstatusVal?.toLowerCase().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\ttradingValue?.toLowerCase().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.lots\r\n\t\t\t\t\t?.toString()\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue?.amount?.toString().includes(search_val)\r\n\t\t\t) {\r\n\t\t\t\tfilteredData.push(value);\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetTableDataFormatted(\r\n\t\t\tfilteredData.map((obj, index) => getRowFormatForTable(index, obj))\r\n\t\t);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tif (fetchData) {\r\n\t\t\tqueryClient.refetchQueries(\"GetStrategySubscription\");\r\n\t\t\tsetFetchData(false);\r\n\t\t}\r\n\t}, [fetchData]);\r\n\r\n\tconst { isDarkTheme } = useTheme()\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<div className=\"table-responsive mt-3\">\r\n\t\t\t\t<Form.Group>\r\n\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\tplaceholder=\"Search\"\r\n\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"} mb-2 searchbox-style`}\r\n\r\n\t\t\t\t\t\tonChange={handleSearch}\r\n\t\t\t\t\t\trequired\r\n\t\t\t\t\t/>\r\n\t\t\t\t</Form.Group>\r\n\t\t\t\t<DataTable\r\n\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\tdata={tableDataFormatted}\r\n\t\t\t\t\tpagination\r\n\t\t\t\t\tpaginationPerPage={10}\r\n\t\t\t\t\tstriped={!isDarkTheme}\r\n\t\t\t\t\ttheme={getTheme()}\r\n\t\t\t\t\thighlightOnHover\r\n\t\t\t\t\tnoHeader\r\n\t\t\t\t/>\r\n\t\t\t\t{apiResponseModal.show && (\r\n\t\t\t\t\t<ResponseModal\r\n\t\t\t\t\t\tres={apiResponseModal.res}\r\n\t\t\t\t\t\tsetApiResponseModal={setApiResponseModal}\r\n\t\t\t\t\t\tmsg={apiResponseModal.res.msg}\r\n\t\t\t\t\t/>\r\n\t\t\t\t)}\r\n\t\t\t\t{showUpdateStrategySubscriptionPopup.status && (\r\n\t\t\t\t\t<Modal show={true} animation={true} size=\"md\" className={`${isDarkTheme && \"dark-modal\"} mt-5`}>\r\n\t\t\t\t\t\t<Form onSubmit={handleUpdateStrategySubscription}>\r\n\t\t\t\t\t\t\t<Modal.Header className=\"text-center py-2\">\r\n\t\t\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t\t\t<h3 className=\"mb-0\">Update Subscription</h3>\r\n\t\t\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t\t\t<Modal.Body className={`p-10 ${!isDarkTheme && \"border\"}`}>\r\n\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t<Form.Label className={`${isDarkTheme && \"dark-text\"}`}>Lot Size</Form.Label>\r\n\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"}`}\r\n\t\t\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\t\t\tname=\"lots\"\r\n\t\t\t\t\t\t\t\t\t\tdefaultValue={showUpdateStrategySubscriptionPopup.lots}\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => e.target.value}\r\n\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t<Form.Label className={`${isDarkTheme && \"dark-text\"}`}>Expiry Date : (mm/dd/yyyy)</Form.Label>\r\n\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"}`}\r\n\t\t\t\t\t\t\t\t\t\ttype=\"date\"\r\n\t\t\t\t\t\t\t\t\t\tname=\"date\"\r\n\t\t\t\t\t\t\t\t\t\tdefaultValue={showUpdateStrategySubscriptionPopup.date}\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => e.target.value}\r\n\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t<Form.Group className={`d-flex align-items-center`}>\r\n\t\t\t\t\t\t\t\t\t<Form.Check\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"form-check-input mt-0 ml-1\"\r\n\t\t\t\t\t\t\t\t\t\ttype=\"switch\"\r\n\t\t\t\t\t\t\t\t\t\tid=\"strategy_trading\"\r\n\t\t\t\t\t\t\t\t\t\tname=\"strategy_trading\"\r\n\t\t\t\t\t\t\t\t\t\tdefaultChecked={showUpdateStrategySubscriptionPopup.trading}\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => e.target.value}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t<label htmlFor=\"strategy_trading\" className={`${isDarkTheme && \"dark-text\"} m-0`}>\r\n\t\t\t\t\t\t\t\t\t\tTrading Status\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t</Modal.Body>\r\n\t\t\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\t\tsetShowUpdateStrategySubscriptionPopup({\r\n\t\t\t\t\t\t\t\t\t\t\t\tstatus: false,\r\n\t\t\t\t\t\t\t\t\t\t\t\tid: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tlots: 0,\r\n\t\t\t\t\t\t\t\t\t\t\t\tdate: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\ttrading: false,\r\n\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<Button type=\"submit\" className=\"mx-2 px-3\">\r\n\t\t\t\t\t\t\t\t\t\tYes\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t\t\t</Form>\r\n\t\t\t\t\t</Modal>\r\n\t\t\t\t)}\r\n\t\t\t</div>\r\n\t\t</>\r\n\t);\r\n}\r\n\r\nexport default UserInfoStrategyTable;\r\n", "import React, { useEffect, useState } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON>, Spinner, Modal } from \"react-bootstrap\";\r\nimport { useMutation, useQuery, useQueryClient } from \"react-query\";\r\nimport {\r\n\tAddBroker,\r\n\tGetAvailableBrokers,\r\n\tSendDataForKotakOtp,\r\n\tUpdateBrokerTradingFlag,\r\n\tRemoveBroker,\r\n} from \"../../../services/backendServices\";\r\nimport ResponseModal from \"../../components/modal/ResponseModal\";\r\nimport \"../userpages.css\";\r\nimport Select from \"react-select\";\r\nimport withAuth from \"../../components/higher-order/withauth\";\r\nimport BrokerDataTable from \"../../components/table/BrokerDataTable\";\r\nimport { TOKEN_URL } from \"../../../Util/constant\";\r\nimport OTPForBrokerConnectionModel from \"../../components/modal/OTPForBrokerConnectionModel\";\r\nimport { mergeStyles } from \"react-select/dist/react-select.cjs.prod\";\r\nimport { customStylesForSelect, hoverEffectOnSelect } from \"../ui-helper\";\r\nimport { useTheme } from \"../../context/ThemeContext\";\r\n\r\nfunction AddBrokerComponent(props) {\r\n\tconst [apiResponseModal, setApiResponseModal] = useState({\r\n\t\tshow: false,\r\n\t\tres: {},\r\n\t}); // Modal : on API Response\r\n\tconst [showDeletePopup, setShowDeletePopup] = useState(false);\r\n\tconst [fetchData, setFetchData] = useState(false); // Fetch data inside UserBroker Table\r\n\tconst [showBrokerConnection, setShowBrokerConnection] = useState(false); // Fetch data inside UserBroker Table\r\n\tconst [brokerData, setBrokerData] = useState(null);\r\n\tconst queryClient = useQueryClient();\r\n\tconst [isFetching, setFetching] = useState(false);\r\n\r\n\tconst initialFormValues = {\r\n\t\tnic_name: \"\",\r\n\t\tmobile_no: \"\",\r\n\t\temail: \"\",\r\n\t};\r\n\tconst [formValues, setFormValues] = useState(initialFormValues);\r\n\tconst [options, setOptions] = useState([]);\r\n\tconst [selectedValueForBrokerName, setSelectedValueForBrokerName] =\r\n\t\tuseState(null);\r\n\tconst [availableBrokerFields, setAvailableBrokerFields] = useState(null);\r\n\r\n\tconst handleBrokerChangeInAddBrokerForm = (selectedOption) => {\r\n\t\tsetSelectedValueForBrokerName(selectedOption);\r\n\t\tsetAvailableBrokerFields(selectedOption.value);\r\n\t};\r\n\r\n\tconst handleChange = (event) => {\r\n\t\tconst { name, value } = event.target;\r\n\t\tsetFormValues((prevValues) => ({ ...prevValues, [name]: value }));\r\n\t};\r\n\t// UseEffect to change field status on dropdown change\r\n\tuseEffect(() => {\r\n\t\tsetFormValues(initialFormValues);\r\n\t}, [selectedValueForBrokerName]);\r\n\r\n\tconst renderFormInputField = (field, fieldName) => {\r\n\t\treturn (\r\n\t\t\t<Form.Group>\r\n\t\t\t\t<Form.Label className={`${isDarkTheme ? \"dark-text\" : \"text-muted\"}`}>{field.placeholder}</Form.Label>\r\n\t\t\t\t<Form.Control\r\n\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"}`}\r\n\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\tplaceholder={field.placeholder}\r\n\t\t\t\t\tname={fieldName}\r\n\t\t\t\t\tvalue={formValues[fieldName]}\r\n\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\trequired\r\n\t\t\t\t/>\r\n\t\t\t</Form.Group>\r\n\t\t);\r\n\t};\r\n\r\n\tconst renderBrokerFields = () => {\r\n\t\tconst broker = availableBrokerFields ? availableBrokerFields : null;\r\n\t\tif (broker) {\r\n\t\t\tconst brokerFields = available_broker_res.data[broker];\r\n\t\t\tconst fieldsToShow = Object.entries(brokerFields)\r\n\t\t\t\t.filter(([_, field]) => field.show)\r\n\t\t\t\t.map(([fieldName, field]) => renderFormInputField(field, fieldName));\r\n\r\n\t\t\tconst rows = [];\r\n\t\t\tfor (let i = 0; i < fieldsToShow.length; i += 3) {\r\n\t\t\t\tconst rowFields = fieldsToShow.slice(i, i + 3);\r\n\t\t\t\trows.push(\r\n\t\t\t\t\t<div key={i} className=\"row\">\r\n\t\t\t\t\t\t{rowFields.map((field, index) => (\r\n\t\t\t\t\t\t\t<div key={index} className=\"col-md-4 grid-margin mb-0\">\r\n\t\t\t\t\t\t\t\t{field}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t))}\r\n\t\t\t\t\t</div>\r\n\t\t\t\t);\r\n\t\t\t}\r\n\t\t\treturn rows;\r\n\t\t}\r\n\t\treturn null;\r\n\t};\r\n\r\n\t// API CALL for GetAvailableBrokers\r\n\tconst { data: available_broker_res } = useQuery(\"GetAvailableBrokers\", () =>\r\n\t\tGetAvailableBrokers()\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tvar options_arr = [];\r\n\t\t\t\t\tfor (var key in res.data) {\r\n\t\t\t\t\t\toptions_arr.push({ label: key, value: key });\r\n\t\t\t\t\t}\r\n\t\t\t\t\tsetOptions(options_arr);\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t})\r\n\t);\r\n\r\n\t// API CALL to Add Broker\r\n\tconst addBrokerMutation = useMutation(\"AddBroker\", (values) =>\r\n\t\tAddBroker(values)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\tsetFetchData(true);\r\n\t\t\t\t\tsetShowBrokerConnection(!showBrokerConnection);\r\n\t\t\t\t\tsetSelectedValueForBrokerName(null);\r\n\t\t\t\t\tsetFormValues(initialFormValues);\r\n\t\t\t\t\tprops.fetchUserProfileData();\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t})\r\n\t);\r\n\r\n\tfunction getAuthCodeFromURL(url, auth_key) {\r\n\t\tconst urlParams = new URLSearchParams(url.substring(url.indexOf(\"?\")));\r\n\t\tconst authCode = urlParams.get(auth_key);\r\n\t\treturn authCode;\r\n\t}\r\n\r\n\tfunction handleFyersTokenGeneration() {\r\n\t\tvar api_key = formValues.api_key;\r\n\t\tconst data = {\r\n\t\t\tclient_id: api_key,\r\n\t\t\tredirect_uri: TOKEN_URL,\r\n\t\t\tresponse_type: \"code\",\r\n\t\t\tstate: \"sample\",\r\n\t\t};\r\n\r\n\t\tconst apiUrl = \"http://api.fyers.in/api/v2/generate-authcode\";\r\n\t\tconst urlParams = new URLSearchParams(data).toString();\r\n\t\tconst url = `${apiUrl}?${urlParams}`;\r\n\t\tconsole.log(url);\r\n\t\t// Open the popup window with specified dimensions\r\n\t\tconst popup = window.open(url, \"newwin\", \"height=700px,width=700px\");\r\n\r\n\t\t// Check for the token in the callback URL\r\n\t\tconst checkToken = setInterval(() => {\r\n\t\t\tif (popup.closed) {\r\n\t\t\t\tclearInterval(checkToken);\r\n\t\t\t} else {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst url = popup.location.href;\r\n\t\t\t\t\tconsole.log(url);\r\n\t\t\t\t\tif (url.includes(\"auth_code\")) {\r\n\t\t\t\t\t\tvar auth_code = getAuthCodeFromURL(url, \"auth_code\");\r\n\t\t\t\t\t\tvar custForm = formValues;\r\n\t\t\t\t\t\tcustForm.app_id = auth_code;\r\n\t\t\t\t\t\tcustForm.is_link_login = true;\r\n\t\t\t\t\t\tcustForm.broker_name = selectedValueForBrokerName.value;\r\n\t\t\t\t\t\tcustForm.user_id = props.user_id;\r\n\t\t\t\t\t\taddBrokerMutation.mutate(custForm);\r\n\t\t\t\t\t\tclearInterval(checkToken);\r\n\t\t\t\t\t\tpopup.close();\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\t// Handle error accessing popup location, if necessary\r\n\t\t\t\t\t// console.log('Error accessing popup location:', error);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}, 500);\r\n\t}\r\n\r\n\tfunction handleZerodhaTokenGeneration() {\r\n\t\tvar api_key = formValues.api_key;\r\n\t\tconst data = {\r\n\t\t\tv: 3,\r\n\t\t\tapi_key: api_key,\r\n\t\t};\r\n\r\n\t\tconst apiUrl = \"https://kite.zerodha.com/connect/login\";\r\n\t\tconst urlParams = new URLSearchParams(data).toString();\r\n\t\tconst url = `${apiUrl}?${urlParams}`;\r\n\t\tconsole.log(url);\r\n\t\t// Open the popup window with specified dimensions\r\n\t\tconst popup = window.open(url, \"newwin\", \"height=700px,width=700px\");\r\n\r\n\t\t// Check for the token in the callback URL\r\n\t\tconst checkToken = setInterval(() => {\r\n\t\t\tif (popup.closed) {\r\n\t\t\t\tclearInterval(checkToken);\r\n\t\t\t} else {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst url = popup.location.href;\r\n\t\t\t\t\tconsole.log(url);\r\n\t\t\t\t\tif (url.includes(\"request_token\")) {\r\n\t\t\t\t\t\tvar auth_code = getAuthCodeFromURL(url, \"request_token\");\r\n\t\t\t\t\t\tvar custForm = formValues;\r\n\t\t\t\t\t\tcustForm.app_id = auth_code;\r\n\t\t\t\t\t\tcustForm.is_link_login = true;\r\n\t\t\t\t\t\tcustForm.broker_name = selectedValueForBrokerName.value;\r\n\t\t\t\t\t\tcustForm.user_id = props.user_id;\r\n\t\t\t\t\t\taddBrokerMutation.mutate(custForm);\r\n\t\t\t\t\t\tclearInterval(checkToken);\r\n\t\t\t\t\t\tpopup.close();\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\t// Handle error accessing popup location, if necessary\r\n\t\t\t\t\t// console.log('Error accessing popup location:', error);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}, 500);\r\n\t}\r\n\r\n\tfunction handleFlattradeTokenGeneration() {\r\n\t\tvar api_key = formValues.api_key;\r\n\t\tconst data = {\r\n\t\t\tapp_key: api_key,\r\n\t\t};\r\n\r\n\t\tconst apiUrl = \"https://auth.flattrade.in\";\r\n\t\tconst urlParams = new URLSearchParams(data).toString();\r\n\t\tconst url = `${apiUrl}?${urlParams}`;\r\n\t\tconsole.log(url);\r\n\t\t// Open the popup window with specified dimensions\r\n\t\tconst popup = window.open(url, \"newwin\", \"height=700px,width=700px\");\r\n\r\n\t\t// Check for the token in the callback URL\r\n\t\tconst checkToken = setInterval(() => {\r\n\t\t\tif (popup.closed) {\r\n\t\t\t\tclearInterval(checkToken);\r\n\t\t\t} else {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst url = popup.location.href;\r\n\t\t\t\t\tconsole.log(url);\r\n\t\t\t\t\tif (url.includes(\"code\")) {\r\n\t\t\t\t\t\tvar auth_code = getAuthCodeFromURL(url, \"code\");\r\n\t\t\t\t\t\tvar custForm = formValues;\r\n\t\t\t\t\t\tcustForm.app_id = auth_code;\r\n\t\t\t\t\t\tcustForm.is_link_login = true;\r\n\t\t\t\t\t\tcustForm.broker_name = selectedValueForBrokerName.value;\r\n\t\t\t\t\t\tcustForm.user_id = props.user_id;\r\n\t\t\t\t\t\taddBrokerMutation.mutate(custForm);\r\n\t\t\t\t\t\tclearInterval(checkToken);\r\n\t\t\t\t\t\tpopup.close();\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\t// Handle error accessing popup location, if necessary\r\n\t\t\t\t\t// console.log('Error accessing popup location:', error);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}, 500);\r\n\t}\r\n\r\n\tconst [otpModel, setOtpModel] = useState({ show: false, form_data: {} });\r\n\t// currently not in use\r\n\tasync function handleKotakNeoTokenGeneration() {\r\n\t\tconst form_data = formValues;\r\n\t\tconst neo_data = await SendDataForKotakOtp(form_data);\r\n\t\tif (neo_data.status) {\r\n\t\t\tform_data.broker_name = \"kotakneo\";\r\n\t\t\tform_data.auth_token = neo_data.auth_token;\r\n\t\t\tform_data.neo_sid = neo_data.neo_sid;\r\n\t\t\tform_data.neo_userid = neo_data.neo_userid;\r\n\t\t\tform_data.login_auth_token = neo_data.login_auth_token;\r\n\t\t\tsetOtpModel({\r\n\t\t\t\tshow: true,\r\n\t\t\t\tform_data: form_data,\r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\tsetApiResponseModal({ show: true, res: neo_data });\r\n\t\t}\r\n\t}\r\n\r\n\t// Function to Handle Form Submit\r\n\tconst handleSubmit = (event) => {\r\n\t\tif (selectedValueForBrokerName.value == \"fyers\") {\r\n\t\t\thandleFyersTokenGeneration();\r\n\t\t} else if (selectedValueForBrokerName.value == \"zerodhamaster\") {\r\n\t\t\thandleZerodhaTokenGeneration();\r\n\t\t} else if (selectedValueForBrokerName.value == \"flattrade\") {\r\n\t\t\thandleFlattradeTokenGeneration();\r\n\t\t} else {\r\n\t\t\tvar custForm = formValues;\r\n\t\t\tcustForm.broker_name = selectedValueForBrokerName.value;\r\n\t\t\tcustForm.user_id = Number(props.user_id);\r\n\t\t\taddBrokerMutation.mutate(formValues);\r\n\t\t}\r\n\t\tevent.preventDefault();\r\n\t};\r\n\tconst diconnect_mutation = useMutation(\"RemoveBroker\", () =>\r\n\t\tRemoveBroker(Number(props.userInfo.broker_data.id))\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tprops.userInfo.broker_data = false;\r\n\t\t\t\t\tsetShowDeletePopup(false);\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t})\r\n\t);\r\n\r\n\tconst update_mutation = useMutation(\"UpdateBrokerTradingFlag\", (values) =>\r\n\t\tUpdateBrokerTradingFlag(values)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t})\r\n\t);\r\n\t// Update Trading Status mutation\r\n\tconst handleTradingStatusChange = async (event) => {\r\n\t\tconst { checked } = event.target;\r\n\t\t// console.log(\"===============\", checked);\r\n\t\tupdate_mutation.mutate({\r\n\t\t\tuser_id: props.user_id,\r\n\t\t\tflag_val: checked,\r\n\t\t});\r\n\t};\r\n\r\n\tconst { isDarkTheme } = useTheme()\r\n\treturn (\r\n\t\t<div>\r\n\t\t\t{props.userInfo.broker_data !== false ? (\r\n\t\t\t\t<div className=\"row\">\r\n\t\t\t\t\t<div className=\"col-lg-12 grid-margin stretch-card mb-0\">\r\n\t\t\t\t\t\t<div className={`card ${isDarkTheme && \"dark-theme-card\"}`}>\r\n\t\t\t\t\t\t\t<div className=\"card-body\">\r\n\t\t\t\t\t\t\t\t<div className=\"container ml-0 mr-0 mb-0 mt-0\">\r\n\t\t\t\t\t\t\t\t\t<div className=\"row\">\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-4 pt-1\">\r\n\t\t\t\t\t\t\t\t\t\t\t<h4 className=\"preview-subject\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{props.userInfo.broker_data?.nic_name} -{\" \"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t{props.userInfo.broker_data?.username}-{\" \"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t{props.userInfo.broker_data?.broker_name?.toUpperCase()}\r\n\t\t\t\t\t\t\t\t\t\t\t</h4>\r\n\t\t\t\t\t\t\t\t\t\t\t<p>{props.userInfo?.email}</p>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-2 pt-1\">\r\n\t\t\t\t\t\t\t\t\t\t\t{props.userInfo.broker_data?.broker_flag ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"button\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"btn btn-outline-success btn-icon-text\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tConnected\r\n\t\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"button\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"btn btn-outline-danger btn-icon-text\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{\" \"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tDisconnected{\" \"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-2 pt-1\">\r\n\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"button\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => setShowDeletePopup(true)}\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"btn btn-danger btn-icon-text\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<i className=\"mdi mdi-delete\"></i>\r\n\t\t\t\t\t\t\t\t\t\t\t\tRemove\r\n\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t{props.userInfo.broker_data && (\r\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-2 pt-1\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Group className={`${isDarkTheme && \"dark-text\"} d-flex align-items-center mt-1`} >\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Check\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"form-check-input mt-0 ml-1\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"switch\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tid=\"trading_status\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tname=\"trading_status\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdefaultChecked={\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tprops.userInfo.broker_data?.trading_flag\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleTradingStatusChange} // Bind the event handler here\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<label htmlFor=\"trading_status\" className={`${isDarkTheme && \"dark-text\"} m-0`}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tTrading\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t) : (\r\n\t\t\t\t<div className=\"row\">\r\n\t\t\t\t\t<div className=\"col-lg-12 grid-margin stretch-card\">\r\n\t\t\t\t\t\t<div className={`card ${isDarkTheme && \"dark-theme-card\"}`}>\r\n\t\t\t\t\t\t\t<div className=\"card-body\">\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tclassName=\"d-flex align-items-center justify-content-between cursor-pointer\"\r\n\t\t\t\t\t\t\t\t\tonClick={() => setShowBrokerConnection(!showBrokerConnection)}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<h4 className={`card-title ${isDarkTheme && \"dark-card-title\"} mb-1`}>Connect Master's Broker</h4>\r\n\t\t\t\t\t\t\t\t\t<i className=\"mdi mdi-arrow-down-drop-circle-outline\"></i>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t{showBrokerConnection ? (\r\n\t\t\t\t\t\t\t\t\t<Form className=\"mt-4\" onSubmit={handleSubmit}>\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"row scroll\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Group className=\"mb-0\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className={`${isDarkTheme ? \"dark-text\" : \"text-muted\"}`}>Select Broker</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t// colors=\"primary\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassNamePrefix=\"react-select\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName={isDarkTheme ? \"dark-select\" : \"\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={selectedValueForBrokerName}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleBrokerChangeInAddBrokerForm}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\toptions={options}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSearchable={true}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Select broker\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyles={mergeStyles(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thoverEffectOnSelect,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcustomStylesForSelect\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className={`${isDarkTheme ? \"dark-text\" : \"text-muted\"}`}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tNickname\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"}`}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Enter Nickname\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tname=\"nic_name\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={formValues.nic_name}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className={`${isDarkTheme ? \"dark-text\" : \"text-muted\"}`}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tMobile No.\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"}`}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"tel\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpattern=\"[0-9]{10}\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttitle=\"Enter a 10-digit contact number\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Enter Mobile No.\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tname=\"mobile_no\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tminLength=\"10\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmaxLength=\"10\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={formValues.mobile_no}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Label className={`${isDarkTheme ? \"dark-text\" : \"text-muted\"}`}>Email</Form.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"}`}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"email\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Enter Email\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tname=\"email\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={formValues.email}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div id=\"custom-row-container\">{renderBrokerFields()}</div>\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"row scroll\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"col-md-4 pt-4\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"button\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"btn btn-md btn-danger mr-3\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetSelectedValueForBrokerName(null);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetAvailableBrokerFields(null);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tClear\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Button type=\"submit\" className=\"btn btn-md\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tAdd\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</Form>\r\n\t\t\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t)}\r\n\t\t\t<OTPForBrokerConnectionModel\r\n\t\t\t\tshow={otpModel.show}\r\n\t\t\t\tform_data={otpModel.form_data}\r\n\t\t\t\tmutationFunction={addBrokerMutation}\r\n\t\t\t/>\r\n\t\t\t{showDeletePopup && (\r\n\t\t\t\t<Modal show={true} animation={true} size=\"md\" className={`${isDarkTheme && \"dark-modal\"} mt-5`}>\r\n\t\t\t\t\t<Modal.Header className=\"text-center py-3\">\r\n\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t<h5 className=\"mb-0\">Disconnect the broker</h5>\r\n\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t<Modal.Body className={`p-10 ${isDarkTheme && \"border\"}`}>\r\n\t\t\t\t\t\tDo you want disconnect your account?\r\n\t\t\t\t\t</Modal.Body>\r\n\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\tonClick={() => setShowDeletePopup(false)}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tvariant=\"outline-primary\"\r\n\t\t\t\t\t\t\t\tclassName=\"mx-2 px-3\"\r\n\t\t\t\t\t\t\t\tonClick={() => diconnect_mutation.mutate()}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tYes\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t</Modal>\r\n\t\t\t)}\r\n\t\t\t{apiResponseModal.show && (\r\n\t\t\t\t<ResponseModal\r\n\t\t\t\t\tres={apiResponseModal.res}\r\n\t\t\t\t\tsetApiResponseModal={setApiResponseModal}\r\n\t\t\t\t\tmsg={apiResponseModal.res.msg}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t</div>\r\n\t);\r\n}\r\n\r\nexport default withAuth(AddBrokerComponent);\r\n", "import React, { useEffect, useMemo, useState } from \"react\";\r\nimport DataTable from \"react-data-table-component\";\r\nimport { GetUserEarningData } from \"../../../services/backendServices\";\r\nimport { useQuery, useQueryClient } from \"react-query\";\r\nimport { Form } from \"react-bootstrap\";\r\nimport { useTheme } from \"../../context/ThemeContext\";\r\nimport { getTheme } from \"../ui-helper\";\r\n\r\nfunction UserEarning(props) {\r\n\tconst queryClient = useQueryClient();\r\n\tconst [tableDataFormatted, setTableDataFormatted] = useState([]);\r\n\tconst rawUserMap = useMemo(() => new Map(), []);\r\n\r\n\tconst fetchTransactionData = () => {\r\n\t\tGetUserEarningData(props.id)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tconst jsonArr = Object.values(res.data);\r\n\t\t\t\t\tsetTableData(jsonArr);\r\n\t\t\t\t\t// Setting row data in rawUserMap with key value\r\n\t\t\t\t\tjsonArr.map((obj, index) => {\r\n\t\t\t\t\t\treturn rawUserMap.set(index, obj);\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t});\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tfetchTransactionData();\r\n\t}, []);\r\n\r\n\tfunction setTableData(tableData) {\r\n\t\tvar rowTableData = [];\r\n\t\ttableData.map((obj, index) => {\r\n\t\t\treturn rowTableData.push(getRowFormatForTable(index, obj));\r\n\t\t});\r\n\t\tsetTableDataFormatted(rowTableData);\r\n\t}\r\n\r\n\tfunction getRowFormatForTable(ind, obj) {\r\n\t\treturn {\r\n\t\t\tindex: ind + 1,\r\n\t\t\tamount: obj.amount,\r\n\t\t\tdescription: obj.description,\r\n\t\t\tadd_time: obj.add_time.split(\".\")[0].replace(\"T\", \" \"),\r\n\t\t\tobj: obj,\r\n\t\t};\r\n\t}\r\n\r\n\tconst columns = [\r\n\t\t{\r\n\t\t\tname: \"Id\",\r\n\t\t\tselector: (row) => row.index,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"65px\",\r\n\t\t\tminWidth: \"65px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Amount\",\r\n\t\t\tselector: (row) => row.amount,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t\tmaxWidth: \"150px\",\r\n\t\t\tminWidth: \"150px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Description\",\r\n\t\t\tselector: (row) => row.description,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Time\",\r\n\t\t\tselector: (row) => row.add_time,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{ name: \"obj\", selector: (row) => row.obj, omit: true },\r\n\t];\r\n\r\n\tconst handleSearch = (e) => {\r\n\t\tvar search_val = e.target.value;\r\n\t\tvar filteredData = [];\r\n\r\n\t\tif (\r\n\t\t\tsearch_val.trim() === \"\" ||\r\n\t\t\tsearch_val === null ||\r\n\t\t\tsearch_val === undefined\r\n\t\t) {\r\n\t\t\t// If search value is empty, show all data\r\n\t\t\tfilteredData = Array.from(rawUserMap.values());\r\n\t\t} else {\r\n\t\t\t// Filter data based on search value\r\n\t\t\tfor (let value of rawUserMap.values()) {\r\n\t\t\t\tif (\r\n\t\t\t\t\tvalue.description?.toLowerCase().includes(search_val.toLowerCase()) ||\r\n\t\t\t\t\tvalue.add_time\r\n\t\t\t\t\t\t?.split(\".\")[0]\r\n\t\t\t\t\t\t.replace(\"T\", \" \")\r\n\t\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t\t.includes(search_val.toLowerCase()) ||\r\n\t\t\t\t\tvalue?.amount.toString().includes(search_val)\r\n\t\t\t\t) {\r\n\t\t\t\t\tfilteredData.push(value);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tsetTableDataFormatted(\r\n\t\t\tfilteredData.map((obj, index) => getRowFormatForTable(index, obj))\r\n\t\t);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tif (props.fetchData) {\r\n\t\t\tqueryClient.refetchQueries(\"GetWalletHistory\");\r\n\t\t\tprops.setFetchData(false);\r\n\t\t}\r\n\t}, [props.fetchData]);\r\n\r\n\tconst { isDarkTheme } = useTheme()\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<div className=\"table-responsive mt-3\">\r\n\t\t\t\t<Form.Group>\r\n\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"} mb-2 searchbox-style`}\r\n\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\tplaceholder=\"Search\"\r\n\t\t\t\t\t\tonChange={handleSearch}\r\n\t\t\t\t\t\trequired\r\n\t\t\t\t\t/>\r\n\t\t\t\t</Form.Group>\r\n\t\t\t\t<DataTable\r\n\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\tdata={tableDataFormatted}\r\n\t\t\t\t\tpagination\r\n\t\t\t\t\tpaginationPerPage={10}\r\n\t\t\t\t\tstriped={!isDarkTheme}\r\n\t\t\t\t\ttheme={getTheme()}\r\n\t\t\t\t\thighlightOnHover\r\n\t\t\t\t\tnoHeader\r\n\t\t\t\t/>\r\n\t\t\t</div>\r\n\t\t</>\r\n\t);\r\n}\r\n\r\nexport default UserEarning;\r\n", "import React, { useState, useEffect } from \"react\";\r\nimport \"../userpages.css\";\r\nimport { Button, Form, InputGroup, Modal, Tab, Tabs } from \"react-bootstrap\";\r\nimport {\r\n\tAddCredit,\r\n\tGetUserProfile,\r\n\tResetUserPassword,\r\n\tTransferCredit,\r\n} from \"../../../services/backendServices\";\r\nimport { useMutation, useQuery } from \"react-query\";\r\nimport ResponseModal from \"../../components/modal/ResponseModal\";\r\n// import UserInfoStrategyTable from \"../../components/table/UserInfoStrategyTable\";\r\n// import UserInfoOrderTable from \"../../components/table/UserInfoOrderTable\";\r\n// import UserInfoWalletTable from \"../../components/table/UserInfoWalletTable\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport AddBrokerComponent from \"./AddBrokerComponent\";\r\nimport UserInfoOrderTable from \"../../components/table/UserInfoOrderTable\";\r\nimport UserInfoWalletTable from \"./UserInfoWalletTable\";\r\nimport UserInfoStrategyTable from \"../../components/table/UserInfoStrategyTable\";\r\nimport UserReferral from \"./UserReferral\";\r\nimport UserEarning from \"./UserEarning\";\r\nimport { useTheme } from \"../../context/ThemeContext\";\r\n\r\nfunction UserInfo(props) {\r\n\tconst { user_id } = useParams();\r\n\tconst [showAddCreditModal, setShowAddCreditModal] = useState(false);\r\n\tconst [showResetPasswordModal, setShowResetPasswordModal] = useState(false);\r\n\tconst [fetchData, setFetchData] = useState(false);\r\n\tconst [userInfo, setUserInfo] = useState({});\r\n\tconst [apiResponseModal, setApiResponseModal] = useState({\r\n\t\tshow: false,\r\n\t\tres: {},\r\n\t}); // Modal : on API Response\r\n\r\n\tconst [selectedTab, setSelectedTab] = useState(\"strategy\");\r\n\tconst handleTabSelect = (eventKey) => {\r\n\t\tsetSelectedTab(eventKey);\r\n\t};\r\n\r\n\tconst [showPassword, setShowPassword] = useState(false);\r\n\tconst toggleShowPassword = () => {\r\n\t\tsetShowPassword(!showPassword);\r\n\t};\r\n\r\n\t// API CALL to Add Strategy\r\n\tconst mutation = useMutation(\"AddCredit\", (values) =>\r\n\t\tAddCredit(values)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tsetFetchData(true);\r\n\t\t\t\t\tsetShowAddCreditModal(false);\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t})\r\n\t);\r\n\r\n\tfunction handleAddCreditSubmit(event) {\r\n\t\tevent.preventDefault();\r\n\t\tconst amountVal = Number(event.target.amount.value);\r\n\t\tvar values = {\r\n\t\t\tuser_id: user_id,\r\n\t\t\tamount: amountVal,\r\n\t\t\tdescription: event.target.description.value,\r\n\t\t\tis_referral: true,\r\n\t\t};\r\n\t\tmutation.mutate(values);\r\n\t}\r\n\r\n\t// API CALL to Reset Password\r\n\tconst ResetPasswordMutation = useMutation(\"ResetUserPassword\", (values) =>\r\n\t\tResetUserPassword(values)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tsetShowPassword(false);\r\n\t\t\t\t\tsetShowResetPasswordModal(false);\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetApiResponseModal({ show: true, res: res });\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\tsetApiResponseModal({ show: true, res: e });\r\n\t\t\t})\r\n\t);\r\n\tfunction handleResetPasswordSubmit(event) {\r\n\t\tevent.preventDefault();\r\n\t\tvar values = {\r\n\t\t\tuser_id: user_id,\r\n\t\t\tnew_password: event.target.password.value,\r\n\t\t};\r\n\t\tResetPasswordMutation.mutate(values);\r\n\t}\r\n\r\n\tconst fetchUserProfileData = () => {\r\n\t\tGetUserProfile(user_id)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tsetUserInfo(res.data);\r\n\t\t\t\t\t// console.log(\"===\", res.data.broker_data.id);\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t});\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tif (user_id) {\r\n\t\t\tGetUserProfile(user_id)\r\n\t\t\t\t.then((res) => {\r\n\t\t\t\t\tif (res.status) {\r\n\t\t\t\t\t\tsetUserInfo(res.data);\r\n\t\t\t\t\t\t// console.log(\"===\", res.data.broker_data.id);\r\n\t\t\t\t\t\treturn res;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((e) => {\r\n\t\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t\t});\r\n\t\t}\r\n\t}, []);\r\n\r\n\tconst { isDarkTheme } = useTheme()\r\n\treturn (\r\n\t\t<div>\r\n\t\t\t<div className={`card ${isDarkTheme && \"dark-theme-card\"} mb-2`}>\r\n\t\t\t\t<div className=\"card-body py-3\">\r\n\t\t\t\t\t<div className=\"row align-items-center justify-content-between\">\r\n\t\t\t\t\t\t<div className=\"d-flex align-items-center\">\r\n\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\tclassName=\"btn btn-primary p-1\"\r\n\t\t\t\t\t\t\t\tonClick={() => window.close()}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{\" \"}\r\n\t\t\t\t\t\t\t\t<i\r\n\t\t\t\t\t\t\t\t\tclassName=\"mdi mdi-arrow-left-bold m-0\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ fontSize: \"20px\" }}\r\n\t\t\t\t\t\t\t\t></i>{\" \"}\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\tclassName=\"btn btn-primary btn-rounded px-3 py-2 ml-3\"\r\n\t\t\t\t\t\t\t\tonClick={() => setShowAddCreditModal(true)}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tAdd Credit\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\tclassName=\"btn btn-primary btn-rounded px-3 py-2 ml-3\"\r\n\t\t\t\t\t\t\t\tonClick={() => setShowResetPasswordModal(true)}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tReset Password\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<h3 className=\"mb-0 strategy-data-text\">\r\n\t\t\t\t\t\t\t{(userInfo?.email ?? \"\")?.toLowerCase()}\r\n\t\t\t\t\t\t</h3>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t{userInfo && (\r\n\t\t\t\t<AddBrokerComponent\r\n\t\t\t\t\tuserInfo={userInfo}\r\n\t\t\t\t\tuser_id={user_id}\r\n\t\t\t\t\tfetchUserProfileData={fetchUserProfileData}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\r\n\t\t\t<div className=\"row customtab mt-2\">\r\n\t\t\t\t<div className=\"col-lg-12 grid-margin stretch-card\">\r\n\t\t\t\t\t<div className={`card ${isDarkTheme && \"dark-theme-card\"}`}>\r\n\t\t\t\t\t\t<div className=\"card-body\">\r\n\t\t\t\t\t\t\t<Tabs\r\n\t\t\t\t\t\t\t\tdefaultActiveKey=\"strategy\"\r\n\t\t\t\t\t\t\t\tid=\"fill-tab-example\"\r\n\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-nav-tabs\"} mb-3`}\r\n\t\t\t\t\t\t\t\tfill\r\n\t\t\t\t\t\t\t\tjustify\r\n\t\t\t\t\t\t\t\tactiveKey={selectedTab}\r\n\t\t\t\t\t\t\t\tonSelect={handleTabSelect}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Tab eventKey=\"strategy\" title=\"Strategy\" tabClassName={`${isDarkTheme && \"dark-nav-tabs\"}`}>\r\n\t\t\t\t\t\t\t\t\t{user_id && <UserInfoStrategyTable id={user_id} />}\r\n\t\t\t\t\t\t\t\t</Tab>\r\n\t\t\t\t\t\t\t\t<Tab eventKey=\"orders\" title=\"Orders\" tabClassName={`${isDarkTheme && \"dark-nav-tabs\"}`}>\r\n\t\t\t\t\t\t\t\t\t{user_id && <UserInfoOrderTable id={user_id} />}\r\n\t\t\t\t\t\t\t\t</Tab>\r\n\t\t\t\t\t\t\t\t<Tab eventKey=\"wallet\" title=\"Wallet\" tabClassName={`${isDarkTheme && \"dark-nav-tabs\"}`}>\r\n\t\t\t\t\t\t\t\t\t{user_id && (\r\n\t\t\t\t\t\t\t\t\t\t<UserInfoWalletTable\r\n\t\t\t\t\t\t\t\t\t\t\tid={user_id}\r\n\t\t\t\t\t\t\t\t\t\t\tfetchData={fetchData}\r\n\t\t\t\t\t\t\t\t\t\t\tsetFetchData={setFetchData}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</Tab>\r\n\t\t\t\t\t\t\t\t<Tab eventKey=\"referral\" title=\"Referral\" tabClassName={`${isDarkTheme && \"dark-nav-tabs\"}`}>\r\n\t\t\t\t\t\t\t\t\t{user_id && (\r\n\t\t\t\t\t\t\t\t\t\t<UserReferral\r\n\t\t\t\t\t\t\t\t\t\t\tid={user_id}\r\n\t\t\t\t\t\t\t\t\t\t\tfetchData={fetchData}\r\n\t\t\t\t\t\t\t\t\t\t\tsetFetchData={setFetchData}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</Tab>\r\n\t\t\t\t\t\t\t\t<Tab eventKey=\"earning\" title=\"Earning\" tabClassName={`${isDarkTheme && \"dark-nav-tabs\"}`}>\r\n\t\t\t\t\t\t\t\t\t{user_id && (\r\n\t\t\t\t\t\t\t\t\t\t<UserEarning\r\n\t\t\t\t\t\t\t\t\t\t\tid={user_id}\r\n\t\t\t\t\t\t\t\t\t\t\tfetchData={fetchData}\r\n\t\t\t\t\t\t\t\t\t\t\tsetFetchData={setFetchData}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</Tab>\r\n\t\t\t\t\t\t\t</Tabs>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t{showAddCreditModal && (\r\n\t\t\t\t<Modal show={true} animation={true} size=\"md\" className={`${isDarkTheme && \"dark-modal\"} mt-5`}>\r\n\t\t\t\t\t<Form onSubmit={handleAddCreditSubmit}>\r\n\t\t\t\t\t\t<Modal.Header className=\"text-center py-2\">\r\n\t\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t\t<h3 className=\"mb-0\">Add Credit</h3>\r\n\t\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t\t<Modal.Body className={`p-10 ${isDarkTheme && \"border\"}`}>\r\n\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"}`}\r\n\t\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\t\tname=\"amount\"\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"Amount\"\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => e.target.value}\r\n\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"}`}\r\n\t\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\t\tname=\"description\"\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"Description\"\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => e.target.value}\r\n\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t</Modal.Body>\r\n\t\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\t\tonClick={() => setShowAddCreditModal(false)}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button type=\"submit\" className=\"mx-2 px-3\">\r\n\t\t\t\t\t\t\t\t\tAdd\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t\t</Form>\r\n\t\t\t\t</Modal>\r\n\t\t\t)}\r\n\t\t\t{showResetPasswordModal && (\r\n\t\t\t\t<Modal show={true} animation={true} size=\"md\" className={`${isDarkTheme && \"dark-modal\"} mt-5`} >\r\n\t\t\t\t\t<Form onSubmit={handleResetPasswordSubmit}>\r\n\t\t\t\t\t\t<Modal.Header className=\"text-center py-2\">\r\n\t\t\t\t\t\t\t<Modal.Title className=\"text-center\">\r\n\t\t\t\t\t\t\t\t<h3 className=\"mb-0\">Reset Password</h3>\r\n\t\t\t\t\t\t\t</Modal.Title>\r\n\t\t\t\t\t\t</Modal.Header>\r\n\t\t\t\t\t\t<Modal.Body className={`p-10 ${isDarkTheme && \"border\"}`}>\r\n\t\t\t\t\t\t\t<Form.Group>\r\n\t\t\t\t\t\t\t\t<InputGroup>\r\n\t\t\t\t\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"}`}\r\n\t\t\t\t\t\t\t\t\t\ttype={showPassword ? \"text\" : \"password\"}\r\n\t\t\t\t\t\t\t\t\t\tplaceholder=\"Enter Password\"\r\n\t\t\t\t\t\t\t\t\t\tname=\"password\"\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => e.target.value}\r\n\t\t\t\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t<InputGroup.Append>\r\n\t\t\t\t\t\t\t\t\t\t<Button onClick={toggleShowPassword}>\r\n\t\t\t\t\t\t\t\t\t\t\t{showPassword ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<i className=\"mdi mdi-eye\"></i>\r\n\t\t\t\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<i className=\"mdi mdi-eye-off\"></i>\r\n\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t</InputGroup.Append>\r\n\t\t\t\t\t\t\t\t</InputGroup>\r\n\t\t\t\t\t\t\t</Form.Group>\r\n\t\t\t\t\t\t</Modal.Body>\r\n\t\t\t\t\t\t<Modal.Footer className=\"py-1 d-flex justify-content-center\">\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tvariant=\"outline-danger\"\r\n\t\t\t\t\t\t\t\t\tonClick={() => setShowResetPasswordModal(false)}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<Button type=\"submit\" className=\"mx-2 px-3\">\r\n\t\t\t\t\t\t\t\t\tReset\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Modal.Footer>\r\n\t\t\t\t\t</Form>\r\n\t\t\t\t</Modal>\r\n\t\t\t)}\r\n\t\t\t{apiResponseModal.show && (\r\n\t\t\t\t<ResponseModal\r\n\t\t\t\t\tres={apiResponseModal.res}\r\n\t\t\t\t\tsetApiResponseModal={setApiResponseModal}\r\n\t\t\t\t\tmsg={apiResponseModal.res}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t</div>\r\n\t);\r\n}\r\n\r\nexport default UserInfo;\r\n", "import React, { useEffect, useMemo, useState } from \"react\";\r\nimport DataTable from \"react-data-table-component\";\r\nimport {\r\n\tGetLedgerData,\r\n\tGetWalletHistory,\r\n} from \"../../../services/backendServices\";\r\nimport { useQuery, useQueryClient } from \"react-query\";\r\nimport { Form } from \"react-bootstrap\";\r\nimport { useTheme } from \"../../context/ThemeContext\";\r\nimport { getTheme } from \"../ui-helper\";\r\n\r\nfunction UserInfoWalletTable(props) {\r\n\tconst queryClient = useQueryClient();\r\n\tconst [tableDataFormatted, setTableDataFormatted] = useState([]);\r\n\tconst rawUserMap = useMemo(() => new Map(), []);\r\n\r\n\tconst fetchTransactionData = () => {\r\n\t\tGetLedgerData(props.id)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tconst jsonArr = Object.values(res.data);\r\n\t\t\t\t\tsetTableData(jsonArr);\r\n\t\t\t\t\t// Setting row data in rawUserMap with key value\r\n\t\t\t\t\tjsonArr.map((obj, index) => {\r\n\t\t\t\t\t\treturn rawUserMap.set(obj.id, obj);\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t});\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tfetchTransactionData();\r\n\t}, []);\r\n\r\n\tfunction setTableData(tableData) {\r\n\t\tvar rowTableData = [];\r\n\t\ttableData.map((obj, index) => {\r\n\t\t\treturn rowTableData.push(getRowFormatForTable(index, obj));\r\n\t\t});\r\n\t\tsetTableDataFormatted(rowTableData);\r\n\t}\r\n\r\n\tfunction getRowFormatForTable(ind, obj) {\r\n\t\treturn {\r\n\t\t\tindex: ind + 1,\r\n\t\t\tamount: obj.amount,\r\n\t\t\tdescription: obj.description,\r\n\t\t\tadd_time: obj.add_time.split(\".\")[0].replace(\"T\", \" \"),\r\n\t\t\tobj: obj,\r\n\t\t};\r\n\t}\r\n\r\n\tconst columns = [\r\n\t\t{\r\n\t\t\tname: \"Id\",\r\n\t\t\tselector: (row) => row.index,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"65px\",\r\n\t\t\tminWidth: \"65px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Amount\",\r\n\t\t\tselector: (row) => row.amount,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t\tmaxWidth: \"150px\",\r\n\t\t\tminWidth: \"150px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Description\",\r\n\t\t\tselector: (row) => row.description,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Time\",\r\n\t\t\tselector: (row) => row.add_time,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{ name: \"obj\", selector: (row) => row.obj, omit: true },\r\n\t];\r\n\r\n\tconst handleSearch = (e) => {\r\n\t\tvar search_val = e.target.value;\r\n\t\tvar filteredData = [];\r\n\t\tfor (let value of rawUserMap.values()) {\r\n\t\t\tif (\r\n\t\t\t\tvalue.description?.toLowerCase().includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue.add_time\r\n\t\t\t\t\t?.split(\".\")[0]\r\n\t\t\t\t\t.replace(\"T\", \" \")\r\n\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t.includes(search_val?.toLowerCase()) ||\r\n\t\t\t\tvalue?.amount.toString().includes(search_val)\r\n\t\t\t) {\r\n\t\t\t\tfilteredData.push(value);\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetTableDataFormatted(\r\n\t\t\tfilteredData.map((obj, index) => getRowFormatForTable(index, obj))\r\n\t\t);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tif (props.fetchData) {\r\n\t\t\tqueryClient.refetchQueries(\"GetWalletHistory\");\r\n\t\t\tprops.setFetchData(false);\r\n\t\t}\r\n\t}, [props.fetchData]);\r\n\r\n\tconst { isDarkTheme } = useTheme()\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<div className=\"table-responsive mt-3\">\r\n\t\t\t\t<Form.Group>\r\n\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"} mb-2 searchbox-style`}\r\n\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\tplaceholder=\"Search\"\r\n\t\t\t\t\t\tonChange={handleSearch}\r\n\t\t\t\t\t\trequired\r\n\t\t\t\t\t/>\r\n\t\t\t\t</Form.Group>\r\n\t\t\t\t<DataTable\r\n\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\tdata={tableDataFormatted}\r\n\t\t\t\t\tpagination\r\n\t\t\t\t\tpaginationPerPage={10}\r\n\t\t\t\t\tstriped={!isDarkTheme}\r\n\t\t\t\t\ttheme={getTheme()}\r\n\t\t\t\t\thighlightOnHover\r\n\t\t\t\t\tnoHeader\r\n\t\t\t\t/>\r\n\t\t\t</div>\r\n\t\t</>\r\n\t);\r\n}\r\n\r\nexport default UserInfoWalletTable;\r\n", "import React, { useEffect, useMemo, useState } from \"react\";\r\nimport DataTable from \"react-data-table-component\";\r\nimport { GetUserReferralData } from \"../../../services/backendServices\";\r\nimport { useQuery, useQueryClient } from \"react-query\";\r\nimport { Form } from \"react-bootstrap\";\r\nimport { useTheme } from \"../../context/ThemeContext\"\r\nimport { getTheme } from \"../ui-helper\";\r\n\r\nfunction UserReferral(props) {\r\n\tconst queryClient = useQueryClient();\r\n\tconst [tableDataFormatted, setTableDataFormatted] = useState([]);\r\n\tconst rawUserMap = useMemo(() => new Map(), []);\r\n\r\n\tconst fetchTransactionData = () => {\r\n\t\tGetUserReferralData(props.id)\r\n\t\t\t.then((res) => {\r\n\t\t\t\tif (res.status) {\r\n\t\t\t\t\tconst jsonArr = Object.values(res.data);\r\n\t\t\t\t\tsetTableData(jsonArr);\r\n\t\t\t\t\t// Setting row data in rawUserMap with key value\r\n\t\t\t\t\tjsonArr.map((obj, index) => {\r\n\t\t\t\t\t\treturn rawUserMap.set(index, obj);\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn res;\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch((e) => {\r\n\t\t\t\tconsole.log(\"error : \", e);\r\n\t\t\t});\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tfetchTransactionData();\r\n\t}, []);\r\n\r\n\tfunction setTableData(tableData) {\r\n\t\tvar rowTableData = [];\r\n\t\ttableData.map((obj, index) => {\r\n\t\t\treturn rowTableData.push(getRowFormatForTable(index, obj));\r\n\t\t});\r\n\t\tsetTableDataFormatted(rowTableData);\r\n\t}\r\n\r\n\tfunction getRowFormatForTable(ind, obj) {\r\n\t\treturn {\r\n\t\t\tindex: ind + 1,\r\n\t\t\temail: obj.email,\r\n\t\t\tfirst_name: obj.first_name,\r\n\t\t\tlast_name: obj.last_name,\r\n\t\t\tmobile_no: obj.mobile_no,\r\n\t\t\tobj: obj,\r\n\t\t};\r\n\t}\r\n\r\n\tconst columns = [\r\n\t\t{\r\n\t\t\tname: \"Id\",\r\n\t\t\tselector: (row) => row.index,\r\n\t\t\tsortable: true,\r\n\t\t\tmaxWidth: \"65px\",\r\n\t\t\tminWidth: \"65px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Email\",\r\n\t\t\tselector: (row) => row.email,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t\tmaxWidth: \"150px\",\r\n\t\t\tminWidth: \"150px\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"First Name\",\r\n\t\t\tselector: (row) => row.first_name,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Last Name\",\r\n\t\t\tselector: (row) => row.last_name,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Mobile No\",\r\n\t\t\tselector: (row) => row.mobile_no,\r\n\t\t\tsortable: true,\r\n\t\t\twrap: true,\r\n\t\t},\r\n\t\t{ name: \"obj\", selector: (row) => row.obj, omit: true },\r\n\t];\r\n\r\n\tconst handleSearch = (e) => {\r\n\t\tvar search_val = e.target.value;\r\n\t\tvar filteredData = [];\r\n\r\n\t\tif (\r\n\t\t\tsearch_val.trim() === \"\" ||\r\n\t\t\tsearch_val === null ||\r\n\t\t\tsearch_val === undefined\r\n\t\t) {\r\n\t\t\t// If search value is empty, show all data\r\n\t\t\tfilteredData = Array.from(rawUserMap.values());\r\n\t\t} else {\r\n\t\t\t// Filter data based on search value\r\n\t\t\tfor (let value of rawUserMap.values()) {\r\n\t\t\t\tif (\r\n\t\t\t\t\tvalue.email?.toLowerCase().includes(search_val.toLowerCase()) ||\r\n\t\t\t\t\tvalue.first_name?.toLowerCase().includes(search_val.toLowerCase()) ||\r\n\t\t\t\t\tvalue?.last_name\r\n\t\t\t\t\t\t.toString()\r\n\t\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t\t.includes(search_val?.toLowerCase()) ||\r\n\t\t\t\t\tvalue?.mobile_no\r\n\t\t\t\t\t\t.toString()\r\n\t\t\t\t\t\t?.toLowerCase()\r\n\t\t\t\t\t\t.includes(search_val?.toLowerCase())\r\n\t\t\t\t) {\r\n\t\t\t\t\tfilteredData.push(value);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tsetTableDataFormatted(\r\n\t\t\tfilteredData.map((obj, index) => getRowFormatForTable(index, obj))\r\n\t\t);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tif (props.fetchData) {\r\n\t\t\tprops.setFetchData(false);\r\n\t\t}\r\n\t}, [props.fetchData]);\r\n\r\n\r\n\tconst { isDarkTheme } = useTheme()\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<div className=\"table-responsive mt-3\">\r\n\t\t\t\t<Form.Group>\r\n\t\t\t\t\t<Form.Control\r\n\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\tplaceholder=\"Search\"\r\n\t\t\t\t\t\tclassName={`${isDarkTheme && \"dark-form-control\"} mb-2 searchbox-style`}\r\n\t\t\t\t\t\tonChange={handleSearch}\r\n\t\t\t\t\t\trequired\r\n\t\t\t\t\t/>\r\n\t\t\t\t</Form.Group>\r\n\t\t\t\t<DataTable\r\n\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\tdata={tableDataFormatted}\r\n\t\t\t\t\tpagination\r\n\t\t\t\t\tpaginationPerPage={10}\r\n\t\t\t\t\tstriped={!isDarkTheme}\r\n\t\t\t\t\ttheme={getTheme()}\r\n\t\t\t\t\thighlightOnHover\r\n\t\t\t\t\tnoHeader\r\n\t\t\t\t/>\r\n\t\t\t</div>\r\n\t\t</>\r\n\t);\r\n}\r\n\r\nexport default UserReferral;\r\n", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar _excluded = [\"bsPrefix\", \"size\", \"hasValidation\", \"className\", \"as\"];\nimport classNames from 'classnames';\nimport React from 'react';\nimport createWithBsPrefix from './createWithBsPrefix';\nimport { useBootstrapPrefix } from './ThemeProvider';\nvar InputGroupAppend = createWithBsPrefix('input-group-append');\nvar InputGroupPrepend = createWithBsPrefix('input-group-prepend');\nvar InputGroupText = createWithBsPrefix('input-group-text', {\n  Component: 'span'\n});\n\nvar InputGroupCheckbox = function InputGroupCheckbox(props) {\n  return /*#__PURE__*/React.createElement(InputGroupText, null, /*#__PURE__*/React.createElement(\"input\", _extends({\n    type: \"checkbox\"\n  }, props)));\n};\n\nvar InputGroupRadio = function InputGroupRadio(props) {\n  return /*#__PURE__*/React.createElement(InputGroupText, null, /*#__PURE__*/React.createElement(\"input\", _extends({\n    type: \"radio\"\n  }, props)));\n};\n\n/**\n *\n * @property {InputGroupAppend} Append\n * @property {InputGroupPrepend} Prepend\n * @property {InputGroupText} Text\n * @property {InputGroupRadio} Radio\n * @property {InputGroupCheckbox} Checkbox\n */\nvar InputGroup = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var bsPrefix = _ref.bsPrefix,\n      size = _ref.size,\n      hasValidation = _ref.hasValidation,\n      className = _ref.className,\n      _ref$as = _ref.as,\n      Component = _ref$as === void 0 ? 'div' : _ref$as,\n      props = _objectWithoutPropertiesLoose(_ref, _excluded);\n\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'input-group');\n  return /*#__PURE__*/React.createElement(Component, _extends({\n    ref: ref\n  }, props, {\n    className: classNames(className, bsPrefix, size && bsPrefix + \"-\" + size, hasValidation && 'has-validation')\n  }));\n});\nInputGroup.displayName = 'InputGroup';\nInputGroup.Text = InputGroupText;\nInputGroup.Radio = InputGroupRadio;\nInputGroup.Checkbox = InputGroupCheckbox;\nInputGroup.Append = InputGroupAppend;\nInputGroup.Prepend = InputGroupPrepend;\nexport default InputGroup;"], "names": ["UserInfoOrderTable", "props", "tableDataFormatted", "setTableDataFormatted", "useState", "rawUserMap", "useMemo", "Map", "selectedDate", "setSelectedDate", "Date", "setTableData", "tableData", "rowTableData", "map", "obj", "index", "push", "getRowFormatForTable", "ind", "name", "ref_name", "trading_symbol", "exchange", "price", "type", "getTransactionType", "action_type", "order_time", "split", "replace", "qty", "quantity", "useQuery", "GetUserOrders", "id", "format", "then", "res", "jsonArr", "Object", "values", "data", "set", "catch", "e", "console", "log", "columns", "selector", "row", "sortable", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "wrap", "sortFunction", "actionTypeSortFunction", "a", "b", "omit", "isDarkTheme", "useTheme", "_jsxs", "_Fragment", "children", "className", "_jsx", "Form", "Group", "controlId", "concat", "DatePicker", "selected", "onChange", "date", "dateFormat", "<PERSON><PERSON>", "onClick", "handleShowOrder", "Control", "placeholder", "search_val", "target", "value", "filteredData", "_value$ref_name", "_value$trading_symbol", "_value$exchange", "_value$action_type", "_value$order_time", "_value$order_time$spl", "_value$order_id", "_value$emsg", "toLowerCase", "includes", "order_id", "emsg", "toString", "required", "DataTable", "pagination", "paginationPerPage", "striped", "theme", "getTheme", "highlightOnHover", "<PERSON><PERSON><PERSON><PERSON>", "UserInfoStrategyTable", "queryClient", "useQueryClient", "apiResponseModal", "setApiResponseModal", "show", "fetchData", "setFetchData", "showUpdateStrategySubscriptionPopup", "setShowUpdateStrategySubscriptionPopup", "status", "lots", "trading", "FetchApiData", "GetStrategySubscription", "subscription_start_date", "start_date", "slice", "subscription_end_date", "expiry_date", "is_expired", "action", "editMutation", "useMutation", "UpdateStrategySubscription", "isExpiredSortFunction", "tradingStatusSortFunction", "useEffect", "refetchQueries", "_value$start_date", "_value$expiry_date", "_value$strategy_id", "_value$strategy_id$to", "_value$lots", "_value$lots$toString", "_value$amount", "statusVal", "tradingValue", "strategy_id", "amount", "ResponseModal", "msg", "Modal", "animation", "size", "onSubmit", "event", "preventDefault", "lotVal", "Number", "originalDate", "year", "month", "day", "convertDateFormat", "ss_id", "strategy_trading", "checked", "mutate", "Header", "Title", "Body", "Label", "defaultValue", "Check", "defaultChecked", "htmlFor", "Footer", "variant", "AddBrokerComponent", "_props$userInfo$broke", "_props$userInfo$broke2", "_props$userInfo$broke3", "_props$userInfo$broke4", "_props$userInfo", "_props$userInfo$broke5", "_props$userInfo$broke6", "showDeletePopup", "setShowDeletePopup", "showBrokerConnection", "setShowBrokerConnection", "brokerData", "setBrokerData", "isFetching", "setFetching", "initialFormValues", "nic_name", "mobile_no", "email", "formValues", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "options", "setOptions", "selectedValueForBrokerName", "setSelectedValueForBrokerName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "setAvailableBroke<PERSON><PERSON>ields", "handleChange", "prevV<PERSON><PERSON>", "available_broker_res", "GetAvailableBrokers", "options_arr", "key", "label", "addBrokerMutation", "AddBroker", "fetchUserProfileData", "getAuthCodeFromURL", "url", "auth_key", "URLSearchParams", "substring", "indexOf", "get", "otpModel", "setOtpModel", "form_data", "diconnect_mutation", "RemoveBroker", "userInfo", "broker_data", "update_mutation", "UpdateBrokerTradingFlag", "username", "broker_name", "toUpperCase", "broker_flag", "disabled", "trading_flag", "async", "user_id", "flag_val", "client_id", "api_key", "redirect_uri", "TOKEN_URL", "response_type", "state", "urlParams", "popup", "window", "open", "checkToken", "setInterval", "closed", "clearInterval", "location", "href", "auth_code", "custForm", "app_id", "is_link_login", "close", "error", "handleFyersTokenGeneration", "v", "handleZerodhaTokenGeneration", "app_key", "handleFlattradeTokenGeneration", "Select", "classNamePrefix", "selectedOption", "isSearchable", "styles", "mergeStyles", "hoverEffectOnSelect", "customStylesForSelect", "pattern", "title", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "broker", "brokerFields", "fieldsToShow", "entries", "filter", "_ref", "_", "field", "_ref2", "fieldName", "renderFormInputField", "rows", "i", "length", "rowFields", "OTPForBrokerConnectionModel", "mutationFunction", "<PERSON><PERSON><PERSON>", "UserEarning", "fetchTransactionData", "GetUserEarningData", "description", "add_time", "trim", "undefined", "Array", "from", "_value$description", "_value$add_time", "_value$add_time$split", "UserInfo", "_userInfo$email", "useParams", "showAddCreditModal", "setShowAddCreditModal", "showResetPasswordModal", "setShowResetPasswordModal", "setUserInfo", "selectedTab", "setSelectedTab", "showPassword", "setShowPassword", "mutation", "AddCredit", "ResetPasswordMutation", "ResetUserPassword", "GetUserProfile", "style", "fontSize", "Tabs", "defaultActiveKey", "fill", "justify", "active<PERSON><PERSON>", "onSelect", "eventKey", "Tab", "tabClassName", "UserInfoWalletTable", "UserReferral", "amountVal", "is_referral", "new_password", "password", "InputGroup", "Append", "toggleShowPassword", "GetLedgerData", "GetUserReferralData", "first_name", "last_name", "_value$email", "_value$first_name", "_value$last_name$toSt", "_value$mobile_no$toSt", "_excluded", "InputGroupAppend", "createWithBsPrefix", "InputGroupPrepend", "InputGroupText", "Component", "React", "ref", "bsPrefix", "hasValidation", "_ref$as", "as", "_objectWithoutPropertiesLoose", "useBootstrapPrefix", "_extends", "classNames", "displayName", "Text", "Radio", "Checkbox", "Prepend"], "sourceRoot": ""}
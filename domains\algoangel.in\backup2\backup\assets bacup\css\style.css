@charset "UTF-8";
/*------------------------------------------------------------------
[Table of contents]
@File: Erudex Template Style
[ Default Index Number ]
- Defult css with Helper css
    -- Before this comment ( Start Main CSS Style ) defult css continue .
- helper css
- page-bg
- preloader
- navbar
- side-modal-wrapper
- header
- innder-page-header
- feature-section
- about-section
- service-section
- forum-details
- sidebar
- recent-article
- process-section
- partner-section
- testimonial-section
- blog-section
- newsletter-section
- footer
- pricing-section
- product-review-box
- post-comment-area
- map-section
- product-section
- product-details-section
- project-section
- search-overlay
- scroll-top
- contact-info-section
- contact-form-section
- accordion-section
- related-product-section
- cart-section
- checkout-section
- account-page-section
- authentication-section
- error-section
- coming-soon-section
-------------------------------------------------------------------*/
/*------------------------------------------------------------------
[Typography]
	[ This template has one font typography ]
		Typography 1: 'Roboto', sans-serif;
		Typography 2: 'Poppins', sans-serif;
-------------------------------------------------------------------*/
/*************** IMPORT FONTS ***************/
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;900&amp;display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700;900&amp;display=swap");
/*************** COLOR VARIABLE ***************/
/*************** DEFAULT CSS ***************/
body {
  font-family: "Roboto", sans-serif;
  background-color: #fff;
  font-size: 16px;
  color: #767676;
}

@media (prefers-reduced-motion: no-preference) {
  :root {
    scroll-behavior: inherit;
  }
}

/*************** HELPER CSS ***************/
.mt-10 {
  margin-top: 10px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-100 {
  margin-top: 100px;
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-100 {
  margin-bottom: 100px;
}

.pt-10 {
  padding-top: 10px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-70 {
  padding-top: 70px;
}

.pt-80 {
  padding-top: 80px;
}

.pt-min-100 {
  padding-top: calc(100px - 8px);
}

.pt-100 {
  padding-top: calc(100px - 8px);
}

.pb-30 {
  padding-bottom: 30px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pb-70 {
  padding-bottom: 70px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pb-130 {
  padding-bottom: 130px;
}

.pb-160 {
  padding-bottom: 160px;
}

.pb-170 {
  padding-bottom: 170px;
}

.pb-200 {
  padding-bottom: 200px;
}

.p-tb-50 {
  padding-top: 50px;
  padding-bottom: 50px;
}

.p-tb-100 {
  padding-top: 100px;
  padding-bottom: 100px;
}

.sec-pt-70 {
  padding-top: 70px;
}

.sec-pb-40 {
  padding-bottom: 40px;
}

.sec-pb-70 {
  padding-bottom: 70px;
}

.no-animation {
  -webkit-animation: none !important;
          animation: none !important;
}

.around-border {
  border: 1px solid #e1e1e1;
}

p {
  color: #767676;
  line-height: 1.6;
}

a {
  color: #1f12fd;
  text-decoration: none;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

a:hover, a:active {
  text-decoration: none;
  color: #1f12fd;
}

a:hover.redirect-link i, a:active.redirect-link i {
  -webkit-transform: translateX(5px);
          transform: translateX(5px);
}

a:focus {
  outline: 0;
}

a.redirect-link {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

a.redirect-link i {
  -webkit-transition: -webkit-transform 0.3s cubic-bezier(0.25, 0.55, 0.4, 2.1) 0s;
  transition: -webkit-transform 0.3s cubic-bezier(0.25, 0.55, 0.4, 2.1) 0s;
  transition: transform 0.3s cubic-bezier(0.25, 0.55, 0.4, 2.1) 0s;
  transition: transform 0.3s cubic-bezier(0.25, 0.55, 0.4, 2.1) 0s, -webkit-transform 0.3s cubic-bezier(0.25, 0.55, 0.4, 2.1) 0s;
  margin-left: 5px;
}

h1, h2, h3, h4, h5, h6 {
  color: #000;
  font-weight: 600;
  font-family: "Poppins", sans-serif;
}

.fluid-height {
  height: calc(100% - 30px);
  margin-bottom: 30px;
}

.full-height {
  height: 100% !important;
}

.full-width {
  width: 100%;
}

.text-justify {
  text-align: justify;
}

.width-225 {
  width: 225px;
}

.border-radius-0 {
  border-radius: 0 !important;
}

img {
  max-width: 100%;
  height: auto;
}

.max-530 {
  max-width: 530px;
}

.max-670 {
  max-width: 670px;
}

.border-radius-3 {
  border-radius: 3px;
}

.border-top {
  border-top: 1px solid #f4f8ff !important;
}

.border-bottom {
  border-bottom: 1px solid rgba(133, 133, 133, 0.5) !important;
}

.border-bottom-1 {
  border-bottom: 1px solid #eee;
}

.default-box-shadow {
  -webkit-box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
          box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
}

button {
  outline: 0;
  border: 0;
}

button:focus, button:active {
  outline: 0;
  border: 0;
}

.main-btn {
  padding: 15px 50px;
  text-align: center;
  color: #fff;
  font-size: 15px;
  font-weight: bold;
  -webkit-transition: all .3s linear;
  transition: all .3s linear;
  border: 0;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  position: relative;
  border-radius: 35px;
  z-index: 0 !important;
  background-color: #1f12fd;
  -webkit-box-shadow: 0px 7px 51px 0px rgba(31, 18, 253, 0.12);
          box-shadow: 0px 7px 51px 0px rgba(31, 18, 253, 0.12);
}

.main-btn i {
  font-size: 15px;
}

.main-btn:focus, .main-btn:active {
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 0;
}

.main-btn:hover {
  color: #fff;
  background-color: #1509d8;
}

.main-btn.main-btn-small {
  padding: 10px 15px;
  font-size: 13px;
}

.main-btn.main-btn-rounded-icon span {
  background-color: #fff;
  color: #1f12fd;
  padding: 1px 5px;
  border-radius: 20px;
  margin-left: 10px;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.main-btn.main-btn-white {
  background-color: #fff;
  color: #1f12fd;
}

.main-btn.main-btn-white:hover {
  background-color: #1f12fd;
  color: #fff;
}

.main-btn.main-btn-white:hover.main-btn-rounded-icon span {
  background-color: #fff;
  color: #1f12fd;
}

.main-btn.main-btn-white.main-btn-rounded-icon span {
  background-color: #1f12fd;
  color: #fff;
}

.main-btn.main-btn-black {
  background-color: #000;
  color: #fff;
}

.main-btn.main-btn-black:hover {
  background-color: #1f12fd;
  color: #000;
}

.main-btn:disabled, .main-btn.disabled {
  opacity: 1;
}

.main-btn.btn-fb {
  background-color: #3B5998;
  color: #fff;
}

.main-btn.btn-tw {
  background-color: #00ACEE;
  color: #fff;
}

.main-btn.btn-ins {
  background-color: #DD2A7B;
  color: #fff;
}

.main-btn.btn-yt {
  background-color: #C4302B;
  color: #fff;
}

.main-btn.btn-vim {
  background-color: #86C9EF;
  color: #fff;
}

.no-radius {
  border-radius: 0;
}

.no-radius.btn, .no-radius .btn {
  border-radius: 0;
}

.no-radius.btn:before, .no-radius .btn:before {
  border-radius: 0;
}

.no-radius.btn:after, .no-radius .btn:after {
  border-radius: 0;
}

.bg-white {
  background: #fff;
}

.bg-main {
  background: #1f12fd;
}

.bg-off-white {
  background-color: #f4f8ff;
}

.bg-white {
  background: #fff;
}

.bg-black {
  background: #161616;
}

.bg-blue-dark {
  background-color: #0f0886 !important;
}

.color-maincolor {
  color: #1f12fd !important;
}

.color-white {
  color: #fff !important;
}

.z-index-1 {
  z-index: 1;
}

.section-title {
  max-width: 575px;
  text-align: center;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 50px;
}

.section-title small {
  display: inline-block;
  font-size: 18px;
  margin-bottom: 15px;
  font-weight: 500;
  color: #1f12fd;
}

.section-title h2 {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 16px;
  position: relative;
  line-height: 1.4;
}

.section-title h2 span {
  color: #1f12fd;
}

.section-title h2:last-child {
  margin-bottom: 0;
}

.section-title p {
  font-size: 17px;
  font-weight: 400;
  margin-bottom: 30px;
  position: relative;
}

.section-title p:last-child {
  margin-bottom: 0;
}

.section-title p a {
  color: #1f12fd;
}

.section-title.section-title-lg {
  max-width: 825px;
}

.section-title.section-title-white small {
  color: #fff;
}

.section-title.section-title-white h2 {
  color: #fff;
}

.section-title.section-title-white p {
  color: #fff;
}

.section-title.section-title-white p a {
  color: #fff;
}

.section-title.section-title-left {
  max-width: 100%;
  text-align: left;
}

.sub-section-title {
  margin-bottom: 30px;
}

.sub-section-title .sub-section-title-heading {
  font-size: 22px;
  margin-bottom: 15px;
}

.sub-section-title p {
  font-size: 16px;
  font-weight: 400;
}

.breadcrumb {
  background-color: transparent;
  padding: 0;
  margin-bottom: 0;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.breadcrumb .breadcrumb-item {
  font-size: 16px;
  font-weight: 500;
  padding-left: 0;
  padding-right: .5rem;
  color: #fff;
}

.breadcrumb .breadcrumb-item a {
  color: #fff;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.breadcrumb .breadcrumb-item a:hover {
  color: #fff;
}

.breadcrumb .breadcrumb-item.active {
  color: #fff;
}

.breadcrumb .breadcrumb-item + .breadcrumb-item:before {
  color: #fff;
  font-size: 10px;
  content: "\f130";
  font-family: Flaticon;
  padding-top: 4px;
}

.breadcrumb .breadcrumb-item:last-child {
  padding-right: 0;
}

.page-link {
  position: relative;
  padding: 0 0;
  padding-top: 2px;
  width: 40px;
  height: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-left: -1px;
  line-height: 1;
  color: #1f12fd;
  font-weight: 600;
  font-size: 16px;
  border-radius: 4px;
  background-color: white;
  -webkit-box-shadow: 0px 8px 35px 0px rgba(32, 32, 32, 0.08);
          box-shadow: 0px 8px 35px 0px rgba(32, 32, 32, 0.08);
  border: 0;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  margin-top: 3px;
  overflow: hidden;
}

.page-link:hover {
  background-color: #1f12fd;
  color: #fff;
}

.page-link:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.page-link i {
  font-size: 10px;
  font-weight: 600;
}

.page-pagination .pagination {
  margin-top: -3px;
  margin-bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding-left: 0;
  padding-right: 0;
}

.page-item {
  padding: 0 7px;
  margin-top: 3px;
}

.page-item:first-child {
  padding-left: 0;
}

.page-item:first-child .page-link {
  background-color: #fff;
  color: #1f12fd;
}

.page-item:first-child .page-link:hover {
  background-color: #1f12fd;
  color: #fff;
}

.page-item:last-child {
  padding-right: 0;
}

.page-item:last-child .page-link {
  background-color: transparent;
  color: #1f12fd;
}

.page-item:last-child .page-link:hover {
  background-color: #1f12fd;
  color: #fff;
}

.page-item.disabled .page-link {
  background-color: #fff;
  color: #1f12fd;
  border: 0;
}

.page-item.active .page-link {
  background-color: #1f12fd;
  color: #fff;
}

.form-group label {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #000;
}

.text-muted {
  font-size: 13px;
  color: #767676 !important;
  margin-top: 5px;
}

.input-group {
  border: 0;
  border-radius: 3px;
  padding: 15px 25px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
  border-radius: 3px;
  background-color: #efefef;
}

.input-group.input-group-maincolor .form-control {
  color: #fff;
}

.input-group.input-group-maincolor .form-control::-webkit-input-placeholder {
  color: #fff;
}

.input-group.input-group-maincolor .form-control:-ms-input-placeholder {
  color: #fff;
}

.input-group.input-group-maincolor .form-control::-ms-input-placeholder {
  color: #fff;
}

.input-group.input-group-maincolor .form-control::placeholder {
  color: #fff;
}

.form-control {
  padding: 15px 25px;
  color: #000;
  font-size: 15px;
  background-color: #f4f8ff;
  height: auto;
  border: 1px solid #eee;
  border-radius: 30px;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.form-control:focus, .form-control:active {
  -webkit-box-shadow: none;
          box-shadow: none;
  background-color: #f4f8ff;
}

.form-control::-webkit-input-placeholder {
  color: #585858;
}

.form-control:-ms-input-placeholder {
  color: #585858;
}

.form-control::-ms-input-placeholder {
  color: #585858;
}

.form-control::placeholder {
  color: #585858;
}

.form-control.form-control-white {
  color: #fff;
}

.form-control.form-control-white::-webkit-input-placeholder {
  color: #585858;
}

.form-control.form-control-white:-ms-input-placeholder {
  color: #585858;
}

.form-control.form-control-white::-ms-input-placeholder {
  color: #585858;
}

.form-control.form-control-white::placeholder {
  color: #585858;
}

select.form-control {
  background-image: url(../images/chevron.png);
  background-repeat: no-repeat;
  background-position: right 25px center;
  background-size: 15px;
}

.help-block {
  font-size: 14px;
  margin-top: 5px;
}

.help-block.with-errors {
  color: #ff5421;
}

#msgSubmit {
  color: #ff5421 !important;
  font-weight: 600;
  font-size: 18px;
}

#msgSubmit.submit-post-info {
  margin-top: 5px;
}

.input-checkbox label {
  position: relative;
  padding-left: 35px;
  color: #767676;
  margin-bottom: 0;
  font-size: 15px;
}

.input-checkbox label a {
  color: #1f12fd;
}

.input-checkbox label a:hover {
  color: #1f12fd;
}

.input-checkbox label:before {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  border: 1px solid #e1e1e1;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  font-size: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  left: 0;
  padding-left: 4px;
  line-height: 1;
  top: 1px;
  border-radius: 5px;
}

.input-checkbox input {
  display: none;
}

.input-checkbox input:checked ~ label:before {
  color: #fff;
  content: "\f110";
  font-family: Flaticon;
  background-color: #1f12fd;
  border-color: #1f12fd;
}

.input-radio label {
  position: relative;
  padding-left: 35px;
  color: #767676;
  margin-bottom: 0;
  font-size: 15px;
}

.input-radio label a {
  color: #1f12fd;
}

.input-radio label:before {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  border: 1px solid #B5B5B5;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  font-size: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  left: 0;
  padding-left: 4px;
  line-height: 1;
  top: -2px;
}

.input-radio input {
  display: none;
}

.input-radio input:checked ~ label:before {
  color: #E6E6E6;
  content: "\f11c";
  font-family: Flaticon;
  font-size: 10px;
}

.input-radio.input-radio-white label {
  color: #B5B5B5;
}

.social-list {
  padding-left: 0;
  padding-right: 0;
  margin-bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.social-list li {
  list-style: none;
  font-size: 18px;
  margin-right: 15px;
}

.social-list li a {
  color: #000;
}

.social-list li a:hover {
  color: #1f12fd;
}

.social-list li:last-child {
  margin-right: 0;
}

.social-list.social-list-btn li {
  width: 40px;
  height: 40px;
  font-size: 15px;
  -webkit-box-shadow: 0px 6px 21px 0px rgba(32, 32, 32, 0.08);
          box-shadow: 0px 6px 21px 0px rgba(32, 32, 32, 0.08);
}

.social-list.social-list-btn li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #fff;
  color: #585858;
  border-radius: 3px;
}

.social-list.social-list-btn li a:hover {
  color: #1f12fd;
}

.social-list.social-list-sm li {
  font-size: 14px;
}

.page-bg {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

.page-bg-overlay {
  position: relative;
}

.page-bg-overlay:before {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(31, 18, 253, 0.58);
}

.page-bg-1 {
  background-image: url(../images/page-bg/page-bg-1.jpg);
}

.page-bg-2 {
  background-image: url(../images/page-bg/page-bg-2.jpg);
}

/*************** MAIN CSS ***************/
.preloader {
  height: 100%;
  width: 100%;
  position: fixed;
  overflow: visible;
  z-index: 99999;
  top: 0;
  left: 0;
  background-color: #000;
}

.preloader .preloader-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.preloader-content {
  width: 300px;
  height: 300px;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  background-color: #000;
  -webkit-filter: blur(10px) contrast(20);
          filter: blur(10px) contrast(20);
}

.blob-1, .blob-2 {
  width: 70px;
  height: 70px;
  position: absolute;
  background: #fff;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

.blob-1 {
  left: 20%;
  -webkit-animation: osc-l 2.5s ease infinite;
          animation: osc-l 2.5s ease infinite;
}

.blob-2 {
  left: 80%;
  -webkit-animation: osc-r 2.5s ease infinite;
          animation: osc-r 2.5s ease infinite;
  background: #1f12fd;
}

@-webkit-keyframes osc-l {
  0% {
    left: 20%;
  }
  50% {
    left: 50%;
  }
  100% {
    left: 20%;
  }
}

@keyframes osc-l {
  0% {
    left: 20%;
  }
  50% {
    left: 50%;
  }
  100% {
    left: 20%;
  }
}

@-webkit-keyframes osc-r {
  0% {
    left: 80%;
  }
  50% {
    left: 50%;
  }
  100% {
    left: 80%;
  }
}

@keyframes osc-r {
  0% {
    left: 80%;
  }
  50% {
    left: 50%;
  }
  100% {
    left: 80%;
  }
}

.pre-loaded .preloader {
  visibility: hidden;
  opacity: 0;
  -webkit-transition: all 0.3s 1s ease-out;
  transition: all 0.3s 1s ease-out;
}

.fixed-top.non-fixed {
  position: relative;
}

.topbar {
  padding-left: 25px;
  padding-right: 25px;
}

.topbar-inner {
  margin-left: auto;
  margin-right: auto;
  padding-top: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e1e1e1;
}

.topbar-item {
  width: auto;
}

.topbar-item .social-list {
  margin-bottom: 15px;
}

.topbar-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding-left: 0;
  padding-right: 0;
  margin-bottom: 0;
}

.topbar-list li {
  list-style: none;
  font-size: 15px;
  color: #767676;
  margin-right: 25px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-right: 1px solid #e1e1e1;
  padding-top: 3px;
  padding-bottom: 3px;
  padding-right: 25px;
}

.topbar-list li:last-child {
  padding-right: 0;
  border-right: 0;
  margin-right: 0;
}

.topbar-list li i {
  font-size: 18px;
  margin-right: 10px;
  color: #1f12fd;
}

.topbar-list li a {
  color: #000;
}

.topbar-list li a:hover {
  color: #1f12fd;
}

.search-option {
  cursor: pointer;
}

.side-modal-wrapper {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: #1f12fd;
  top: 0;
  left: 0;
  z-index: 1111;
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.side-modal-wrapper.side-modal-wrapper-show {
  opacity: 1;
  pointer-events: all;
  visibility: visible;
}

.side-modal {
  width: 300px;
  height: 100%;
  right: 0;
  top: 0;
  background-color: #fff;
  position: absolute;
  padding: 30px;
  -webkit-transform: translateX(100%);
          transform: translateX(100%);
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.side-modal.side-modal-show {
  -webkit-transform: translateX(0);
          transform: translateX(0);
}

.side-modal-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.side-modal-close {
  font-size: 20px;
  color: #000;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  cursor: pointer;
}

.side-modal-close:hover {
  color: #1f12fd;
}

.side-topbar-option {
  display: none;
}

.side-modal-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-bottom: 40px;
}

.side-modal-close {
  font-size: 20px;
  color: #000;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  cursor: pointer;
}

.side-modal-close:hover {
  color: #1f12fd;
}

.sidebar-info-content {
  margin-bottom: 40px;
}

.sidebar-info-content:last-child {
  margin-bottom: 0;
}

.sidebar-info-content h3 {
  font-size: 24px;
  font-family: "Roboto", sans-serif;
  color: #000;
  margin-bottom: 20px;
}

.sidebar-info-list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 16px;
  color: #767676;
  margin-bottom: 15px;
}

.sidebar-info-list-item:last-child {
  margin-bottom: 0;
}

.sidebar-info-list-item i {
  font-size: 18px;
  color: #1f12fd;
  margin-right: 10px;
}

.sidebar-info-list-item a {
  color: #767676;
}

.sidebar-info-list-item a:hover {
  color: #1f12fd;
}

.sidebar-search {
  padding: 20px;
  background-color: #1f12fd;
}

.sidebar-search .form-group {
  margin-bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  background-color: #fff;
  padding: 13px 18px;
  border-radius: 30px;
}

.sidebar-search .form-group .form-control {
  background-color: transparent;
  padding: 0;
  border: 0;
  padding-right: 10px;
}

.sidebar-search .form-group button {
  padding: 0;
  background-color: transparent;
  font-size: 17px;
  color: #000;
}

@-webkit-keyframes menuItem {
  0% {
    opacity: 0;
    -webkit-transform: rotateX(-90deg);
            transform: rotateX(-90deg);
  }
  50% {
    -webkit-transform: rotateX(-20deg);
            transform: rotateX(-20deg);
  }
  100% {
    opacity: 1;
    -webkit-transform: rotateX(0deg);
            transform: rotateX(0deg);
  }
}

@keyframes menuItem {
  0% {
    opacity: 0;
    -webkit-transform: rotateX(-90deg);
            transform: rotateX(-90deg);
  }
  50% {
    -webkit-transform: rotateX(-20deg);
            transform: rotateX(-20deg);
  }
  100% {
    opacity: 1;
    -webkit-transform: rotateX(0deg);
            transform: rotateX(0deg);
  }
}

@-webkit-keyframes menuItem2 {
  0% {
    opacity: 0;
    -webkit-transform: rotateY(-90deg);
            transform: rotateY(-90deg);
  }
  50% {
    -webkit-transform: rotateY(-20deg);
            transform: rotateY(-20deg);
  }
  100% {
    opacity: 1;
    -webkit-transform: rotateY(0deg);
            transform: rotateY(0deg);
  }
}

@keyframes menuItem2 {
  0% {
    opacity: 0;
    -webkit-transform: rotateY(-90deg);
            transform: rotateY(-90deg);
  }
  50% {
    -webkit-transform: rotateY(-20deg);
            transform: rotateY(-20deg);
  }
  100% {
    opacity: 1;
    -webkit-transform: rotateY(0deg);
            transform: rotateY(0deg);
  }
}

.main-nav {
  margin: auto;
  position: relative;
  display: none;
  background-color: transparent;
}

.main-nav nav .navbar-nav .nav-item {
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  padding-top: 10px;
  padding-bottom: 10px;
}

.main-nav nav .navbar-nav .nav-item a {
  color: #000;
  margin-left: 0;
  margin-right: 0;
  padding-left: 15px;
  padding-right: 15px;
  font-size: 16px;
  text-transform: none;
  font-family: "Poppins", sans-serif;
  font-weight: 500;
}

.main-nav nav .navbar-nav .nav-item a.dropdown-toggle:after {
  content: "\f121";
  border: 0;
  font-family: Flaticon;
  font-size: 10px;
  vertical-align: baseline;
  margin-left: 8px;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.main-nav nav .navbar-nav .nav-item a.active {
  color: #1f12fd;
}

.main-nav nav .navbar-nav .nav-item a.active:hover {
  color: #1f12fd;
}

.main-nav nav .navbar-nav .nav-item:hover a {
  color: #1f12fd;
}

.main-nav nav .navbar-nav .nav-item:hover a.active {
  color: #1f12fd;
}

.main-nav nav .navbar-nav .nav-item:hover a.dropdown-toggle:after {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item {
  -webkit-animation-name: menuItem;
          animation-name: menuItem;
  -webkit-animation-fill-mode: forwards;
          animation-fill-mode: forwards;
  -webkit-animation-timing-function: ease-in-out;
          animation-timing-function: ease-in-out;
  -webkit-animation-duration: 300ms;
          animation-duration: 300ms;
  -webkit-transform-origin: top center;
          transform-origin: top center;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(1) {
  -webkit-animation-delay: 60ms;
          animation-delay: 60ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(2) {
  -webkit-animation-delay: 120ms;
          animation-delay: 120ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(3) {
  -webkit-animation-delay: 180ms;
          animation-delay: 180ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(4) {
  -webkit-animation-delay: 240ms;
          animation-delay: 240ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(5) {
  -webkit-animation-delay: 300ms;
          animation-delay: 300ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(6) {
  -webkit-animation-delay: 360ms;
          animation-delay: 360ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(7) {
  -webkit-animation-delay: 420ms;
          animation-delay: 420ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(8) {
  -webkit-animation-delay: 480ms;
          animation-delay: 480ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(9) {
  -webkit-animation-delay: 540ms;
          animation-delay: 540ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(10) {
  -webkit-animation-delay: 600ms;
          animation-delay: 600ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(11) {
  -webkit-animation-delay: 660ms;
          animation-delay: 660ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(12) {
  -webkit-animation-delay: 720ms;
          animation-delay: 720ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(13) {
  -webkit-animation-delay: 780ms;
          animation-delay: 780ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(14) {
  -webkit-animation-delay: 840ms;
          animation-delay: 840ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(15) {
  -webkit-animation-delay: 900ms;
          animation-delay: 900ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(16) {
  -webkit-animation-delay: 960ms;
          animation-delay: 960ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(17) {
  -webkit-animation-delay: 1020ms;
          animation-delay: 1020ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(18) {
  -webkit-animation-delay: 1080ms;
          animation-delay: 1080ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(19) {
  -webkit-animation-delay: 1140ms;
          animation-delay: 1140ms;
}

.main-nav nav .navbar-nav .nav-item:hover .dropdown-menu .nav-item:nth-child(20) {
  -webkit-animation-delay: 1200ms;
          animation-delay: 1200ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu {
  background-color: #fff;
  border: 0;
  margin: 0;
  -webkit-box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
          box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
  padding: 10px 0;
  width: 230px;
  border-radius: 0;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item {
  opacity: 0;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item a {
  color: #000;
  font-weight: 500;
  font-size: 14px;
  padding: 5px 15px;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  text-transform: none !important;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item a.dropdown-toggle:after {
  content: "\f10f";
  border: 0;
  font-family: Flaticon;
  font-size: 10px;
  vertical-align: baseline;
  margin-left: 0;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 10px;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item a.active {
  color: #1f12fd;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item a.active:hover {
  color: #1f12fd;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > a {
  color: #1f12fd;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > a.active {
  color: #1f12fd;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu {
  top: -7px !important;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item {
  -webkit-animation-name: menuItem2;
          animation-name: menuItem2;
  -webkit-animation-fill-mode: forwards;
          animation-fill-mode: forwards;
  -webkit-animation-timing-function: ease-in-out;
          animation-timing-function: ease-in-out;
  -webkit-animation-duration: 300ms;
          animation-duration: 300ms;
  -webkit-transform-origin: top center;
          transform-origin: top center;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(1) {
  -webkit-animation-delay: 60ms;
          animation-delay: 60ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(2) {
  -webkit-animation-delay: 120ms;
          animation-delay: 120ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(3) {
  -webkit-animation-delay: 180ms;
          animation-delay: 180ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(4) {
  -webkit-animation-delay: 240ms;
          animation-delay: 240ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(5) {
  -webkit-animation-delay: 300ms;
          animation-delay: 300ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(6) {
  -webkit-animation-delay: 360ms;
          animation-delay: 360ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(7) {
  -webkit-animation-delay: 420ms;
          animation-delay: 420ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(8) {
  -webkit-animation-delay: 480ms;
          animation-delay: 480ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(9) {
  -webkit-animation-delay: 540ms;
          animation-delay: 540ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(10) {
  -webkit-animation-delay: 600ms;
          animation-delay: 600ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(11) {
  -webkit-animation-delay: 660ms;
          animation-delay: 660ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(12) {
  -webkit-animation-delay: 720ms;
          animation-delay: 720ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(13) {
  -webkit-animation-delay: 780ms;
          animation-delay: 780ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(14) {
  -webkit-animation-delay: 840ms;
          animation-delay: 840ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(15) {
  -webkit-animation-delay: 900ms;
          animation-delay: 900ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(16) {
  -webkit-animation-delay: 960ms;
          animation-delay: 960ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(17) {
  -webkit-animation-delay: 1020ms;
          animation-delay: 1020ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(18) {
  -webkit-animation-delay: 1080ms;
          animation-delay: 1080ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(19) {
  -webkit-animation-delay: 1140ms;
          animation-delay: 1140ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(20) {
  -webkit-animation-delay: 1200ms;
          animation-delay: 1200ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu {
  left: 100%;
  top: 0;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item {
  opacity: 0;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item a {
  color: #000;
  font-weight: 400;
  font-size: 14px;
  padding: 5px 10px;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item a.dropdown-toggle:after {
  content: "\f10f";
  border: 0;
  font-family: Flaticon;
  font-size: 10px;
  vertical-align: baseline;
  margin-left: 0;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 10px;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item a.active {
  color: #1f12fd;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > a {
  color: #1f12fd;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item {
  -webkit-animation-name: menuItem;
          animation-name: menuItem;
  -webkit-animation-fill-mode: forwards;
          animation-fill-mode: forwards;
  -webkit-animation-timing-function: ease-in-out;
          animation-timing-function: ease-in-out;
  -webkit-animation-duration: 300ms;
          animation-duration: 300ms;
  -webkit-transform-origin: top center;
          transform-origin: top center;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(1) {
  -webkit-animation-delay: 60ms;
          animation-delay: 60ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(2) {
  -webkit-animation-delay: 120ms;
          animation-delay: 120ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(3) {
  -webkit-animation-delay: 180ms;
          animation-delay: 180ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(4) {
  -webkit-animation-delay: 240ms;
          animation-delay: 240ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(5) {
  -webkit-animation-delay: 300ms;
          animation-delay: 300ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(6) {
  -webkit-animation-delay: 360ms;
          animation-delay: 360ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(7) {
  -webkit-animation-delay: 420ms;
          animation-delay: 420ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(8) {
  -webkit-animation-delay: 480ms;
          animation-delay: 480ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(9) {
  -webkit-animation-delay: 540ms;
          animation-delay: 540ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(10) {
  -webkit-animation-delay: 600ms;
          animation-delay: 600ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(11) {
  -webkit-animation-delay: 660ms;
          animation-delay: 660ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(12) {
  -webkit-animation-delay: 720ms;
          animation-delay: 720ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(13) {
  -webkit-animation-delay: 780ms;
          animation-delay: 780ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(14) {
  -webkit-animation-delay: 840ms;
          animation-delay: 840ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(15) {
  -webkit-animation-delay: 900ms;
          animation-delay: 900ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(16) {
  -webkit-animation-delay: 960ms;
          animation-delay: 960ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(17) {
  -webkit-animation-delay: 1020ms;
          animation-delay: 1020ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(18) {
  -webkit-animation-delay: 1080ms;
          animation-delay: 1080ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(19) {
  -webkit-animation-delay: 1140ms;
          animation-delay: 1140ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > .dropdown-menu .nav-item:nth-child(20) {
  -webkit-animation-delay: 1200ms;
          animation-delay: 1200ms;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item .dropdown-menu {
  left: 100%;
  top: 0 !important;
}

.main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item {
  opacity: 0;
}

.main-nav .main-btn {
  padding: 14px 40px;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.navbar {
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 0;
}

.navbar-collapse {
  width: auto;
}

.navbar-option-item {
  margin-left: 30px;
}

.navbar-option-item a i {
  font-size: 25px;
}

.navbar-option {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}

.navbar-brand {
  padding-top: 0;
  padding-bottom: 0;
  margin-right: 15px;
  position: relative;
}

.mobile-nav {
  padding: 20px 0;
  display: block;
}

.mobile-nav .navbar-option {
  margin-left: auto;
  margin-right: 60px;
  z-index: 1000;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 0;
}

.mobile-nav .navbar-option-item {
  margin-left: 25px;
}

.mobile-nav .navbar-option-item button {
  color: #000;
  background-color: transparent;
  padding-left: 0;
  padding-right: 0;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.mobile-nav .navbar-option-item button i {
  font-size: 20px;
  vertical-align: middle;
}

.mobile-nav .navbar-option-item button:hover {
  color: #1f12fd;
}

.mobile-brand {
  position: relative;
  z-index: 1000;
}

.mean-container {
  position: relative;
}

.mean-container .mean-bar {
  position: absolute;
  height: 100%;
  top: 0;
  left: 0;
  padding: 0;
  border-bottom: 0;
}

.mean-container a.meanmenu-reveal {
  padding-top: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  height: auto;
  padding-right: 0;
  color: #1f12fd;
}

.mean-container a.meanmenu-reveal span {
  background-color: #1f12fd;
}

.mean-container .mean-nav {
  margin-top: 53px;
}

.mean-container .mean-nav .navbar-nav {
  max-height: 350px;
  overflow: auto;
  -webkit-box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
          box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
}

.mean-container .mean-nav .navbar-nav .nav-item a {
  color: #000;
}

.mean-container .mean-nav .navbar-nav .nav-item a.active {
  color: #1f12fd;
}

.navbar-area {
  padding-left: 25px;
  padding-right: 25px;
}

.navbar-area.is-sticky {
  -webkit-animation: 500ms running fadeInDown;
          animation: 500ms running fadeInDown;
  -webkit-transition: all .5s;
  transition: all .5s;
  width: 100%;
  z-index: 999999;
  position: fixed;
  background-color: #fff;
  top: 0;
  -webkit-box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
          box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
}

.navbar-area.is-sticky .main-nav nav .navbar-nav .nav-item a {
  color: #000;
}

.navbar-area.is-sticky .main-nav nav .navbar-nav .nav-item a.active {
  color: #1f12fd;
}

.navbar-area.is-sticky .main-nav nav .navbar-nav .nav-item a.active:hover {
  color: #1f12fd;
}

.navbar-area.is-sticky .main-nav nav .navbar-nav .nav-item:hover a {
  color: #1f12fd;
}

.navbar-area.is-sticky .main-nav nav .navbar-nav .nav-item:hover a.active {
  color: #1f12fd;
}

.navbar-area.is-sticky .main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item a {
  color: #000;
}

.navbar-area.is-sticky .main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item a.active {
  color: #1f12fd;
}

.navbar-area.is-sticky .main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item a.active:hover {
  color: #1f12fd;
}

.navbar-area.is-sticky .main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > a {
  color: #1f12fd;
}

.navbar-area.is-sticky .main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item:hover > a.active {
  color: #1f12fd;
}

.navbar-area.is-sticky .main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item a {
  color: #000;
}

.navbar-area.is-sticky .main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item a.active {
  color: #1f12fd;
}

.navbar-area.is-sticky .main-nav nav .navbar-nav .nav-item .dropdown-menu .nav-item .dropdown-menu .nav-item:hover > a {
  color: #1f12fd;
}

.navbar-area.is-sticky .navbar-option-search a {
  color: #000;
}

.navbar-area.is-sticky .navbar-option-search a:hover {
  color: #1f12fd;
}

.max-585 {
  max-width: 585px;
}

.header-bg {
  position: relative;
  padding-top: 240px;
  padding-bottom: 100px;
}

.header-bg-lg {
  position: relative;
}

.header-bottom-space {
  padding-bottom: 200px;
}

.header-bg-image {
  position: relative;
  background: url(../images/homepage-three/bg.jpg) no-repeat right;
  background-size: cover;
}

.header-bg-image:before {
  content: "";
  width: 100%;
  height: 100%;
  background-color: rgba(31, 18, 253, 0.4);
  position: absolute;
  top: 0;
  left: 0;
}

.header-bg-shape {
  position: relative;
  background-image: url(../images/homepage-one/header-bg-shape.png);
  background-repeat: no-repeat;
  background-position: center bottom;
}

@-webkit-keyframes scale {
  0% {
    -webkit-transform: scale(0);
            transform: scale(0);
    opacity: 0;
  }
  50% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
  }
  80% {
    -webkit-transform: scale(1.5);
            transform: scale(1.5);
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(2);
            transform: scale(2);
    opacity: 0;
  }
}

@keyframes scale {
  0% {
    -webkit-transform: scale(0);
            transform: scale(0);
    opacity: 0;
  }
  50% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
  }
  80% {
    -webkit-transform: scale(1.5);
            transform: scale(1.5);
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(2);
            transform: scale(2);
    opacity: 0;
  }
}

.header-animation-shape {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background-color: #fd6a12;
  position: absolute;
  left: 50%;
  bottom: 0;
  -webkit-transform: translate(-400px, -100px);
          transform: translate(-400px, -100px);
}

.header-animation-shape:before {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background-color: rgba(253, 106, 18, 0.5);
  -webkit-animation: scale 1s infinite linear;
          animation: scale 1s infinite linear;
  border-radius: 50%;
}

.header-image-shape {
  position: absolute;
  top: 50%;
  right: 0;
  -webkit-transform: translateY(-45%);
          transform: translateY(-45%);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.inner-page-header {
  background-color: #0d0594;
  padding-top: 249px;
  padding-bottom: 100px;
}

.inner-header-content {
  text-align: center;
}

.inner-header-content h1 {
  font-size: 36px;
  color: #fff;
  margin-bottom: 20px;
}

.header-content {
  position: relative;
  z-index: 11;
  padding-left: 25px;
}

.header-content h1 {
  margin-bottom: 10px;
}

.header-content p {
  font-size: 18px;
  margin-bottom: 25px;
}

.header-content-white h1 {
  color: #fff;
}

.header-content-white p {
  color: #fff;
}

.button-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-top: -20px;
}

.button-group .main-btn {
  margin-right: 25px;
  margin-top: 20px;
}

.button-group .main-btn:last-child {
  margin-right: 0;
}

.section-to-header {
  margin-top: -100px;
  position: relative;
  z-index: 11;
}

.feature-card {
  padding: 30px;
  border-radius: 10px;
  -webkit-box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
          box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  background-color: #fff;
}

.feature-card:hover {
  -webkit-transform: translateY(-5px);
          transform: translateY(-5px);
}

.feature-card-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-top: -15px;
  margin-bottom: 20px;
}

.feature-card-header span {
  color: #000;
  font-size: 22px;
  margin-top: 15px;
  font-weight: 600;
}

.feature-card-thumb {
  border-radius: 10px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 60px;
  height: 60px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-right: 20px;
  margin-top: 15px;
  font-size: 35px;
}

.feature-card-thumb.feature-card-thumb-green {
  background-color: rgba(53, 231, 149, 0.15);
  color: #56c5b2;
}

.feature-card-thumb.feature-card-thumb-green-dark {
  background-color: #56c5b2;
  color: #fff;
}

.feature-card-thumb.feature-card-thumb-yellow {
  background-color: rgba(232, 201, 30, 0.15);
  color: #e8c91e;
}

.feature-card-thumb.feature-card-thumb-yellow-dark {
  background-color: #e8c91e;
  color: #fff;
}

.feature-card-thumb.feature-card-thumb-blue {
  background-color: rgba(48, 140, 211, 0.15);
  color: #308cd3;
}

.feature-card-thumb.feature-card-thumb-blue-dark {
  background-color: #1f12fd;
  color: #fff;
}

.feature-card-thumb.feature-card-thumb-violet {
  background-color: rgba(186, 76, 249, 0.15);
  color: #ba4cf9;
}

.feature-card-thumb.feature-card-thumb-violet-dark {
  background-color: #ba4cf9;
  color: #fff;
}

.feature-card-thumb.feature-card-thumb-lightgreen-dark {
  background-color: #35e795;
  color: #fff;
}

.feature-card-thumb-wrapper {
  border: 1px solid #fff;
  border-radius: 50%;
  padding: 10px;
  width: 40px;
  height: 40px;
}

.feature-card-thumb-wrapper i {
  font-size: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.feature-card-body h3 {
  font-size: 22px;
  line-height: 1.4;
  margin-bottom: 10px;
}

.feature-card-body p {
  margin-bottom: 15px;
}

.feature-card-body p:last-child {
  margin-bottom: 0;
}

.feature-card-body a {
  font-size: 14px;
  font-weight: 600;
}

.about-item-details .section-title {
  margin-bottom: 18px;
}

.about-content p {
  margin-bottom: 25px;
}

.about-content p:last-child {
  margin-bottom: 0;
}

.about-content ul {
  padding-left: 0;
  padding-right: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-bottom: 5px;
}

.about-content ul li {
  position: relative;
  font-size: 16px;
  margin-bottom: 10px;
  color: #000;
  list-style: none;
  padding-left: 30px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 50%;
  padding-right: 10px;
}

.about-content ul li:before {
  content: "\eed9";
  font-family: IcoFont;
  position: absolute;
  left: 0;
  top: -3px;
  font-size: 20px;
  color: #1f12fd;
}

.service-card {
  border-radius: 10px;
  -webkit-box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
          box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
  padding: 30px;
  position: relative;
  overflow: hidden;
}

.service-card:before {
  content: "";
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #1f12fd;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  z-index: -1;
}

.service-card:hover:before {
  opacity: 1;
  width: 100%;
  height: 100%;
  border-radius: 0;
}

.service-card:hover .service-card-body h3 {
  color: #fff;
}

.service-card:hover .service-card-body p {
  color: #fff;
}

.service-card:hover .service-card-body a {
  color: #fff;
}

.service-card-thumb {
  border-radius: 10px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 125px;
  height: 125px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 65px;
  background-color: #f6f6ff;
  color: #1f12fd;
  margin-bottom: 25px;
}

.service-card-body h3 {
  font-size: 22px;
  line-height: 1.4;
  margin-bottom: 15px;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  -webkit-transition-delay: 0.1s;
          transition-delay: 0.1s;
}

.service-card-body p {
  margin-bottom: 15px;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  -webkit-transition-delay: 0.1s;
          transition-delay: 0.1s;
}

.service-card-body p:last-child {
  margin-bottom: 0;
}

.service-card-body a {
  font-size: 15px;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  -webkit-transition-delay: 0.1s;
          transition-delay: 0.1s;
  font-weight: 500;
}

.service-card-2 {
  padding-bottom: 23px;
  border-radius: 10px;
  -webkit-box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
          box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
  background-color: #fff;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  overflow: hidden;
}

.service-card-2:hover {
  -webkit-transform: translateY(-10px);
          transform: translateY(-10px);
}

.service-card-2:hover .service-card-image-2 img {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}

.service-card-2:hover .service-card-text-2 h3 a {
  color: #1f12fd;
  text-decoration: underline;
}

.service-card-image-2 {
  overflow: hidden;
  margin-bottom: 23px;
  position: relative;
  background-color: #1f12fd;
}

.service-card-image-2:before {
  content: "";
  background-image: url(../images/frame-1.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: -1px;
  z-index: 11;
}

.service-card-image-2 img {
  -webkit-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out;
}

.service-category-2 {
  color: #1f12fd;
  font-size: 15px;
  font-weight: 700;
  margin-bottom: 18px;
}

.service-card-text-2 {
  padding-left: 25px;
  padding-right: 25px;
}

.service-card-text-2 h3 {
  font-size: 22px;
  line-height: 1.4;
  margin-bottom: 13px;
}

.service-card-text-2 h3 a {
  color: #000;
}

.service-card-text-2 p {
  font-size: 18px;
  margin-bottom: 15px;
}

.service-card-text-2 p:last-child {
  margin-bottom: 0;
}

.forum-details img {
  margin-bottom: 30px;
  border-radius: 10px;
}

.forum-details h1 {
  font-size: 35px;
  margin-bottom: 20px;
}

.forum-details h2 {
  font-size: 30px;
  margin-bottom: 20px;
}

.forum-details h3 {
  font-size: 22px;
  margin-bottom: 20px;
}

.forum-details h4 {
  font-size: 20px;
  margin-bottom: 20px;
}

.forum-details h5 {
  font-size: 18px;
  margin-bottom: 20px;
}

.forum-details h6 {
  font-size: 16px;
  margin-bottom: 20px;
}

.forum-details p {
  margin-bottom: 20px;
}

.forum-details p:last-child {
  margin-bottom: 0;
}

.forum-details a {
  color: #1f12fd;
}

.forum-details a:hover {
  color: #1f12fd;
}

.forum-details ul {
  padding-left: 0;
  padding-right: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-bottom: 20px;
  margin-top: -10px;
}

.forum-details ul:last-child {
  margin-bottom: 0;
}

.forum-details ul li {
  position: relative;
  font-size: 17px;
  margin-top: 10px;
  color: #000;
  list-style: none;
  padding-left: 30px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 50%;
  padding-right: 10px;
}

.forum-details ul li:before {
  content: "\eed9";
  font-family: IcoFont;
  position: absolute;
  left: 0;
  top: -3px;
  font-size: 20px;
  color: #1f12fd;
}

.forum-details ol {
  padding-left: 20px;
  padding-right: 0;
  margin-top: -10px;
  margin-bottom: 20px;
}

.forum-details ol:last-child {
  margin-bottom: 0;
}

.forum-details ol li {
  font-size: 17px;
  color: #767676;
  margin-top: 10px;
}

.forum-details ol li:last-child {
  margin-bottom: 0;
}

.forum-details blockquote {
  margin-bottom: 25px;
  border-radius: 10px;
  border-left: 5px solid #1f12fd;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
  border-right: 1px solid #eee;
  -webkit-box-shadow: 0px 7px 40px 0px rgba(32, 32, 32, 0.05);
          box-shadow: 0px 7px 40px 0px rgba(32, 32, 32, 0.05);
  padding: 30px;
}

.forum-details blockquote:last-child {
  margin-bottom: 0;
}

.forum-details blockquote p {
  font-size: 18px;
  margin-bottom: 15px;
  font-style: italic;
  font-weight: 500;
}

.forum-details blockquote span {
  font-style: 17px;
  color: #000;
}

.forum-details .blog-entry-list {
  margin-top: -10px;
  margin-bottom: 15px;
}

.forum-details .blog-entry-list li {
  padding-left: 0;
  margin-bottom: 0;
  margin-top: 10px;
}

.forum-details .blog-entry-list li:before {
  content: none;
}

.forum-details .forum-table {
  overflow: auto;
  margin-bottom: 23px;
}

.forum-details .forum-table::-webkit-scrollbar {
  height: 5px;
}

.forum-details .forum-table::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.forum-details .forum-table::-webkit-scrollbar-thumb {
  background: gainsboro;
}

.forum-details .forum-table:last-child {
  margin-bottom: 0;
}

.forum-details .forum-table table {
  width: 100%;
}

.forum-details .forum-table table thead tr {
  background: #1f12fd;
}

.forum-details .forum-table table thead tr th {
  font-size: 15px;
  color: #000;
  font-weight: 500;
  padding: 20px;
  white-space: nowrap;
  color: #fff;
}

.forum-details .forum-table table tbody tr {
  border-bottom: 1px solid rgba(209, 200, 212, 0.5);
}

.forum-details .forum-table table tbody tr:last-child {
  border-bottom: 0;
}

.forum-details .forum-table table tbody tr td {
  font-size: 15px;
  color: #767676;
  font-weight: 400;
  padding: 10px 20px;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  white-space: nowrap;
}

.sidebar-bg {
  background-color: #f4f8ff;
  padding: 25px;
  border-radius: 10px;
}

.sidebar-item {
  margin-bottom: 30px;
}

.sidebar-item:last-child {
  margin-bottom: 0;
}

.sidebar-item h3 {
  margin-bottom: 25px;
  font-size: 24px;
}

.details-sidebar-search .form-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-radius: 10px;
  overflow: hidden;
}

.details-sidebar-search .form-group .form-control {
  border: 0;
  background-color: #fff;
  border-radius: 0;
}

.details-sidebar-search .form-group .form-control::-webkit-input-placeholder {
  color: #585858;
}

.details-sidebar-search .form-group .form-control:-ms-input-placeholder {
  color: #585858;
}

.details-sidebar-search .form-group .form-control::-ms-input-placeholder {
  color: #585858;
}

.details-sidebar-search .form-group .form-control::placeholder {
  color: #585858;
}

.details-sidebar-search .form-group button {
  padding-left: 20px;
  padding-right: 20px;
  background-color: #1f12fd;
  font-style: 20px;
  color: #fff;
}

.sidebar-title h3 {
  font-size: 20px;
  font-weight: 700;
  color: #000;
  margin-bottom: 20px;
}

.sidebar-list {
  padding-left: 0;
  padding-right: 0;
  margin-bottom: 0;
}

.sidebar-list li {
  list-style: none;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 500;
}

.sidebar-list li:last-child {
  margin-bottom: 0;
}

.sidebar-list li a {
  color: #000;
  position: relative;
  padding-left: 12px;
}

.sidebar-list li a:before {
  content: "•";
  margin-right: 10px;
  position: absolute;
  left: 0;
  font-size: 14px;
  padding-top: 2px;
  color: #8881ff;
}

.sidebar-list li a:hover {
  color: #1f12fd;
}

.sidebar-list li a.active {
  color: #1f12fd;
}

.sidebar-category {
  padding-left: 0;
  padding-right: 0;
  margin-bottom: 0;
}

.sidebar-category li {
  list-style: none;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 500;
}

.sidebar-category li:last-child {
  margin-bottom: 0;
}

.sidebar-category li a {
  color: #000;
  position: relative;
  padding-left: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.sidebar-category li a:before {
  content: "•";
  margin-right: 10px;
  position: absolute;
  left: 0;
  font-size: 14px;
  padding-top: 2px;
  color: #8881ff;
}

.sidebar-category li a:hover {
  color: #1f12fd;
}

.sidebar-category li a.active {
  color: #1f12fd;
}

.sidebar-author p {
  font-style: 18px;
  margin-bottom: 20px;
  color: #fff;
}

.sidebar-author .author-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.sidebar-author .author-info img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 75px;
  height: 75px;
  border-radius: 50%;
  margin-right: 20px;
}

.sidebar-author .author-info-details h3 {
  font-size: 20px;
  margin-bottom: 5px;
  color: #fff;
}

.sidebar-author .author-info-details p {
  font-size: 16px;
  margin-bottom: 0;
  color: #fff;
}

.sidebar-recent-post-item {
  margin-bottom: 30px;
}

.sidebar-recent-post-item:last-child {
  margin-bottom: 0;
}

.sidebar-recent-post-item a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.sidebar-recent-post-item a img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 120px;
  height: 120px;
  -o-object-fit: cover;
     object-fit: cover;
  margin-right: 20px;
  border-radius: 10px;
}

.sidebar-recent-post-item a:hover .recent-post-details h3 {
  color: #1f12fd;
}

.recent-article-item {
  margin-bottom: 20px;
}

.recent-article-item:last-child {
  margin-bottom: 0;
}

.recent-article-item a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.recent-article-item a:hover .recent-article-thumb img {
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}

.recent-article-item a:hover .recent-article-content h3 {
  color: #1f12fd;
}

.recent-article-thumb {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 90px;
  margin-right: 15px;
  overflow: hidden;
}

.recent-article-thumb img {
  width: 100%;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.recent-article-content h3 {
  font-size: 16px;
  font-weight: 700;
  color: #000;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.recent-post-details h3 {
  font-size: 18px;
  margin-bottom: 10px;
  line-height: 1.4;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.recent-post-details .post-entry {
  padding-left: 0;
  padding-right: 0;
  margin-bottom: 0;
}

.recent-post-details .post-entry li {
  list-style: none;
  font-size: 16px;
  color: #1f12fd;
}

.process-content {
  position: relative;
}

@-webkit-keyframes stepDotRide {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
    opacity: 0;
  }
  20% {
    -webkit-transform: translateX(100px);
            transform: translateX(100px);
    opacity: 1;
  }
  35% {
    -webkit-transform: translateX(300px);
            transform: translateX(300px);
    opacity: 1;
  }
  50% {
    -webkit-transform: translateX(500px);
            transform: translateX(500px);
    opacity: 1;
  }
  70% {
    -webkit-transform: translateX(700px);
            transform: translateX(700px);
    opacity: 1;
  }
  85% {
    -webkit-transform: translateX(900px);
            transform: translateX(900px);
    opacity: 1;
  }
  100% {
    -webkit-transform: translateX(950px);
            transform: translateX(950px);
    opacity: 0;
  }
}

@keyframes stepDotRide {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
    opacity: 0;
  }
  20% {
    -webkit-transform: translateX(100px);
            transform: translateX(100px);
    opacity: 1;
  }
  35% {
    -webkit-transform: translateX(300px);
            transform: translateX(300px);
    opacity: 1;
  }
  50% {
    -webkit-transform: translateX(500px);
            transform: translateX(500px);
    opacity: 1;
  }
  70% {
    -webkit-transform: translateX(700px);
            transform: translateX(700px);
    opacity: 1;
  }
  85% {
    -webkit-transform: translateX(900px);
            transform: translateX(900px);
    opacity: 1;
  }
  100% {
    -webkit-transform: translateX(950px);
            transform: translateX(950px);
    opacity: 0;
  }
}

.process-content-line {
  width: 65%;
  height: 2px;
  background: #fff;
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  top: 43px;
}

.process-item:hover .process-thumb img {
  -webkit-transform: translateY(-10px);
          transform: translateY(-10px);
}

.process-item.border {
  padding: 30px;
  background-color: #fff;
}

.process-item-center .process-thumb {
  margin-bottom: 20px;
  position: relative;
}

.process-item-center .process-thumb:before {
  background-position: center;
}

.process-thumb {
  margin-bottom: 20px;
  position: relative;
}

.process-thumb:before {
  content: "";
  position: absolute;
  left: 0;
  background-image: url(../images/process/process-shape.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 100px;
  height: 100%;
  background-position: center;
}

.process-thumb img {
  position: relative;
  -webkit-transition: -webkit-transform 0.3s cubic-bezier(0.25, 0.55, 0.4, 2.1) 0s;
  transition: -webkit-transform 0.3s cubic-bezier(0.25, 0.55, 0.4, 2.1) 0s;
  transition: transform 0.3s cubic-bezier(0.25, 0.55, 0.4, 2.1) 0s;
  transition: transform 0.3s cubic-bezier(0.25, 0.55, 0.4, 2.1) 0s, -webkit-transform 0.3s cubic-bezier(0.25, 0.55, 0.4, 2.1) 0s;
}

.process-text h3 {
  font-size: 22px;
  line-height: 1.4;
  margin-bottom: 10px;
}

.process-text p {
  margin-bottom: 12px;
}

.process-text p:last-child {
  margin-bottom: 0;
}

.partner-carousel .item {
  text-align: center;
}

.partner-carousel .item img {
  margin: auto;
  width: auto;
}

.partner-carousel-opacity .item img {
  opacity: 0.5;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.partner-carousel-opacity .item img:hover {
  opacity: 1;
}

@-webkit-keyframes wobble {
  50% {
    border-radius: 750px 550px 350px 750px / 350px 750px 550px 450px;
  }
  100% {
    border-radius: 750px 250px 750px 250px / 250px 750px 250px 750px;
  }
}

@keyframes wobble {
  50% {
    border-radius: 750px 550px 350px 750px / 350px 750px 550px 450px;
  }
  100% {
    border-radius: 750px 250px 750px 250px / 250px 750px 250px 750px;
  }
}

.section-inner-center-shape {
  width: 420px;
  height: 420px;
  background-color: #f2f2f3;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(-100%, -50%);
          transform: translate(-100%, -50%);
  left: 50%;
  -webkit-animation: wobble 5s ease-in-out alternate infinite;
          animation: wobble 5s ease-in-out alternate infinite;
}

.testimonial-carousel {
  margin-top: -30px;
}

.testimonial-carousel .owl-stage-outer {
  padding-top: 30px;
}

.testimonial-card-body {
  padding: 30px;
  border-radius: 10px;
  -webkit-box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
          box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
  position: relative;
  margin-bottom: 40px;
  background-color: #fff;
}

.testimonial-card-body:after {
  content: "";
  border-style: solid;
  border-color: #fff transparent transparent;
  border-width: 30px;
  position: absolute;
  top: 100%;
  left: 58px;
}

.testimonial-card-inner-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 20px;
}

.testimonial-card-inner-header h3 {
  font-size: 22px;
  margin-bottom: 0;
}

.testimonial-quote {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 40px;
  height: 40px;
  background-color: #e7e5ff;
  color: #1f12fd;
  border-radius: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 20px;
  -webkit-border-radius: 40px;
  -moz-border-radius: 40px;
  -ms-border-radius: 40px;
  -o-border-radius: 40px;
}

.testimonial-para {
  font-size: 17px;
  margin-bottom: 15px;
}

.testimonial-card-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  padding-left: 50px;
  margin-top: -10px;
}

.testimonial-card-info-thumb {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #1f12fd;
  margin-right: 15px;
  margin-top: 10px;
  overflow: hidden;
}

.testimonial-card-info-thumb img {
  width: 100%;
  height: 100%;
}

.testimonial-card-info-text {
  margin-top: 10px;
}

.testimonial-name {
  font-size: 20px;
  margin-bottom: 10px;
}

.testimonial-address {
  font-size: 16px;
  margin-bottom: 10px;
}

.testimonial-address:last-child {
  margin-bottom: 0;
}

.default-carousel.owl-theme .owl-nav {
  margin: 0;
  margin-top: 50px;
}

.default-carousel.owl-theme .owl-nav button {
  font-size: 18px;
  width: 50px;
  height: 50px;
  border: 1px solid #e1e1e1;
  color: #1f12fd;
  border-radius: 50%;
  margin: 0;
  margin-right: 15px;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.default-carousel.owl-theme .owl-nav button:hover, .default-carousel.owl-theme .owl-nav button:focus, .default-carousel.owl-theme .owl-nav button:active {
  background-color: #1f12fd;
  border-color: #1f12fd;
  color: #fff;
}

.default-carousel.owl-theme .owl-nav button:last-child {
  margin-right: 0;
}

.blog-card {
  padding: 10px 10px 23px;
  border-radius: 10px;
  -webkit-box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
          box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
  background-color: #fff;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  position: relative;
}

.blog-card:hover {
  -webkit-transform: translateY(-10px);
          transform: translateY(-10px);
}

.blog-card:hover .blog-card-image img {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}

.blog-card:hover .blog-card-text h3 a {
  color: #1f12fd;
  text-decoration: underline;
}

.blog-card:hover .blog-card-text .redirect-link {
  color: #1f12fd;
}

.blog-card-image {
  border-radius: 10px 10px 0 0;
  overflow: hidden;
  margin-bottom: 23px;
  position: relative;
}

.blog-card-image img {
  -webkit-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out;
}

.blog-category {
  padding: 6px 20px;
  background-color: #e7e5ff;
  color: #1f12fd;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 18px;
  display: inline-block;
  border-radius: 25px;
}

.blog-category-text {
  color: #1f12fd;
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 13px;
}

.blog-card-text {
  padding-left: 15px;
  padding-right: 15px;
}

.blog-card-text h3 {
  font-size: 22px;
  line-height: 1.4;
  margin-bottom: 20px;
}

.blog-card-text h3 a {
  color: #000;
}

.blog-card-text p {
  font-size: 18px;
  margin-bottom: 15px;
}

.blog-card-text p:last-child {
  margin-bottom: 0;
}

.blog-card-text .redirect-link {
  font-size: 15px;
  font-weight: 400;
  color: #000;
}

.blog-card-entry {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.blog-entry-thumb {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 50px;
  height: 50px;
  overflow: hidden;
  border-radius: 50%;
  margin-right: 10px;
}

.blog-entry-thumb img {
  width: 100%;
  height: 100%;
}

.blog-entry-text h4 {
  font-size: 16px;
  margin-bottom: 5px;
}

.blog-entry-text h4 strong {
  font-weight: 500;
}

.blog-entry-text p {
  font-size: 14px;
  color: #1f12fd;
  margin-bottom: 0;
}

.card-video {
  width: 90px;
  height: 90px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

.card-video a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #fff;
  color: #1f12fd;
  border-radius: 50%;
  font-size: 26px;
}

.blog-details-category {
  position: relative;
  margin-bottom: 20px;
}

.blog-details-category .blog-category {
  background-color: #1f12fd;
  color: #fff;
  padding: 10px 30px;
  margin-bottom: 0;
  position: absolute;
  bottom: 7px;
  right: 20px;
}

.newsletter-form {
  max-width: 690px;
  margin-left: auto;
  margin-right: auto;
}

.newsletter-form .form-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  background-color: #fff;
  border-radius: 35px;
  padding: 10px;
  overflow: hidden;
}

.newsletter-form .form-group .form-control {
  border: 0;
  padding: 10px 20px;
  background-color: transparent;
  border-radius: 0;
}

.newsletter-form .form-group .form-control:active, .newsletter-form .form-group .form-control:focus {
  border: 0;
}

.newsletter-form #validator-newsletter {
  color: #fff;
  text-align: center;
  font-size: 15px;
}

.newsletter-form #validator-newsletter.validation-danger {
  margin-top: 5px;
}

.footer {
  position: relative;
}

@-webkit-keyframes horizontalRide {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  20% {
    -webkit-transform: translateX(-20px);
            transform: translateX(-20px);
  }
  40% {
    -webkit-transform: translateX(-40px);
            transform: translateX(-40px);
  }
  60% {
    -webkit-transform: translateX(-50px);
            transform: translateX(-50px);
  }
  80% {
    -webkit-transform: translateX(-20px);
            transform: translateX(-20px);
  }
  100% {
    -webkit-transform: translateX(0px);
            transform: translateX(0px);
  }
}

@keyframes horizontalRide {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  20% {
    -webkit-transform: translateX(-20px);
            transform: translateX(-20px);
  }
  40% {
    -webkit-transform: translateX(-40px);
            transform: translateX(-40px);
  }
  60% {
    -webkit-transform: translateX(-50px);
            transform: translateX(-50px);
  }
  80% {
    -webkit-transform: translateX(-20px);
            transform: translateX(-20px);
  }
  100% {
    -webkit-transform: translateX(0px);
            transform: translateX(0px);
  }
}

.footer-shape {
  position: absolute;
}

.footer-shape:nth-child(1) {
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  left: 5%;
}

.footer-shape:nth-child(2) {
  bottom: 65px;
  right: 5%;
}

.footer-round-shape {
  opacity: .12;
}

.footer-round-shape img {
  -webkit-animation: horizontalRide 10s alternate infinite linear;
          animation: horizontalRide 10s alternate infinite linear;
}

.footer-content-item {
  margin-bottom: 20px;
}

.footer-details {
  margin-top: 20px;
}

.footer-details p {
  margin-bottom: 15px;
  color: #000;
}

.footer-details p:last-child {
  margin-bottom: 0;
}

.footer-content-title h3 {
  font-size: 22px;
  margin-bottom: 0;
}

.footer-list {
  padding-left: 0;
  padding-right: 0;
  margin-bottom: 0;
}

.footer-list li {
  font-size: 16px;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
  list-style: none;
}

.footer-list li:last-child {
  margin-bottom: 0;
}

.footer-list li a {
  color: #000;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.footer-list li p {
  margin-top: 3px;
}

.footer-list li:before {
  content: "";
  position: absolute;
  left: 0;
  top: 9px;
  width: 5px;
  height: 5px;
  background: #1f12fd;
}

.footer-list li:hover a {
  color: #1f12fd;
}

.footer-address-item {
  margin-bottom: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.footer-address-item:last-child {
  margin-bottom: 0;
}

.footer-address-text h4 {
  font-size: 18px;
  margin-bottom: 7px;
}

.footer-address-text p {
  font-size: 16px;
  margin-bottom: 0;
  color: #000;
}

.footer-address-text p a {
  color: #000;
}

.footer-address-text p a:hover {
  color: #1f12fd;
}

.footer-lower {
  padding-top: 20px;
  padding-bottom: 20px;
  border-top: 1px solid #ebebeb;
}

.footer-copyright-text {
  text-align: center;
}

.footer-copyright-text p {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 0;
  color: #000;
}

.footer-copyright-text p a {
  color: #1f12fd;
  font-weight: 500;
}

.footer-copyright-text p a:hover {
  text-decoration: underline;
}

.pricing-card {
  border-radius: 10px;
  -webkit-box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
          box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
  padding: 60px;
  text-align: center;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.pricing-card:hover {
  background-color: #1f12fd;
}

.pricing-card:hover .pricing-category h3 {
  color: #fff;
}

.pricing-card:hover .pricing-thumb i {
  color: #fff;
  -webkit-transform: translateY(-5px);
          transform: translateY(-5px);
}

.pricing-card:hover .pricing-thumb:after {
  opacity: 0.3;
}

.pricing-card:hover .pricing-info h3 {
  color: #fff;
}

.pricing-card:hover .pricing-info h3 span {
  color: #fff;
}

.pricing-card:hover .pricing-info ul li {
  color: rgba(255, 255, 255, 0.6);
}

.pricing-card:hover .pricing-info ul li.pricing-available {
  color: #fff;
}

.pricing-card:hover .main-btn {
  background-color: #fff;
  color: #1f12fd;
}

.pricing-category {
  margin-bottom: 18px;
}

.pricing-category h3 {
  font-size: 22px;
  margin-bottom: 20px;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.pricing-thumb {
  position: relative;
  padding-bottom: 10px;
}

.pricing-thumb:after {
  content: "";
  width: 115px;
  height: 55px;
  background-color: #f6f6ff;
  position: absolute;
  border-radius: 55px / 30px;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.pricing-thumb i {
  font-size: 70px;
  line-height: 1;
  position: relative;
  z-index: 1;
  display: block;
  -webkit-transition: -webkit-transform 0.3s cubic-bezier(0.25, 0.55, 0.4, 2.1) 0s;
  transition: -webkit-transform 0.3s cubic-bezier(0.25, 0.55, 0.4, 2.1) 0s;
  transition: transform 0.3s cubic-bezier(0.25, 0.55, 0.4, 2.1) 0s;
  transition: transform 0.3s cubic-bezier(0.25, 0.55, 0.4, 2.1) 0s, -webkit-transform 0.3s cubic-bezier(0.25, 0.55, 0.4, 2.1) 0s;
  color: #000;
}

.pricing-info h3 {
  font-size: 40px;
  color: #1f12fd;
  margin-bottom: 30px;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.pricing-info h3 span {
  font-size: 18px;
  color: #000;
  font-weight: 400;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.pricing-info ul {
  padding-left: 0;
  padding-right: 0;
  margin-bottom: 40px;
}

.pricing-info ul li {
  list-style: none;
  font-size: 18px;
  color: rgba(0, 0, 0, 0.4);
  margin-bottom: 15px;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.pricing-info ul li:last-child {
  margin-bottom: 0;
}

.pricing-info ul li.pricing-available {
  color: #000;
}

.post-review-area {
  border-top: 1px solid #f4f8ff;
  padding-top: 30px;
}

.review-holder-item {
  background-color: #f4f8ff;
  border-radius: 10px;
  padding: 10px 30px;
  margin-bottom: 20px;
}

.review-holder-item:last-child {
  margin-bottom: 0;
}

.post-review-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-top: 20px;
  padding-bottom: 20px;
}

.post-review-item-reply {
  border-top: 1px solid #ebebeb;
}

.post-review-thumb {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
}

.post-review-thumb img {
  width: 100%;
  height: 100%;
}

.post-review-content-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-top: -5px;
  margin-bottom: 10px;
}

.post-review-header-item {
  margin-top: 5px;
}

.post-review-header-item h3 {
  font-size: 16px;
  color: #000;
  margin-bottom: 0;
}

.post-review-header-item p {
  font-size: 12px;
  color: #717171;
  margin-top: 5px !important;
  margin-bottom: 0;
}

.post-review-header-item .post-review-btn {
  padding: 10px 20px;
  font-size: 14px;
  background-color: #1f12fd;
  color: #fff;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  border-radius: 30px;
}

.post-review-header-item .post-review-btn:hover {
  -webkit-box-shadow: 0px 8px 35px 0px rgba(32, 32, 32, 0.08);
          box-shadow: 0px 8px 35px 0px rgba(32, 32, 32, 0.08);
}

.post-review-content {
  -webkit-box-flex: 1;
          flex: 1;
  -ms-flex: 1;
  max-width: 100%;
}

.post-review-content p {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 0;
}

.post-review-item-reply {
  padding-left: 130px;
}

.post-comment-area {
  border-top: 1px solid #e1e1e1;
  padding-top: 30px;
}

.map-box {
  border: 1px solid #1f12fd;
  height: 540px;
}

.map-box iframe {
  border: 0;
  width: 100%;
  height: 100%;
}

.product-list-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-left: -10px;
  margin-right: -10px;
  margin-top: -10px;
  margin-bottom: 30px;
}

.product-list-header-item {
  padding: 0 10px;
  margin-top: 10px;
}

.product-list-action {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-left: -10px;
  margin-right: -10px;
  margin-top: -15px;
}

.product-list-action-item {
  margin-top: 15px;
}

.product-list-search .form-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border: 1px solid #e1e1e1;
  padding: 15px 20px;
  border-radius: 5px;
}

.product-list-search .form-group .form-control {
  border-radius: 0;
  background-color: transparent;
  padding: 0;
  border: none;
  padding-right: 10px;
}

.product-list-search .form-group button {
  background-color: transparent;
  color: #000;
  font-size: 16px;
}

.product-list-form {
  padding-left: 10px;
  padding-right: 10px;
}

.product-list-form select {
  font-size: 16px;
  font-weight: 400;
  color: #767676;
  border: 0;
  outline: 0;
  cursor: pointer;
}

.product-list-result p {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

.product-list-result p span {
  font-weight: 600;
}

.nice-select {
  padding: 15px 35px 15px 20px;
  height: auto;
  font-size: 16px;
  border: 1px solid #e1e1e1;
  line-height: 1.7;
  background: transparent;
  border-radius: 5px;
  color: #767676;
}

.nice-select.open, .nice-select:focus {
  border: 1px solid #e1e1e1;
  color: #000;
}

.nice-select.open:after, .nice-select:focus:after {
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.nice-select:hover {
  border: 1px solid #e1e1e1;
}

.nice-select:after {
  content: "\f121";
  font-family: Flaticon;
  border: 0;
  font-size: 13px;
  top: 50%;
  position: absolute;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  margin-top: 0;
  height: auto;
  right: 10px;
  width: auto;
  -webkit-transform-origin: center;
          transform-origin: center;
}

.nice-select .list {
  left: auto;
  right: 0;
  border: 0;
  border-radius: 0;
  padding-top: 5px;
  padding-bottom: 5px;
}

.nice-select .list li {
  min-height: auto;
  line-height: 30px;
  font-size: 15px;
  color: #000;
}

.nice-select .list li:hover {
  background-color: transparent;
  color: #1f12fd;
}

.nice-select .list li:hover.selected, .nice-select .list li:hover.focus, .nice-select .list li:hover.selected.focus {
  background-color: transparent;
  color: #1f12fd;
}

.nice-select .list li.selected, .nice-select .list li.focus, .nice-select .list li.selected.focus {
  background-color: transparent;
  color: #1f12fd;
}

.review-star-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-left: 0;
  padding-right: 0;
  margin-bottom: 0;
}

.review-star-list li {
  margin-right: 1px;
  color: #e1e1e1;
  list-style: none;
  font-size: 16px;
  line-height: 1;
}

.review-star-list li.starred {
  color: #eebe2d;
}

.review-star-list li:last-child {
  margin-right: 0;
}

.review-star-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-top: -5px;
}

.review-star-group .review-star-list {
  margin-top: 5px;
  margin-right: 10px;
}

.review-star-group span {
  color: #767676;
  font-size: 15px;
  margin-top: 5px;
}

.product-card {
  padding: 10px 10px 23px;
  border-radius: 10px;
  -webkit-box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
          box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
  background-color: #fff;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  position: relative;
}

.product-card:hover {
  -webkit-transform: translateY(-10px);
          transform: translateY(-10px);
}

.product-card:hover .product-card-image img {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}

.product-card:hover .product-card-details h3 a {
  color: #1f12fd;
  text-decoration: underline;
}

.product-card:hover .product-card-details .redirect-link {
  color: #1f12fd;
}

.product-card-thumb {
  border-radius: 10px 10px 0 0;
  overflow: hidden;
  margin-bottom: 23px;
  position: relative;
  background-color: #f6f6f6;
}

.product-card-thumb img {
  -webkit-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out;
}

.product-card-details {
  padding-left: 15px;
  padding-right: 15px;
}

.product-card-details h3 {
  font-size: 20px;
  line-height: 1.4;
  margin-bottom: 11px;
}

.product-card-details h3 a {
  color: #000;
}

.product-card-details .review-star-list {
  margin-bottom: 15px;
}

.product-card-details .product-price {
  margin-bottom: 15px;
}

.product-card-details .product-price:last-child {
  margin-bottom: 0;
}

.product-price {
  color: #767676;
  font-size: 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.product-price del {
  margin-right: 10px;
  font-size: 15px;
  color: #1f12fd;
}

.product-slider-for {
  background-color: #f4f8ff;
  border-radius: 3px;
  margin-bottom: 30px;
  position: relative;
}

.product-slider-for .item {
  padding: 20px;
}

.product-slider-nav .item {
  background-color: #f4f8ff;
  border-radius: 3px;
  padding: 15px;
  cursor: pointer;
  border: 1px solid transparent;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.product-slider-nav .owl-item.synced .item {
  border: 1px solid #1f12fd;
}

.product-gallery-trigger {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  position: absolute;
  top: 35px;
  right: 35px;
  z-index: 1;
}

.product-gallery-trigger a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: #1f12fd;
  width: 100%;
  height: 100%;
  color: #fff;
}

.product-gallery-trigger a i {
  font-size: 20px;
}

.product-details-caption h3 {
  font-size: 30px;
  margin-bottom: 15px;
}

.product-details-caption h5 {
  font-size: 22px;
  margin-bottom: 15px;
}

.product-details-caption .review-star-group {
  margin-bottom: 15px;
}

.product-details-caption .product-price {
  margin-bottom: 15px;
}

.product-details-caption .product-details-para {
  margin-bottom: 15px;
}

.product-details-caption .product-quantity {
  margin-bottom: 18px;
}

.product-details-para p {
  font-size: 17px;
}

.product-action-info h4 {
  font-size: 16px;
  margin-bottom: 10px;
}

.cart-quantity {
  background-color: transparent;
  border: 1px solid #e1e1e1;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  border-radius: 5px;
  overflow: hidden;
}

.cart-quantity button {
  background-color: transparent;
  padding: 10px 20px;
  color: #767676;
  font-size: 15px;
}

.cart-quantity input {
  padding: 5px 10px;
  border: 0;
  outline: 0;
  background-color: transparent;
  -webkit-box-flex: 0;
          flex: 0 0 50px;
  -ms-flex: 0 0 50px;
  max-width: 50px;
  width: 50px;
  text-align: center;
  color: #000;
  font-size: 17px;
  border-left: 1px solid #e1e1e1;
  border-right: 1px solid #e1e1e1;
}

.product-quantity .cart-quantity {
  margin-right: 15px;
  margin-top: 10px;
}

.product-quantity .btn {
  font-size: 16px;
  padding-top: 10px;
  padding-bottom: 10px;
  margin-top: 10px;
}

.product-tab-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 2px solid #e1e1e1;
  padding-left: 0;
  padding-right: 0;
  margin-bottom: 0;
}

.product-tab-list li {
  padding: 20px 0;
  margin-right: 60px;
  cursor: pointer;
  font-size: 18px;
  font-weight: 500;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  position: relative;
  color: #000;
  list-style: none;
}

.product-tab-list li:before {
  content: "";
  position: absolute;
  height: 2px;
  width: 100%;
  top: 100%;
  background-color: transparent;
  left: 0;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.product-tab-list li.active:before {
  background-color: #1f12fd;
}

.product-tab-list li span {
  margin-left: 3px;
}

.product-tab-information-item {
  display: none;
}

.product-tab-information-item.active {
  display: inline-block;
  width: 100%;
  -webkit-animation: fadeIn 1s;
          animation: fadeIn 1s;
}

.product-description {
  border-bottom: 1px solid #eee;
  border-left: 1px solid #eee;
  border-right: 1px solid #eee;
  padding: 30px;
}

.product-description p {
  font-size: 16px;
  margin-bottom: 20px;
}

.product-description p:last-child {
  margin-bottom: 0;
}

.product-description ul {
  padding-left: 25px;
  margin-top: -5px;
  margin-bottom: 20px;
}

.product-description ul li {
  font-size: 16px;
  margin-top: 5px;
  position: relative;
  list-style: none;
  padding-left: 30px;
}

.product-description ul li:before {
  content: "\eed9";
  font-family: IcoFont;
  position: absolute;
  left: 0;
  top: -3px;
  font-size: 20px;
  color: #1f12fd;
}

.product-description-table {
  overflow: auto;
  margin-bottom: 20px;
}

.product-description-table::-webkit-scrollbar {
  height: 7px;
}

.product-description-table::-webkit-scrollbar-thumb {
  background: #e1e1e1;
}

.product-description-table::-webkit-scrollbar-track {
  background: #efefef;
}

.product-description-table table {
  width: 100%;
}

.product-description-table table tbody {
  border: 1px solid #e1e1e1;
}

.product-description-table table tbody tr {
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.product-description-table table tbody tr td {
  font-size: 15px;
  color: #767676;
  font-weight: 400;
  padding: 10px 15px;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  border-bottom: 1px solid #e1e1e1;
  border-right: 1px solid #e1e1e1;
}

.product-description-table table tbody tr td.weight-500 {
  font-weight: 500;
}

.product-description-table table tbody tr td:last-child {
  border-right: 0;
}

.product-review-box {
  max-width: 700px;
  margin: 30px auto 0;
}

.product-review-area {
  max-width: 700px;
  margin: auto;
  margin-bottom: 20px;
}

.product-review-area .sub-section-title {
  margin-bottom: 20px;
}

.product-review-area .input-group label {
  font-size: 16px;
}

.form-review {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.form-review .rating {
  margin-right: 15px;
}

.form-review h4 {
  font-size: 15px;
  font-weight: 400;
  color: #767676;
  margin-bottom: 0;
}

.star-rating {
  -webkit-box-flex: 0;
          flex: 0 0 125px;
  -ms-flex: 0 0 120px;
  max-width: 120px;
  width: 125px;
  font-size: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
  -ms-flex-pack: distribute;
      justify-content: space-around;
}

.star-rating input {
  display: none;
}

.star-rating label {
  color: #e1e1e1;
  cursor: pointer;
}

.star-rating label:before {
  content: "\f000";
  font-family: IcoFont;
}

.star-rating label:hover, .star-rating label:hover ~ label {
  color: #eebe2d;
}

.star-rating label:hover:before, .star-rating label:hover ~ label:before {
  content: "\f000";
  font-family: IcoFont;
}

.star-rating input:checked ~ label {
  color: #eebe2d;
}

.star-rating input:checked ~ label:before {
  content: "\f000";
  font-family: IcoFont;
}

.divider {
  margin-top: 20px;
  margin-bottom: 20px;
  border-top: 1px solid #e1e1e1;
}

.divider.divider-lg {
  margin-top: 30px;
  margin-bottom: 30px;
}

.project-flat-card:hover .project-flat-text h3 a {
  color: #1f12fd;
}

.project-flat-thumb {
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 20px;
}

.project-flat-text {
  text-align: center;
}

.project-flat-text h3 {
  font-size: 22px;
  position: relative;
  padding-left: 15px;
  display: inline-block;
  line-height: 1.4;
  margin-bottom: 13px;
}

.project-flat-text h3:last-child {
  margin-bottom: 0;
}

.project-flat-text h3:before {
  content: "";
  position: absolute;
  left: 0;
  top: 10px;
  width: 5px;
  height: 5px;
  background-color: #1f12fd;
}

.project-flat-text h3 a {
  color: #000;
}

.search-overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: #1f12fd;
  top: 0;
  left: 0;
  z-index: 1111;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 20px;
  display: none;
}

.search-overlay.search-overlay-show {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-animation: fadeIn 1s;
          animation: fadeIn 1s;
}

.search-overlay.search-overlay-none {
  -webkit-animation: fadeOut 1s;
          animation: fadeOut 1s;
  display: none;
}

.search-area-logo {
  text-align: center;
  margin-bottom: 50px;
}

.search-form-area {
  width: 300px;
}

.search-form-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.search-form-group .form-control {
  border: 1px solid #fff;
  border-radius: 40px;
  margin-right: 15px;
  margin-bottom: 0;
  padding: 10px 15px;
  background-color: #fff;
}

.search-form-group button {
  border: 1px solid #fff;
  background-color: #fff;
  border-radius: 40px;
  padding: 10px 15px;
  font-size: 15px;
  color: #1f12fd;
}

.search-close {
  font-size: 40px;
  color: #fff;
  position: absolute;
  top: 100px;
  right: 100px;
  cursor: pointer;
}

.scroll-top {
  position: fixed;
  bottom: 105%;
  right: 20px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  z-index: 11;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  -webkit-transition: all 0.5s linear;
  transition: all 0.5s linear;
}

.scroll-top.scrolltopactive {
  bottom: 30px;
  opacity: 1;
  visibility: visible;
  pointer-events: all;
}

.scroll-top:hover .scroll-top-inner {
  color: #fff;
}

.scroll-top:after {
  position: absolute;
  z-index: -1;
  content: '';
  top: 100%;
  left: 5%;
  height: 10px;
  width: 90%;
  opacity: 1;
  background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0) 80%);
}

.scroll-top-inner {
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  color: #fff;
  background: #1f12fd;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 20px;
  border-radius: 50%;
}

.contact-info {
  padding: 35px 90px 5px;
  border-radius: 10px;
  -webkit-box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
          box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
  position: relative;
  background-color: #fff;
}

.contact-info:before {
  content: "";
  width: calc(100% + 20px);
  height: 100%;
  background-color: #fff;
  position: absolute;
  bottom: -15px;
  left: -10px;
  -webkit-box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
          box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
  border-radius: 10px;
  z-index: -1;
}

.contact-info-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 20px;
}

.contact-info-header-icon {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 65px;
  height: 65px;
  background-color: #e7e5ff;
  border-radius: 10px;
  font-size: 35px;
  color: #1f12fd;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center;
  margin-right: 18px;
}

.contact-info-header-text h3 {
  font-size: 22px;
  margin-bottom: 5px;
}

.contact-info-header-text p {
  font-size: 16px;
  margin-bottom: 0;
}

.contact-info-body p {
  font-size: 18px;
  margin-bottom: 2px;
  font-weight: 500;
}

.contact-info-body p:last-child {
  margin-bottom: 0;
}

.contact-info-body p a {
  color: #767676;
}

.contact-info-body p a:hover {
  color: #1f12fd;
}

.contact-form-box {
  padding: 40px 40px;
  background-color: #fff;
  border-radius: 10px;
  -webkit-box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
          box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
}

.contact-form-box .contact-form-item .section-title {
  margin-bottom: 30px;
}

.contact-map {
  border-radius: 10px;
  overflow: hidden;
  height: 100%;
}

.contact-map iframe {
  width: 100%;
  height: 100%;
  border: 0;
}

.accordion-button {
  border: 0;
  background-color: #1f12fd;
  padding: 25px 30px;
  color: #fff;
  border-radius: 0 !important;
  position: relative;
  font-size: 18px;
  font-weight: 700;
  text-align: left;
  line-height: 1.4;
}

.accordion-button:after {
  content: "\f121";
  font-family: Flaticon;
  font-weight: 400;
  margin-right: 0;
  background-image: none;
}

.accordion-button:active, .accordion-button:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  outline: 0;
}

.accordion-button:not(.collapsed) {
  background-color: #1f12fd;
  color: #fff;
}

.accordion-button:not(.collapsed):after {
  content: "\f121";
  font-family: Flaticon;
  font-weight: 400;
}

.accordion-collapse {
  background-color: #F5F5F5;
  border: 0;
  border-radius: 0 !important;
}

.accordion-body {
  padding: 25px 30px;
  color: #767676;
}

.accordion-item {
  margin-bottom: 30px;
  border-radius: 5px;
  overflow: hidden;
}

.accordion-item:last-child {
  margin-bottom: 0;
}

.team-card {
  padding: 10px 10px 23px;
  border-radius: 10px;
  -webkit-box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
          box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
  background-color: #fff;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  position: relative;
}

.team-card:hover {
  -webkit-transform: translateY(-10px);
          transform: translateY(-10px);
}

.team-card:hover .team-card-thumb img {
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}

.team-card:hover .team-card-content h3 a {
  color: #1f12fd;
}

.team-card:hover .team-social-list li {
  opacity: 1;
  pointer-events: all;
  -webkit-transform: translateX(0);
          transform: translateX(0);
}

.team-card-thumb {
  overflow: hidden;
  border-radius: 10px 10px 0 0;
  position: relative;
  margin-bottom: 23px;
}

.team-card-thumb img {
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.team-card-content {
  padding-left: 15px;
  padding-right: 15px;
}

.team-card-content h3 {
  font-size: 22px;
  line-height: 1.4;
  margin-bottom: 10px;
}

.team-card-content h3 a {
  color: #000;
}

.team-card-content h4 {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #646363;
}

.team-card-content p {
  font-size: 16px;
  margin-bottom: 10px;
}

.team-card-content p:last-child {
  margin-bottom: 0;
}

.team-social-list {
  padding-left: 0;
  padding-right: 0;
  margin-bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  position: absolute;
  top: 20px;
  right: 20px;
}

.team-social-list li {
  list-style: none;
  margin-bottom: 10px;
  width: 30px;
  height: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 50%;
  overflow: hidden;
  opacity: 0;
  pointer-events: none;
  -webkit-transform: translateX(30px);
          transform: translateX(30px);
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}

.team-social-list li:nth-child(1) {
  -webkit-transition-delay: 0.1s;
          transition-delay: 0.1s;
}

.team-social-list li:nth-child(2) {
  -webkit-transition-delay: 0.2s;
          transition-delay: 0.2s;
}

.team-social-list li:nth-child(3) {
  -webkit-transition-delay: 0.3s;
          transition-delay: 0.3s;
}

.team-social-list li:last-child {
  margin-bottom: 0;
}

.team-social-list li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #1f12fd;
  color: #fff;
  font-size: 14px;
}

.job-card {
  padding: 25px;
  border-radius: 10px;
  -webkit-box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
          box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
  background-color: #fff;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  position: relative;
}

.job-card:hover {
  -webkit-transform: translateY(-10px);
          transform: translateY(-10px);
}

.job-card .btn {
  width: 100%;
  padding: 10px 20px;
  background-color: #1f12fd;
  color: #fff;
}

.job-card .btn:before, .job-card .btn:after {
  content: none;
}

.job-card .btn:hover {
  color: #fff;
}

.job-card-title h3 {
  font-size: 24px;
  margin-bottom: 12px;
  line-height: 1.4;
}

.job-card-title h4 {
  font-size: 18px;
  color: #767676;
  margin-bottom: 10px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e1e1e1;
}

.job-card-title p {
  font-size: 17px;
  color: #767676;
  margin-bottom: 12px;
  font-weight: 500;
}

.job-card-brief p {
  font-size: 16px;
}

.related-product-carousel .owl-stage-outer {
  padding-top: 40px;
  padding-bottom: 40px;
  margin-top: -40px;
  margin-bottom: -40px;
}

.cart-table {
  overflow: auto;
}

.cart-table::-webkit-scrollbar {
  height: 7px;
}

.cart-table::-webkit-scrollbar-thumb {
  background: #a2a2a2;
}

.cart-table::-webkit-scrollbar-track {
  background: #e2e2e2;
}

.cart-table table {
  width: 100%;
  overflow: hidden;
  border-radius: 3px 3px 0 0;
}

.cart-table table thead tr {
  background: #1f12fd;
}

.cart-table table thead tr th {
  font-size: 15px;
  color: #000;
  font-weight: 500;
  padding: 20px;
  white-space: nowrap;
  color: #fff;
}

.cart-table table tbody tr {
  border-bottom: 1px solid #ededed;
}

.cart-table table tbody tr td {
  font-size: 15px;
  color: #767676;
  font-weight: 400;
  padding: 10px 20px;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  white-space: nowrap;
}

.cart-table table tbody tr td.weight-500 {
  font-weight: 500;
}

.cart-table table tbody tr td .main-btn {
  padding: 10px 15px;
  -webkit-box-shadow: none;
          box-shadow: none;
  font-size: 14px;
}

.cart-table table tbody tr td .cart-quantity button {
  padding: 5px 15px;
}

.cart-table table tbody tr td .cart-quantity input {
  padding: 5px 15px;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.cart-table table tbody tr td.cancel a {
  border: 1px solid #e1e1e1;
  padding: 10px;
  font-size: 10px;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  border-radius: 3px;
}

.cart-table table tbody tr td.cancel a:hover {
  background-color: #1f12fd;
  color: #fff;
  border-color: #1f12fd;
}

.product-table-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.product-table-info span {
  font-size: 16px;
  font-weight: bold;
  font-family: "Poppins", sans-serif;
  color: #000;
}

.product-table-thumb {
  -webkit-box-flex: 0;
          flex: 0 0 55px;
  -ms-flex: 0 0 55px;
  max-width: 55px;
  width: 55px;
  margin-right: 10px;
}

.product-table-thumb img {
  width: 100%;
}

.cart-info-item {
  margin-bottom: 30px;
}

.cart-coupon form {
  height: 100%;
}

.cart-coupon form .form-group {
  margin-bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border: 0;
  background-color: #efefef;
  height: 100%;
  border-radius: 10px;
  overflow: hidden;
}

.cart-coupon form .form-group .form-control {
  padding: 15px 20px;
  height: 100%;
  border: 0;
  background-color: transparent;
  -webkit-box-flex: 1;
          flex: 1;
  -ms-flex: 1;
  max-width: 100%;
  width: 100%;
}

.cart-coupon form .form-group .main-btn {
  padding: 15px 20px;
  border-radius: 0;
  border: 0;
  height: 100%;
  position: relative;
  z-index: 1;
  background-color: #1f12fd;
}

.cart-coupon form .form-group .main-btn:hover {
  color: #fff;
}

.cart-details {
  padding: 30px;
  border-radius: 10px;
}

.cart-details .btn {
  width: 100%;
  margin-top: 15px;
}

.cart-details-title {
  font-size: 20px;
}

.cart-total-box {
  margin-top: 15px;
  border: 1px solid #e1e1e1;
  border-radius: 10px;
}

.cart-total-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 1px solid #e1e1e1;
  padding: 10px 15px;
}

.cart-total-item:last-child {
  border-bottom: 0;
}

.cart-total-item h4 {
  font-size: 16px;
  color: #767676;
  margin-right: 10px;
  margin-bottom: 0;
}

.cart-total-item p {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

.cart-section-contents {
  display: block;
}

.cart-section-contents.cart-section-contents-hide {
  display: none;
}

.cart-section-empty {
  display: none;
}

.cart-section-empty.cart-section-empty-active {
  display: block;
}

.cart-empty-content {
  text-align: center;
  margin-bottom: 30px;
}

.cart-empty-image {
  margin-bottom: 25px;
}

.cart-empty-text h3 {
  font-size: 40px;
  margin-bottom: 15px;
}

.cart-empty-text h3 span {
  color: #1f12fd;
  font-weight: bold;
}

.cart-empty-text p {
  font-size: 17px;
  margin-bottom: 25px;
}

.checkout-details .cart-total-box {
  margin-top: 0;
}

.checkout-details .cart-total-item {
  padding: 15px 20px;
}

.checkout-details .cart-total-item h4, .checkout-details .cart-total-item p {
  font-size: 15px;
}

.checkout-details .cart-total-item:last-child {
  border-bottom: 0;
}

.checkout-details .cart-total-item .checkout-total-title {
  color: #000;
  font-weight: 600;
  font-size: 16px;
}

.checkout-payment-area {
  padding: 30px;
  -webkit-box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
          box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
  border-radius: 10px;
}

.checkout-payment-area .sub-section-title {
  margin-bottom: 20px;
}

.checkout-payment-form p {
  font-size: 17px;
  margin-bottom: 20px;
}

.account-sidebar {
  padding: 40px;
  border: 1px solid #e1e1e1;
  border-radius: 10px;
}

.account-sidebar-list {
  padding-left: 0;
  padding-right: 0;
  margin-bottom: 0;
}

.account-sidebar-list li {
  list-style: none;
  margin-bottom: 10px;
}

.account-sidebar-list li:last-child {
  margin-bottom: 0;
}

.account-sidebar-list li a {
  background-color: #f4f8ff;
  font-size: 16px;
  color: #000;
  font-weight: 500;
  padding: 10px 20px;
  display: block;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  border-radius: 3px;
}

.account-sidebar-list li a:hover {
  color: #fff;
  background: #1f12fd;
}

.account-sidebar-list li.active a {
  color: #fff;
  background: #1f12fd;
}

.account-info {
  padding: 40px;
  border: 1px solid #e1e1e1;
  border-radius: 10px;
}

.account-info .cart-table {
  width: 100%;
  margin-bottom: 0;
}

.account-info .cart-table table thead tr th {
  font-size: 16px;
}

.account-info .cart-table table tbody tr td {
  font-size: 16px;
}

.account-info .product-table-thumb {
  -webkit-box-flex: 0;
          flex: 0 0 80px;
  -ms-flex: 0 0 80px;
  max-width: 80px;
}

.account-info .product-table-info span {
  font-size: 17px;
}

.account-avatar-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-top: -10px;
}

.account-avatar-thumb {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 75px;
  height: 75px;
  border-radius: 100%;
  overflow: hidden;
  margin-right: 25px;
  margin-top: 10px;
}

.account-avatar-thumb img {
  width: 100%;
  height: 100%;
}

.account-avatar-action {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-top: 10px;
  margin-bottom: 0;
  padding-left: 0;
  padding-right: 0;
}

.account-avatar-action li {
  margin-right: 10px;
  list-style: none;
}

.account-avatar-action li:last-child {
  margin-right: 0;
}

.account-avatar-action li .main-btn {
  padding: 10px 25px;
  font-size: 15px;
}

.account-setting-button {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-top: -10px;
}

.account-setting-button .btn {
  margin-left: 10px;
  margin-top: 10px;
}

.account-setting-button .btn:first-child {
  margin-left: 0;
}

.my-order {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-top: -20px;
  margin-bottom: 30px;
}

.my-order .sub-section-title {
  margin-top: 20px;
  margin-bottom: 0;
}

.my-order .sub-section-title-heading {
  margin-bottom: 0;
}

.my-order .product-list-action {
  margin-top: 20px;
  margin-left: 0;
  margin-right: 0;
}

.my-order .product-list-action .product-list-form {
  margin-top: 0;
  padding-left: 0;
  padding-right: 0;
}

.billing-title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-bottom: 20px;
  margin-top: -10px;
}

.billing-title h4 {
  font-size: 18px;
  font-weight: 500;
  color: #000;
  margin-bottom: 0;
  margin-top: 10px;
}

.billing-title p {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 10px;
  border-bottom: 1px solid #1f12fd;
  cursor: pointer;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.billing-title p:hover {
  color: #1f12fd;
  border-bottom-color: #1f12fd;
}

.billing-address {
  padding: 20px;
  border: 1px solid #e1e1e1;
}

.billing-address.none {
  display: none;
}

.billing-address address {
  font-size: 15px;
  margin: 0;
  color: #767676;
}

.billing-address-info {
  font-size: 16px;
  font-weight: 400;
  display: none;
}

.billing-address-input {
  display: none;
}

.billing-address-input.active {
  display: block;
}

.authentication-item {
  padding: 50px;
  border-radius: 10px;
  -webkit-box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
          box-shadow: 0px 9px 54px 0px rgba(32, 32, 32, 0.1);
}

.authentication-item h3 {
  font-size: 25px;
  margin-bottom: 25px;
}

.authentication-item .form-desc {
  font-size: 16px;
  color: #767676;
}

.authentication-account-access {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-top: -10px;
}

.authentication-account-access-item {
  margin-top: 10px;
}

.authentication-link a {
  color: #1f12fd;
  border-bottom: 1px solid #1f12fd;
  font-size: 14px;
  font-weight: 400;
}

.error-page-content {
  text-align: center;
}

.error-page-content img {
  margin-bottom: 50px;
}

.error-page-content h2 {
  font-size: 35px;
  margin-bottom: 17px;
}

.error-page-content h2:first-child {
  margin-top: 0;
}

.error-page-content h2:last-child {
  margin-bottom: 0;
}

.error-page-content p {
  font-size: 16px;
  margin-bottom: 23px;
}

.error-page-content p:first-child {
  margin-top: 0;
}

.error-page-content p:last-child {
  margin-bottom: 0;
}

.error-page-content .main-btn {
  margin-bottom: 20px;
}

.error-page-content .main-btn:last-child {
  margin-bottom: 0;
}

.coming-soon-section {
  height: 100vh;
  overflow: auto;
  position: relative;
  padding: 30px;
}

.coming-soon-section .container {
  height: 100%;
  position: relative;
}

.coming-soon-content {
  position: relative;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-flow: column;
          flex-flow: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center;
}

.new-counter {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.new-counter p {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-flow: column;
          flex-flow: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 80px;
  font-weight: 600;
  color: #fff;
  padding-left: 50px;
  padding-right: 50px;
  position: relative;
  font-family: "Poppins", sans-serif;
}

.new-counter p:before {
  content: ":";
  position: absolute;
  right: 0;
  top: 10px;
  color: #fff;
  font-size: 70px;
}

.new-counter p span {
  font-size: 40px;
  font-weight: 400;
}

.new-counter p:last-child:before {
  content: none;
}

.coming-soon-details {
  max-width: 735px;
  padding-top: 45px;
  position: relative;
  margin-top: 50px;
}

.coming-soon-details:before {
  content: "";
  width: 150px;
  height: 1px;
  background-color: #fff;
  position: absolute;
  top: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}

.coming-soon-details h2 {
  font-size: 60px;
  margin-bottom: 20px;
  color: #fff;
}

.coming-soon-details .form-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-bottom: 0;
}

.coming-soon-details .form-group .form-control {
  margin-right: 15px;
  -webkit-box-flex: 1;
          flex: 1;
  -ms-flex: 1;
  max-width: 100%;
}

.coming-soon-details p {
  font-size: 17px;
  margin-top: 20px;
  margin-bottom: 0;
  color: #fff;
}

.coming-soon-details #validator-newsletter {
  margin-top: 5px;
  color: #fff;
  font-size: 13px;
}
/*# sourceMappingURL=style.css.map */
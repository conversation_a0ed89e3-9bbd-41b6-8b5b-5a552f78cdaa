{"name": "hostinger-admin-sync", "version": "1.0.0", "description": "Auto-sync tool for Hostinger admin folder", "main": "sync-watcher.js", "scripts": {"start": "node sync-watcher.js", "setup": "node domain-cli.js setup", "download": "node domain-cli.js download --all", "download-domain": "node domain-cli.js download", "sync": "node domain-cli.js sync", "status": "node domain-cli.js status", "list": "node domain-cli.js list", "enable": "node domain-cli.js enable", "disable": "node domain-cli.js disable"}, "dependencies": {"chokidar": "^3.5.3", "node-ssh": "^13.1.0", "chalk": "^4.1.2", "inquirer": "^8.2.6", "fs-extra": "^11.1.1", "commander": "^11.1.0", "path": "^0.12.7"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["sync", "hostinger", "ssh", "file-watcher", "auto-deploy"], "author": "Your Name", "license": "MIT"}
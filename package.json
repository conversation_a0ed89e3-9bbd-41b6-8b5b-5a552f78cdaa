{"name": "hostinger-admin-sync", "version": "1.0.0", "description": "Auto-sync tool for Hostinger admin folder", "main": "sync-watcher.js", "scripts": {"start": "node sync-watcher.js", "download": "powershell -ExecutionPolicy Bypass -File download-admin.ps1", "upload": "node upload-changes.js", "backup": "node backup-remote.js", "watch": "node sync-watcher.js --watch"}, "dependencies": {"chokidar": "^3.5.3", "node-ssh": "^13.1.0", "chalk": "^4.1.2", "inquirer": "^8.2.6", "fs-extra": "^11.1.1", "path": "^0.12.7"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["sync", "hostinger", "ssh", "file-watcher", "auto-deploy"], "author": "Your Name", "license": "MIT"}
const chokidar = require('chokidar');
const { NodeSSH } = require('node-ssh');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const inquirer = require('inquirer');

class HostingerMultiDomainSync {
    constructor() {
        this.config = this.loadConfig();
        this.ssh = new NodeSSH();
        this.isConnected = false;
        this.uploadQueues = new Map(); // Separate queue for each domain
        this.watchers = new Map(); // File watchers for each domain
        this.activeUploads = 0;
        this.maxConcurrentUploads = this.config.sync.maxConcurrentUploads || 5;
    }

    loadConfig() {
        try {
            const config = JSON.parse(fs.readFileSync('sync-config.json', 'utf8'));
            return config;
        } catch (error) {
            console.error(chalk.red('Error loading config:'), error.message);
            process.exit(1);
        }
    }

    async connect() {
        if (this.isConnected) return;

        try {
            console.log(chalk.blue('Connecting to Hostinger...'));

            const password = await inquirer.prompt([{
                type: 'password',
                name: 'password',
                message: 'Enter your SSH password:'
            }]);

            await this.ssh.connect({
                host: this.config.hostinger.host,
                username: this.config.hostinger.username,
                port: this.config.hostinger.port,
                password: password.password
            });

            this.isConnected = true;
            console.log(chalk.green('✓ Connected to Hostinger successfully!'));

        } catch (error) {
            console.error(chalk.red('Connection failed:'), error.message);
            console.log(chalk.yellow('Please check your credentials in sync-config.json'));
            throw error;
        }
    }

    getDomainFromPath(filePath) {
        const relativePath = path.relative(this.config.hostinger.localBasePath, filePath);
        const domainName = relativePath.split(path.sep)[0];
        return domainName;
    }

    async uploadFile(localFilePath, domainName) {
        if (!this.isConnected) {
            await this.connect();
        }

        const domainConfig = this.config.domains[domainName];
        if (!domainConfig || !domainConfig.enabled) {
            console.log(chalk.yellow(`Skipping upload for disabled domain: ${domainName}`));
            return;
        }

        try {
            const relativePath = path.relative(domainConfig.localPath, localFilePath);
            const remoteFilePath = path.posix.join(domainConfig.remotePath, relativePath);

            console.log(chalk.cyan(`[${domainName}] Uploading: ${relativePath}`));

            // Ensure remote directory exists
            const remoteDir = path.posix.dirname(remoteFilePath);
            await this.ssh.execCommand(`mkdir -p "${remoteDir}"`);

            // Upload file
            await this.ssh.putFile(localFilePath, remoteFilePath);

            console.log(chalk.green(`[${domainName}] ✓ Uploaded: ${relativePath}`));

        } catch (error) {
            console.error(chalk.red(`[${domainName}] Error uploading ${localFilePath}:`), error.message);

            // Retry logic
            const retryAttempts = this.config.sync.retryAttempts || 3;
            for (let i = 1; i <= retryAttempts; i++) {
                try {
                    console.log(chalk.yellow(`[${domainName}] Retry ${i}/${retryAttempts}...`));
                    await new Promise(resolve => setTimeout(resolve, 1000 * i));
                    await this.ssh.putFile(localFilePath, remoteFilePath);
                    console.log(chalk.green(`[${domainName}] ✓ Uploaded on retry: ${relativePath}`));
                    return;
                } catch (retryError) {
                    if (i === retryAttempts) {
                        console.error(chalk.red(`[${domainName}] Failed after ${retryAttempts} retries`));
                    }
                }
            }
        }
    }

    async processUploadQueue(domainName) {
        if (!this.uploadQueues.has(domainName) || this.uploadQueues.get(domainName).size === 0) {
            return;
        }

        if (this.activeUploads >= this.maxConcurrentUploads) {
            // Too many concurrent uploads, try again later
            setTimeout(() => this.processUploadQueue(domainName), 1000);
            return;
        }

        this.activeUploads++;

        const uploadQueue = this.uploadQueues.get(domainName);
        const filesToUpload = Array.from(uploadQueue);
        uploadQueue.clear();

        console.log(chalk.blue(`[${domainName}] Processing ${filesToUpload.length} file(s)...`));

        for (const filePath of filesToUpload) {
            if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
                await this.uploadFile(filePath, domainName);
            }
        }

        this.activeUploads--;

        // Process any new files that were added during upload
        if (this.uploadQueues.has(domainName) && this.uploadQueues.get(domainName).size > 0) {
            setTimeout(() => this.processUploadQueue(domainName), 1000);
        }
    }

    shouldIgnoreFile(filePath) {
        const relativePath = path.relative(this.config.hostinger.localBasePath, filePath);

        return this.config.hostinger.excludePatterns.some(pattern => {
            if (pattern.endsWith('/')) {
                return relativePath.includes(pattern.slice(0, -1));
            }
            return relativePath.includes(pattern) || relativePath.match(pattern.replace('*', '.*'));
        });
    }

    addToUploadQueue(filePath) {
        const domainName = this.getDomainFromPath(filePath);
        if (!this.config.domains[domainName] || !this.config.domains[domainName].enabled) {
            return;
        }

        if (!this.uploadQueues.has(domainName)) {
            this.uploadQueues.set(domainName, new Set());
        }

        this.uploadQueues.get(domainName).add(filePath);
        setTimeout(() => this.processUploadQueue(domainName), this.config.sync.syncInterval);
    }

    startWatchingAllDomains() {
        if (!fs.existsSync(this.config.hostinger.localBasePath)) {
            console.error(chalk.red(`Local base path does not exist: ${this.config.hostinger.localBasePath}`));
            console.log(chalk.yellow('Please run the domain setup first.'));
            return;
        }

        console.log(chalk.blue(`Watching for changes in all domains...`));

        // Watch each domain separately
        Object.entries(this.config.domains).forEach(([domainName, domainConfig]) => {
            if (!domainConfig.enabled) {
                console.log(chalk.gray(`Skipping disabled domain: ${domainName}`));
                return;
            }

            if (!fs.existsSync(domainConfig.localPath)) {
                console.log(chalk.yellow(`Local path does not exist for ${domainName}: ${domainConfig.localPath}`));
                return;
            }

            const watcher = chokidar.watch(domainConfig.localPath, {
                ignored: /(^|[\/\\])\../, // ignore dotfiles
                persistent: true,
                ignoreInitial: true
            });

            watcher
                .on('change', (filePath) => {
                    if (!this.shouldIgnoreFile(filePath)) {
                        const relativePath = path.relative(domainConfig.localPath, filePath);
                        console.log(chalk.yellow(`[${domainName}] File changed: ${relativePath}`));
                        this.addToUploadQueue(filePath);
                    }
                })
                .on('add', (filePath) => {
                    if (!this.shouldIgnoreFile(filePath)) {
                        const relativePath = path.relative(domainConfig.localPath, filePath);
                        console.log(chalk.green(`[${domainName}] File added: ${relativePath}`));
                        this.addToUploadQueue(filePath);
                    }
                })
                .on('unlink', (filePath) => {
                    if (!this.shouldIgnoreFile(filePath)) {
                        const relativePath = path.relative(domainConfig.localPath, filePath);
                        console.log(chalk.red(`[${domainName}] File removed: ${relativePath}`));
                        this.deleteRemoteFile(filePath, domainName);
                    }
                });

            this.watchers.set(domainName, watcher);
            console.log(chalk.green(`✓ Watching domain: ${domainName}`));
        });

        console.log(chalk.green('✓ File watchers started for all enabled domains. Press Ctrl+C to stop.'));
    }

    async deleteRemoteFile(localFilePath, domainName) {
        if (!this.isConnected) {
            await this.connect();
        }

        const domainConfig = this.config.domains[domainName];
        if (!domainConfig) return;

        try {
            const relativePath = path.relative(domainConfig.localPath, localFilePath);
            const remoteFilePath = path.posix.join(domainConfig.remotePath, relativePath);

            if (this.config.operations.confirmDestructive) {
                console.log(chalk.yellow(`[${domainName}] Would delete: ${relativePath}`));
                // In a real implementation, you might want to ask for confirmation
            }

            await this.ssh.execCommand(`rm -f "${remoteFilePath}"`);
            console.log(chalk.red(`[${domainName}] ✓ Deleted remote file: ${relativePath}`));

        } catch (error) {
            console.error(chalk.red(`[${domainName}] Error deleting remote file:`), error.message);
        }
    }

    async stopWatching() {
        console.log(chalk.yellow('Stopping file watchers...'));

        for (const [domainName, watcher] of this.watchers) {
            await watcher.close();
            console.log(chalk.gray(`✓ Stopped watching: ${domainName}`));
        }

        this.watchers.clear();
        this.uploadQueues.clear();
    }

    async disconnect() {
        await this.stopWatching();

        if (this.isConnected) {
            this.ssh.dispose();
            this.isConnected = false;
            console.log(chalk.blue('Disconnected from Hostinger.'));
        }
    }

    getStatus() {
        const enabledDomains = Object.entries(this.config.domains)
            .filter(([, config]) => config.enabled)
            .map(([name]) => name);

        return {
            connected: this.isConnected,
            watchingDomains: Array.from(this.watchers.keys()),
            enabledDomains: enabledDomains,
            activeUploads: this.activeUploads,
            queueSizes: Object.fromEntries(
                Array.from(this.uploadQueues.entries()).map(([domain, queue]) => [domain, queue.size])
            )
        };
    }
}

// Main execution
async function main() {
    const sync = new HostingerMultiDomainSync();

    // Handle graceful shutdown
    process.on('SIGINT', async () => {
        console.log(chalk.yellow('\nShutting down...'));
        await sync.disconnect();
        process.exit(0);
    });

    try {
        // Check if domains are configured
        if (Object.keys(sync.config.domains).length === 0) {
            console.log(chalk.yellow('No domains configured. Please run domain setup first:'));
            console.log(chalk.cyan('node domain-cli.js setup'));
            process.exit(1);
        }

        await sync.connect();
        sync.startWatchingAllDomains();

        // Show status
        setInterval(() => {
            const status = sync.getStatus();
            if (status.activeUploads > 0 || Object.values(status.queueSizes).some(size => size > 0)) {
                console.log(chalk.blue(`Status: ${status.activeUploads} active uploads, Queue sizes: ${JSON.stringify(status.queueSizes)}`));
            }
        }, 30000); // Show status every 30 seconds if there's activity

    } catch (error) {
        console.error(chalk.red('Failed to start sync:'), error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = HostingerMultiDomainSync;

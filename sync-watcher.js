const chokidar = require('chokidar');
const { NodeSSH } = require('node-ssh');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

class HostingerSync {
    constructor() {
        this.config = this.loadConfig();
        this.ssh = new NodeSSH();
        this.isConnected = false;
        this.uploadQueue = new Set();
        this.isUploading = false;
    }

    loadConfig() {
        try {
            const config = JSON.parse(fs.readFileSync('sync-config.json', 'utf8'));
            return config;
        } catch (error) {
            console.error(chalk.red('Error loading config:'), error.message);
            process.exit(1);
        }
    }

    async connect() {
        try {
            console.log(chalk.blue('Connecting to Hostinger...'));
            
            await this.ssh.connect({
                host: this.config.hostinger.host,
                username: this.config.hostinger.username,
                port: this.config.hostinger.port,
                tryKeyboard: true,
                onKeyboardInteractive: (name, instructions, instructionsLang, prompts, finish) => {
                    if (prompts.length > 0 && prompts[0].prompt.toLowerCase().includes('password')) {
                        const inquirer = require('inquirer');
                        inquirer.prompt([{
                            type: 'password',
                            name: 'password',
                            message: 'Enter your SSH password:'
                        }]).then(answers => {
                            finish([answers.password]);
                        });
                    }
                }
            });
            
            this.isConnected = true;
            console.log(chalk.green('✓ Connected to Hostinger successfully!'));
            
        } catch (error) {
            console.error(chalk.red('Connection failed:'), error.message);
            console.log(chalk.yellow('Please check your credentials in sync-config.json'));
            process.exit(1);
        }
    }

    async uploadFile(localFilePath) {
        if (!this.isConnected) {
            await this.connect();
        }

        try {
            const relativePath = path.relative(this.config.hostinger.localPath, localFilePath);
            const remoteFilePath = path.posix.join(this.config.hostinger.remotePath, relativePath);
            
            console.log(chalk.cyan(`Uploading: ${relativePath}`));
            
            // Ensure remote directory exists
            const remoteDir = path.posix.dirname(remoteFilePath);
            await this.ssh.execCommand(`mkdir -p "${remoteDir}"`);
            
            // Upload file
            await this.ssh.putFile(localFilePath, remoteFilePath);
            
            console.log(chalk.green(`✓ Uploaded: ${relativePath}`));
            
        } catch (error) {
            console.error(chalk.red(`Error uploading ${localFilePath}:`), error.message);
        }
    }

    async processUploadQueue() {
        if (this.isUploading || this.uploadQueue.size === 0) {
            return;
        }

        this.isUploading = true;
        const filesToUpload = Array.from(this.uploadQueue);
        this.uploadQueue.clear();

        console.log(chalk.blue(`Processing ${filesToUpload.length} file(s)...`));

        for (const filePath of filesToUpload) {
            if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
                await this.uploadFile(filePath);
            }
        }

        this.isUploading = false;
        
        // Process any new files that were added during upload
        if (this.uploadQueue.size > 0) {
            setTimeout(() => this.processUploadQueue(), 1000);
        }
    }

    shouldIgnoreFile(filePath) {
        const relativePath = path.relative(this.config.hostinger.localPath, filePath);
        
        return this.config.hostinger.excludePatterns.some(pattern => {
            if (pattern.endsWith('/')) {
                return relativePath.includes(pattern.slice(0, -1));
            }
            return relativePath.includes(pattern) || relativePath.endsWith(pattern.replace('*', ''));
        });
    }

    startWatching() {
        if (!fs.existsSync(this.config.hostinger.localPath)) {
            console.error(chalk.red(`Local path does not exist: ${this.config.hostinger.localPath}`));
            console.log(chalk.yellow('Please run the download script first.'));
            return;
        }

        console.log(chalk.blue(`Watching for changes in: ${this.config.hostinger.localPath}`));
        
        const watcher = chokidar.watch(this.config.hostinger.localPath, {
            ignored: /(^|[\/\\])\../, // ignore dotfiles
            persistent: true,
            ignoreInitial: true
        });

        watcher
            .on('change', (filePath) => {
                if (!this.shouldIgnoreFile(filePath)) {
                    console.log(chalk.yellow(`File changed: ${path.relative(this.config.hostinger.localPath, filePath)}`));
                    this.uploadQueue.add(filePath);
                    setTimeout(() => this.processUploadQueue(), this.config.sync.syncInterval);
                }
            })
            .on('add', (filePath) => {
                if (!this.shouldIgnoreFile(filePath)) {
                    console.log(chalk.green(`File added: ${path.relative(this.config.hostinger.localPath, filePath)}`));
                    this.uploadQueue.add(filePath);
                    setTimeout(() => this.processUploadQueue(), this.config.sync.syncInterval);
                }
            })
            .on('unlink', (filePath) => {
                if (!this.shouldIgnoreFile(filePath)) {
                    console.log(chalk.red(`File removed: ${path.relative(this.config.hostinger.localPath, filePath)}`));
                    // Handle file deletion on remote server
                    this.deleteRemoteFile(filePath);
                }
            });

        console.log(chalk.green('✓ File watcher started. Press Ctrl+C to stop.'));
    }

    async deleteRemoteFile(localFilePath) {
        if (!this.isConnected) {
            await this.connect();
        }

        try {
            const relativePath = path.relative(this.config.hostinger.localPath, localFilePath);
            const remoteFilePath = path.posix.join(this.config.hostinger.remotePath, relativePath);
            
            await this.ssh.execCommand(`rm -f "${remoteFilePath}"`);
            console.log(chalk.red(`✓ Deleted remote file: ${relativePath}`));
            
        } catch (error) {
            console.error(chalk.red(`Error deleting remote file:`), error.message);
        }
    }

    async disconnect() {
        if (this.isConnected) {
            this.ssh.dispose();
            console.log(chalk.blue('Disconnected from Hostinger.'));
        }
    }
}

// Main execution
async function main() {
    const sync = new HostingerSync();
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
        console.log(chalk.yellow('\nShutting down...'));
        await sync.disconnect();
        process.exit(0);
    });

    try {
        await sync.connect();
        sync.startWatching();
    } catch (error) {
        console.error(chalk.red('Failed to start sync:'), error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = HostingerSync;

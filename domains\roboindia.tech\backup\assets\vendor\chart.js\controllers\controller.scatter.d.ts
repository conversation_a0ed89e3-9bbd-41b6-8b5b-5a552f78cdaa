export default class ScatterController extends DatasetController {
    static id: string;
    /**
     * @type {any}
     */
    static overrides: any;
    /**
       * @protected
       */
    protected getLabelAndValue(index: any): {
        label: any;
        value: string;
    };
    update(mode: any): void;
    /**
       * @protected
       */
    protected getMaxOverflow(): any;
}
import DatasetController from "../core/core.datasetController.js";

/**
 * @param {{ chart: Chart; scale: Scale; index: number; line: LineElement; }} source
 * @return {LineElement}
 */
export function _buildStackLine(source: {
    chart: Chart;
    scale: Scale;
    index: number;
    line: LineElement;
}): LineElement;
export type Chart = import('../../core/core.controller.js').default;
export type Scale = import('../../core/core.scale.js').default;
export type PointElement = import('../../elements/element.point.js').default;
import { LineElement } from "../../elements/index.js";

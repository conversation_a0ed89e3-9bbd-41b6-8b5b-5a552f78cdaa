#!/usr/bin/env node

const { NodeSSH } = require('node-ssh');
const fs = require('fs-extra');
const chalk = require('chalk');
const inquirer = require('inquirer');

async function manualDelete() {
    try {
        // Load config
        const config = JSON.parse(fs.readFileSync('sync-config.json', 'utf8'));
        
        console.log(chalk.cyan('=== Manual File Deletion ==='));
        
        // Connect to SSH
        const ssh = new NodeSSH();
        console.log(chalk.blue('Connecting to Hostinger...'));
        
        await ssh.connect({
            host: config.hostinger.host,
            username: config.hostinger.username,
            port: config.hostinger.port,
            password: config.hostinger.password
        });
        
        console.log(chalk.green('✓ Connected successfully!'));
        
        // Ask for domain and file path
        const answers = await inquirer.prompt([
            {
                type: 'list',
                name: 'domain',
                message: 'Select domain:',
                choices: Object.keys(config.domains)
            },
            {
                type: 'input',
                name: 'filePath',
                message: 'Enter file path to delete (relative to domain root):',
                default: 'do not upload here'
            }
        ]);
        
        const domainConfig = config.domains[answers.domain];
        const remoteFilePath = `${domainConfig.remotePath}/${answers.filePath}`;
        
        console.log(chalk.yellow(`Deleting: ${remoteFilePath}`));
        
        // Delete the file
        const result = await ssh.execCommand(`rm -f "${remoteFilePath}"`);
        
        if (result.code === 0) {
            console.log(chalk.green(`✓ Successfully deleted: ${answers.filePath}`));
        } else {
            console.log(chalk.red(`Error: ${result.stderr || 'File may not exist'}`));
        }
        
        // Also try to delete as directory if it's a folder
        const dirResult = await ssh.execCommand(`rmdir "${remoteFilePath}" 2>/dev/null || rm -rf "${remoteFilePath}"`);
        if (dirResult.code === 0) {
            console.log(chalk.green(`✓ Also removed as directory: ${answers.filePath}`));
        }
        
        ssh.dispose();
        console.log(chalk.blue('Disconnected from server.'));
        
    } catch (error) {
        console.error(chalk.red('Error:'), error.message);
    }
}

manualDelete();
